
	DO NOT ADD FIRMWARE TO THIS DIRECTORY.
	======================================

This directory is only here to contain firmware images extracted from old
device drivers which predate the common use of request_firmware().

As we update those drivers to use request_firmware() and keep a clean
separation between code and firmware, we put the extracted firmware
here.

This directory is _NOT_ for adding arbitrary new firmware images. The
place to add those is the separate linux-firmware repository:

    git://git.kernel.org/pub/scm/linux/kernel/git/firmware/linux-firmware.git

That repository contains all these firmware images which have been
extracted from older drivers, as well various new firmware images which
we were never permitted to include in a GPL'd work, but which we _have_
been permitted to redistribute under separate cover.

To submit firmware to that repository, please send either a git binary
diff or preferably a git pull request to:
      <PERSON> <<EMAIL>>
      <PERSON> <<EMAIL>>

Your commit should include an update to the WHENCE file clearly
identifying the licence under which the firmware is available, and
that it is redistributable. If the licence is long and involved, it's
permitted to include it in a separate file and refer to it from the
WHENCE file.

Ideally, your commit should contain a Signed-Off-By: from someone
authoritative on the licensing of the firmware in question (i.e. from
within the company that owns the code).
