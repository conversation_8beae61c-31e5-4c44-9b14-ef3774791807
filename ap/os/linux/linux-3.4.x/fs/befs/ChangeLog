Version 0.92 (2002-03-29)
==========
* Minor cleanup. <PERSON><PERSON> on the sources.

Version 0.92 (2002-03-27)
==========
* Fixed module makefile problem. It was not compiling all the correct 
    source files!
* Removed duplicated function definition
* Fixed potential null pointer dereference when reporting an error

Version 0.91 (2002-03-26)
==========
* Oy! Fixed stupid bug that would cause an unresolved symbol error.
	Thanks to <PERSON><PERSON><PERSON> for pointing this out to me.

Version 0.9 (2002-03-14)
==========
* Added Sergey <PERSON>'s patch to eliminate memcpy() overhead
	from b+tree operations. Changes the befs_read_datastream() interface.

* Segregated the functions that interface directly with the linux  vfs 
	interface into their own file called linuxvfs.c. [WD]

Version 0.64 (2002-02-07)
==========
* Did the string comparison really right this time (btree.c) [WD]

* Fixed up some places where I assumed that a long int could hold
	a pointer value. (btree.c) [WD]

* <PERSON> <and<PERSON><PERSON><PERSON>@uq.net.au> pointed out that the module
	wouldn't work on older (<2.4.10) kernels due to an unresolved symbol.
	This is bad, since 2.4.9 is still the current RedHat kernel. I added
	a workaround for this problem (compatibility.h) [WD]

* Sergey S. Kostyliov made befs_find_key() use a binary search to find 
	keys within btree nodes, rather than the linear search we were using 
	before. (btree.c) [Sergey S. Kostyliov <<EMAIL>>]

* Made a debian package of the source for use with kernel-package. [WD]


Version 0.63 (2002-01-31)
==========
* Fixed bug in befs_find_brun_indirect() that would result in the wrong
	block being read. It was introduced when adding byteswapping in 
	0.61. (datastream.c) [WD]

* Fixed a longstanding bug in befs_find_key() that would result in it 
	finding the first key that is a substring of the string it is searching
	for. For example, this would cause files in the same directory with 
	names like file1 and file2 to mysteriously be duplicates of each other 
	(because they have the same inode number). Many thanks to Pavel Roskin 
	for reporting this serious bug!!!
	(btree.c) [WD]

* Added support for long symlinks, after Axel Dorfler explained up how 
	they work. I had forgotten all about them. (inode.c, symlink.c) [WD]

* Documentation improvements in source. [WD]

* Makefile fix for independent module when CONFIG_MODVERSION is set in 
	kernel config [Pavel Roskin <<EMAIL>>]

* Compile warning fix for namei.c. [Sergey S. Kostyliov <<EMAIL>>]


Version 0.62
==========
* Fixed makefile for module install [WD]


Version 0.61 (2002-01-20)
==========
* Made functions in endian.h to do the correct byteswapping, no matter
	the arch. [WD]

* Abbandoned silly checks for a NULL superblock pointer in debug.c. [WD]

* Misc code cleanups. Also cleanup of this changelog file. [WD]

* Added byteswapping to all metadata reads from disk.
	Uses the functions from endian.h [WD]

* Remove the typedef of struct super_block to vfs_sb, as it offended
	certain peoples' aesthetic sense. [WD]

* Ditto with the befs_read_block() interface. [WD]
 

Version 0.6 (2001-12-15)
==========
* Cleanup of NLS functions (util.c) [WD]

* Make directory lookup/read use the NLS if an iocharset is provided. [WD]

* Fixed stupid bug where specifying the uid or gid mount options as '0' 
	would result in the filesystem using the on-disk uid and gid. [WD]

* Added mount option to control debug printing. 
	The option is, simply enough, 'debug'. 
	(super.c, debug.c) [WD]

* Removed notion of btree handle from btree.c. It was unnecessary, as the
	linux VFS doesn't allow us to keep any state between calls. Updated 
	dir.c, namei.c befs_fs.h to account for it. [WD]

* Improved handleing of overflow nodes when listing directories. 
	Now works for overflow nodes hanging off of nodes other than the root 
	node. This is the cleaner solution to Brent Miszalaski's problem. [WD]

* Added new debug/warning/error print functions in debug.c. 
	More flexible. Will soon be controllable at mount time 
	(see TODO). [WD]

* Rewrote datastream position lookups.
	(datastream.c) [WD]

* Moved the TODO list to its own file.


Version 0.50 (2001-11-13)
==========
* Added workaround for mis-understanding of the nature of the b+trees used 
	in directories. A cleaner solution will come after I've thought about it 
	for a while. Thanks to Brent Miszalaski for finding and reporting this bug. 
	(btree.c) [WD]

* Minor cleanups

* Added test for "impossible" condition of empty internal nodes in 
	seekleaf() in btree.c [WD]

* Implemented the abstracted read_block() in io.c [WD]

* Cleaned up the inode validation in inode.c [WD]

* Anton Altaparmakov figured out (by asking Linus :) ) what was causing the 
 	hanging disk io problem. It turns out you need to have the sync_pages 
	callback defined in your address_space_ops, even if it just uses the 
	default linux-supplied implementation. Fixed. Works now.
	(file.c) [WD]

* Anton Altaparmakov and Christoph Hellwig alerted me to the fact that 
	filesystem code should be using GFP_NOFS instead of GFP_KERNEL as the 
	priority parameter to kmalloc(). Fixed. 
	(datastream.c, btree.c super.c inode.c) [WD]

* Anton also told me that the blocksize is not allowed to be larger than 
	the page size in linux, which is 4k i386. Oops. Added a test for 
	(blocksize > PAGE_SIZE), and refuse to mount in that case. What this 
	practically means is that 8k blocksize volumes won't work without a major
	restructuring of the driver (or an alpha or other 64bit hardware). [WD]

* Cleaned up the befs_count_blocks() function. Much smarter now. 
	And somewhat smaller too. [WD]

* Made inode allocations use a slab cache 
	(super.c inode.c) [WD]

* Moved the freeing of the private inode section from put_inode() to 
	clear_inode(). This fixes a potential free twice type bug. Put_inode() 
	can be called multiple times for each inode struct. [WD]

* Converted all non vfs-callback functions to use befs_sb_info as the 
	superblock type, rather than struct super_block. This is for 
	portablity. [WD]

* Fixed a couple of compile warnings due to use of malloc.h, when slab.h 
	is the new way. (inode.c, super.c) [WD]

* Fixed erronous includes of linux/befs_fs_i.h and linux/befs_fs_sb.h 
	in inode.c [WD]

Version 0.45 (2001-10-29)
==========
* Added functions to get the private superblock and inode structures from 
	their enclosing public structures. Switched all references to the 
	private portions to use them. (many files) [WD]

* Made read_super and read_inode allocate the private portions of those 
	structures into the generic pointer fields of the public structures 
	with kmalloc(). put_super and put_inode free them. This allows us not 
	to have to touch the definitions of the public structures in 
	include/linux/fs.h. Also, befs_inode_info is huge (because of the 
	symlink string). (super.c, inode.c, befs_fs.h) [WD]

* Fixed a thinko that was corrupting file reads after the first block_run 
	is done being read. (datastream.c) [WD]

* Removed fsync() hooks, since a read-only filesystem doesn't need them. 
	[Christoph Hellwig].

* Fixed befs_readlink() (symlink.c) [Christoph Hellwig].

* Removed all the Read-Write stuff. I'll redo it when it is time to add 
	write support (various files) [WD].

* Removed prototypes for functions who's definitions have been removed 
	(befs_fs.h) [WD].


Version 0.4 (2001-10-28)
==========
* Made it an option to use the old non-pagecache befs_file_read() for 
	testing purposes. (fs/Config.in)

* Fixed unused variable warnings when compiling without debugging.

* Fixed a bug where the inode and super_block didn't get their blockbits 
	fields set (inode.c and super.c). 

* Release patch version 11. AKA befs-driver version 0.4.

* Thats right. New versioning scheme. 
	I've done some serious testing on it now (on my box anyhow), and it 
	seems stable and not outragously slow. Existing features are more-or-less 
	correct (see TODO list). But it isn't 1.0 yet. I think 0.4 gives me some 
	headroom before the big 1.0.


2001-10-26
==========
* Fixed date format in this file. Was I smoking crack?

* Removed old datastream code from file.c, since it is nolonger used.

* Generic_read_file() is now used to read regular file data. 
	It doesn't chew up the buffer cache (it does page io instead), and seems 
	to be about as fast (even though it has to look up each file block 
	indivdualy). And it knows about doing readahead, which is a major plus. 
	So it does i/o in much larger chunks. It is the correct linux way. It 
	uses befs_get_block() by way of befs_readpage() to find the disk offsets 
	of blocks, which in turn calls befs_fpos2brun() in datastream.c to do 
	the hard work of finding the disk block number.

* Changed method of checking for a dirty filesystem in befs_read_super 
	(super.c). Now we check to see if log_start and log_end differ. If so, 
	the journal needs to be replayed, and the filesystem cannot be mounted.

* Fixed an extra instance of MOD_DEC_USE_COUNT in super.c

* Fixed a problem with reading the superblock on devices with large sector 
	sizes (such as cdroms) on linux 2.4.10 and up.

2001-10-24
==========
* Fix nasty bug in converting block numbers to struct befs_inode_addr. 
	Subtle, because the old version was only sometimes wrong. 
	Probably responsible for lots of problems. (inode.c)

* Fix bug with reading an empty directory. (btree.c and dir.c)

* This one looks good. Release patch version 10

2001-10-23
==========
* Added btree searching function.

* Use befs_btree_find in befs_lookup (namei.c)

* Additional comments in btree.c

2001-10-22
==========
* Added B+tree reading functions (in btree.c). 
	Made befs_readdir() use them them instead of the cruft in index.c.

2001-09-11
==========
* Converted befs_read_file() to use the new datastream code.

* Finally updated the README file.

* Added many comments.

* Posted version 6

* Removed byte-order conversion code. 
	I have no intention of supporting it, and it was very ugly. 
	Flow control with #ifdef (ugh). Maybe I'll redo it once 
	native byteorder works 100%.

2001-09-10
==========
* Finished implementing read_datastream()

* made befs_read_brun() more general
	Supports an offset to start at and a max bytes to read
	Added a wrapper function to give the old call

2001-09-30
==========
* Discovered that the datastream handleing code in file.c is quite deficient 
	in several respects. For one thing, it doesn't deal with indirect blocks

* Rewrote datastream handleing.

* Created io.c, for io related functions.
	Previously, the befs_bread() funtions lived in file.c
	Created the befs_read_brun() function.


2001-09-07
==========
* Made a function to actually count the number of fs blocks used by a file.
	And helper functions.
	(fs/befs/inode.c)

2001-09-05
==========
* Fixed a misunderstanding of the inode fields. 
	This fixed the problmem with wrong file sizes from du and others.
	The i_blocks field of the inode struct is not the number of blocks for the
	inode, it is the number of blocks for the file.	Also, i_blksize is not
	necessarily the size of the inode, although in  practice it works out.
	Changed to blocksize of filesystem.
	(fs/befs/inode.c)

* Permanently removed code that had been provisionally ifdefed out of befs_fs.h

* Since we don't support access time, make that field zero, instead of 
	copying m_time.
	(fs/befs/inode.c)

* Added sanity check for inode reading
	Make sure inode we got was the one we asked for. 
	(fs/befs/inode.c)

* Code cleanup
	Local pointers to commonly used structures in inode.c.
	Got rid of abominations befs_iaddr2inode() and befs_inode2ino(). 
	Replaced with single function iaddr2blockno().
	(fs/befs/super.c) (fs/befs/inode.c)

2001-09-01
==========
* Fixed the problem with statfs where it would always claim the disk was 
	half full, due to improper understanding of the statfs fields.
	(fs/befs/super.c)

* Posted verion 4 of the patch

2001-09-01
==========
* Changed the macros in befs_fs.h to inline functions.
	More readable. Typesafe. Better
	(include/linux/befs_fs.h)

* Moved type definitions from befs_fs.h to a new file, befs_fs_types.h 
	Because befs_fs_i.h and befs_fs_sb.h were including befs_fs.h for the 
	typedefs, and they are inlcuded in <linux/fs.h>, which has definitions 
	that I want the inline functions in befs_fs.h to be able to see. Nasty
	circularity.
	(include/linux/befs_fs.h)

2001-08-30
==========
* Cleaned up some wording.

* Added additional consitency checks on mount
	Check block_size agrees with block_shift
	Check flags == BEFS_CLEAN
	(fs/befs/super.c)

* Tell the kernel to only mount befs read-only. 
	By setting the MS_RDONLY flag in befs_read_super().
	Not that it was possible to write before. But now the kernel won't even try.
	(fs/befs/super.c)

* Got rid of kernel warning on mount.
	The kernel doesn't like it if you call set_blocksize() on a device when 
	you have some of its blocks open. Moved the second set_blocksize() to the
	very end of befs_read_super(), after we are done with the disk superblock.
	(fs/befs/super.c)
	
* Fixed wrong number of args bug in befs_dump_inode
	(fs/befs/debug.c)

* Solved lots of type mismatches in kprint()s
	(everwhere)

2001-08-27
==========
* Cleaned up the fs/Config.in entries a bit, now slightly more descriptive.

* BeFS depends on NLS, so I made activating BeFS enable the NLS questions
	(fs/nls/Config.in)

* Added Configure.help entries for CONFIG_BEFS_FS and CONFIG_DEBUG_BEFS
	(Documentation/Configure.help)

2001-08-??
==========
* Removed superblock locking calls in befs_read_super(). In 2.4, the VFS 
	hands us a super_block struct that is already locked.

2001-08-13
==========
* Will Dyson <<EMAIL>> is now attempting to maintain this module
	Makoto Kato <<EMAIL>> is original author.Daniel Berlin 
	also did some work on it (fixing it up for the later 2.3.x kernels, IIRC).

* Fixed compile errors on 2.4.1 kernel (WD)
	Resolve rejected patches
	Accommodate changed NLS interface (util.h)
	Needed to include <linux/slab.h> in most files
	Makefile changes
	fs/Config.in changes

* Tried to niceify the code using the ext2 fs as a guide
	Declare befs_fs_type using the DECLARE_FSTYPE_DEV() macro

* Made it a configure option to turn on debugging (fs/Config.in)

* Compiles on 2.4.7
