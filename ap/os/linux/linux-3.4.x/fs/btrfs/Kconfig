config BTRFS_FS
	tristate "Btrfs filesystem (EXPERIMENTAL) Unstable disk format"
	depends on EXPERIMENTAL
	select LIBCRC32C
	select ZLIB_INFLATE
	select ZLIB_DEFLATE
	select LZO_COMPRESS
	select LZO_DECOMPRESS
	help
	  Btrfs is a new filesystem with extents, writable snapshotting,
	  support for multiple devices and many more features.

	  Btrfs is highly experimental, and THE DISK FORMAT IS NOT YET
	  FINALIZED.  You should say N here unless you are interested in
	  testing Btrfs with non-critical data.

	  To compile this file system support as a module, choose M here. The
	  module will be called btrfs.

	  If unsure, say N.

config BTRFS_FS_POSIX_ACL
	bool "Btrfs POSIX Access Control Lists"
	depends on BTRFS_FS
	select FS_POSIX_ACL
	help
	  POSIX Access Control Lists (ACLs) support permissions for users and
	  groups beyond the owner/group/world scheme.

	  To learn more about Access Control Lists, visit the POSIX ACLs for
	  Linux website <http://acl.bestbits.at/>.

	  If you don't know what Access Control Lists are, say N

config BTRFS_FS_CHECK_INTEGRITY
	bool "Btrfs with integrity check tool compiled in (DANGEROUS)"
	depends on BTRFS_FS
	help
	  Adds code that examines all block write requests (including
	  writes of the super block). The goal is to verify that the
	  state of the filesystem on disk is always consistent, i.e.,
	  after a power-loss or kernel panic event the filesystem is
	  in a consistent state.

	  If the integrity check tool is included and activated in
	  the mount options, plenty of kernel memory is used, and
	  plenty of additional CPU cycles are spent. Enabling this
	  functionality is not intended for normal use.

	  In most cases, unless you are a btrfs developer who needs
	  to verify the integrity of (super)-block write requests
	  during the run of a regression test, say N
