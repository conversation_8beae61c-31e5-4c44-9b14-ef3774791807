
config CACHEFILES
	tristate "Filesystem caching on files"
	depends on FSCACHE && BLOCK
	help
	  This permits use of a mounted filesystem as a cache for other
	  filesystems - primarily networking filesystems - thus allowing fast
	  local disk to enhance the speed of slower devices.

	  See Documentation/filesystems/caching/cachefiles.txt for more
	  information.

config CACHEFILES_DEBUG
	bool "Debug CacheFiles"
	depends on CACHEFILES
	help
	  This permits debugging to be dynamically enabled in the filesystem
	  caching on files module.  If this is set, the debugging output may be
	  enabled by setting bits in /sys/modules/cachefiles/parameter/debug or
	  by including a debugging specifier in /etc/cachefilesd.conf.

config CACHEFILES_HISTOGRAM
	bool "Gather latency information on CacheFiles"
	depends on CACHEFILES && PROC_FS
	help

	  This option causes latency information to be gathered on CacheFiles
	  operation and exported through file:

		/proc/fs/cachefiles/histogram

	  The generation of this histogram adds a certain amount of overhead to
	  execution as there are a number of points at which data is gathered,
	  and on a multi-CPU system these may be on cachelines that keep
	  bouncing between CPUs.  On the other hand, the histogram may be
	  useful for debugging purposes.  Saying 'N' here is recommended.

	  See Documentation/filesystems/caching/cachefiles.txt for more
	  information.
