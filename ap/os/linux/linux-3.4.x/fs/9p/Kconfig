config 9P_FS
	tristate "Plan 9 Resource Sharing Support (9P2000)"
	depends on INET && NET_9P
	help
	  If you say Y here, you will get experimental support for
	  Plan 9 resource sharing via the 9P2000 protocol.

	  See <http://v9fs.sf.net> for more information.

	  If unsure, say N.

if 9P_FS
config 9P_FSCACHE
	bool "Enable 9P client caching support (EXPERIMENTAL)"
	depends on EXPERIMENTAL
	depends on 9P_FS=m && FSCACHE || 9P_FS=y && FSCACHE=y
	help
	  Choose Y here to enable persistent, read-only local
	  caching support for 9p clients using FS-Cache


config 9P_FS_POSIX_ACL
	bool "9P POSIX Access Control Lists"
	select FS_POSIX_ACL
	help
	  POSIX Access Control Lists (ACLs) support permissions for users and
	  groups beyond the owner/group/world scheme.

	  To learn more about Access Control Lists, visit the POSIX ACLs for
	  Linux website <http://acl.bestbits.at/>.

	  If you don't know what Access Control Lists are, say N

endif
