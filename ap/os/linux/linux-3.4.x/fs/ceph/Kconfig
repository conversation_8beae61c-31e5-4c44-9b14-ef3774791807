config CEPH_FS
        tristate "Ceph distributed file system (EXPERIMENTAL)"
	depends on INET && EXPERIMENTAL
	select CEPH_LIB
	select LIBCRC32C
	select CRYPTO_AES
	select CRYPTO
	default n
	help
	  Choose Y or M here to include support for mounting the
	  experimental Ceph distributed file system.  Ceph is an extremely
	  scalable file system designed to provide high performance,
	  reliable access to petabytes of storage.

	  More information at http://ceph.newdream.net/.

	  If unsure, say N.

