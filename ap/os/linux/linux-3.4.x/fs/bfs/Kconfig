config BFS_FS
	tristate "BFS file system support (EXPERIMENTAL)"
	depends on BLOCK && EXPERIMENTAL
	help
	  Boot File System (BFS) is a file system used under SCO UnixWare to
	  allow the bootloader access to the kernel image and other important
	  files during the boot process.  It is usually mounted under /stand
	  and corresponds to the slice marked as "STAND" in the UnixWare
	  partition.  You should say Y if you want to read or write the files
	  on your /stand slice from within Linux.  You then also need to say Y
	  to "UnixWare slices support", below.  More information about the BFS
	  file system is contained in the file
	  <file:Documentation/filesystems/bfs.txt>.

	  If you don't know what this is about, say N.

	  To compile this as a module, choose M here: the module will be called
	  bfs.  Note that the file system of your root partition (the one
	  containing the directory /) cannot be compiled as a module.
