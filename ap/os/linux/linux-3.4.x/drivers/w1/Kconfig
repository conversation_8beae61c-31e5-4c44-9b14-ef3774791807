menuconfig W1
	tristate "Dallas's 1-wire support"
	depends on HAS_IOMEM
	---help---
	  Dallas' 1-wire bus is useful to connect slow 1-pin devices
	  such as iButtons and thermal sensors.

	  If you want W1 support, you should say Y here.

	  This W1 support can also be built as a module.  If so, the module
	  will be called wire.

if W1

config W1_CON
	depends on CONNECTOR
	bool "Userspace communication over connector"
	default y
	--- help ---
	  This allows to communicate with userspace using connector. For more
	  information see <file:Documentation/connector/connector.txt>.
	  There are three types of messages between w1 core and userspace:
	  1. Events. They are generated each time new master or slave device found
		either due to automatic or requested search.
	  2. Userspace commands. Includes read/write and search/alarm search commands.
	  3. Replies to userspace commands.

source drivers/w1/masters/Kconfig
source drivers/w1/slaves/Kconfig

endif # W1
