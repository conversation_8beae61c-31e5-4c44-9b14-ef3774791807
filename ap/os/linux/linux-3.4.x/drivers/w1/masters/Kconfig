#
# 1-wire bus master configuration
#

menu "1-wire Bus Masters"

config W1_MASTER_MATROX
	tristate "Matrox G400 transport layer for 1-wire"
	depends on PCI
	help
	  Say Y here if you want to communicate with your 1-wire devices
	  using Matrox's G400 GPIO pins.

	  This support is also available as a module.  If so, the module
	  will be called matrox_w1.

config W1_MASTER_DS2490
	tristate "DS2490 USB <-> W1 transport layer for 1-wire"
  	depends on USB
  	help
	  Say Y here if you want to have a driver for DS2490 based USB <-> W1 bridges,
	  for example DS9490*.

  	  This support is also available as a module.  If so, the module
	  will be called ds2490.

config W1_MASTER_DS2482
	tristate "Maxim DS2482 I2C to 1-Wire bridge"
	depends on I2C && EXPERIMENTAL
	help
	  If you say yes here you get support for the Maxim DS2482
	  I2C to 1-Wire bridge.

	  This driver can also be built as a module.  If so, the module
	  will be called ds2482.

config W1_MASTER_MXC
	tristate "Freescale MXC 1-wire busmaster"
	depends on W1 && ARCH_MXC
	help
	  Say Y here to enable MXC 1-wire host

config W1_MASTER_DS1WM
	tristate "Maxim DS1WM 1-wire busmaster"
	depends on W1 && GENERIC_HARDIRQS
	help
	  Say Y here to enable the DS1WM 1-wire driver, such as that
	  in HP iPAQ devices like h5xxx, h2200, and ASIC3-based like
	  hx4700.

config W1_MASTER_GPIO
	tristate "GPIO 1-wire busmaster"
	depends on GENERIC_GPIO
	help
	  Say Y here if you want to communicate with your 1-wire devices using
	  GPIO pins. This driver uses the GPIO API to control the wire.

	  This support is also available as a module.  If so, the module
	  will be called w1-gpio.

config HDQ_MASTER_OMAP
	tristate "OMAP HDQ driver"
	depends on SOC_OMAP2430 || ARCH_OMAP3
	help
	  Say Y here if you want support for the 1-wire or HDQ Interface
	  on an OMAP processor.

endmenu

