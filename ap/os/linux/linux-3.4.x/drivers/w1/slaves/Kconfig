#
# 1-wire slaves configuration
#

menu "1-wire Slaves"

config W1_SLAVE_THERM
	tristate "Thermal family implementation"
	help
	  Say Y here if you want to connect 1-wire thermal sensors to your
	  wire.

config W1_SLAVE_SMEM
	tristate "Simple 64bit memory family implementation"
	help
	  Say Y here if you want to connect 1-wire
	  simple 64bit memory rom(ds2401/ds2411/ds1990*) to your wire.

config W1_SLAVE_DS2408
        tristate "8-Channel Addressable Switch (IO Expander) 0x29 family support (DS2408)"
        help
          Say Y here if you want to use a 1-wire

		  DS2408 8-Channel Addressable Switch device support

config W1_SLAVE_DS2423
	tristate "Counter 1-wire device (DS2423)"
	select CRC16
	help
	  If you enable this you can read the counter values available
	  in the DS2423 chipset from the w1_slave file under the
	  sys file system.

	  Say Y here if you want to use a 1-wire
	  counter family device (DS2423).

config W1_SLAVE_DS2431
	tristate "1kb EEPROM family support (DS2431)"
	help
	  Say Y here if you want to use a 1-wire
	  1kb EEPROM family device (DS2431)

config W1_SLAVE_DS2433
	tristate "4kb EEPROM family support (DS2433)"
	help
	  Say Y here if you want to use a 1-wire
	  4kb EEPROM family device (DS2433).

config W1_SLAVE_DS2433_CRC
	bool "Protect DS2433 data with a CRC16"
	depends on W1_SLAVE_DS2433
	select CRC16
	help
	  Say Y here to protect DS2433 data with a CRC16.
	  Each block has 30 bytes of data and a two byte CRC16.
	  Full block writes are only allowed if the CRC is valid.

config W1_SLAVE_DS2760
	tristate "Dallas 2760 battery monitor chip (HP iPAQ & others)"
	depends on W1
	help
	  If you enable this you will have the DS2760 battery monitor
	  chip support.

	  The battery monitor chip is used in many batteries/devices
	  as the one who is responsible for charging/discharging/monitoring
	  Li+ batteries.

	  If you are unsure, say N.

config W1_SLAVE_DS2780
	tristate "Dallas 2780 battery monitor chip"
	depends on W1
	help
	  If you enable this you will have the DS2780 battery monitor
	  chip support.

	  The battery monitor chip is used in many batteries/devices
	  as the one who is responsible for charging/discharging/monitoring
	  Li+ batteries.

	  If you are unsure, say N.

config W1_SLAVE_DS2781
	tristate "Dallas 2781 battery monitor chip"
	depends on W1
	help
	  If you enable this you will have the DS2781 battery monitor
	  chip support.

	  The battery monitor chip is used in many batteries/devices
	  as the one who is responsible for charging/discharging/monitoring
	  Li+ batteries.

	  If you are unsure, say N.

config W1_SLAVE_BQ27000
	tristate "BQ27000 slave support"
	depends on W1
	help
	  Say Y here if you want to use a hdq
	  bq27000 slave support.

endmenu
