#
# Virtualization support drivers
#

menuconfig VIRT_DRIVERS
	bool "Virtualization drivers"
	---help---
	  Say Y here to get to see options for device drivers that support
	  virtualization environments.

	  If you say N, all options in this submenu will be skipped and disabled.

if VIRT_DRIVERS

config FSL_HV_MANAGER
	tristate "Freescale hypervisor management driver"
	depends on FSL_SOC
	help
          The Freescale hypervisor management driver provides several services
	  to drivers and applications related to the Freescale hypervisor:

          1) An ioctl interface for querying and managing partitions.

          2) A file interface to reading incoming doorbells.

          3) An interrupt handler for shutting down the partition upon
	     receiving the shutdown doorbell from a manager partition.

          4) A kernel interface for receiving callbacks when a managed
	     partition shuts down.

endif
