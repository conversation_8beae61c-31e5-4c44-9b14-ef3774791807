/*
 *	watchdog_core.h
 *
 *	(c) Copyright 2008-2011 <PERSON> <<EMAIL>>,
 *						All Rights Reserved.
 *
 *	(c) Copyright 2008-2011 W<PERSON> <<EMAIL>>.
 *
 *	This source code is part of the generic code that can be used
 *	by all the watchdog timer drivers.
 *
 *	Based on source code of the following authors:
 *	  <PERSON> <<PERSON>_<PERSON>@dell.com>,
 *	  <PERSON> <<EMAIL>>,
 *	  <PERSON> <<EMAIL>>
 *	  <PERSON><PERSON><PERSON> <<EMAIL>>
 *	  <PERSON> <<EMAIL>>
 *
 *	This program is free software; you can redistribute it and/or
 *	modify it under the terms of the GNU General Public License
 *	as published by the Free Software Foundation; either version
 *	2 of the License, or (at your option) any later version.
 *
 *	Neither <PERSON>, CymruNet Ltd., Wim <PERSON> nor Iguana vzw.
 *	admit liability nor provide warranty for any of this software.
 *	This material is provided "AS-IS" and at no charge.
 */

/*
 *	Functions/procedures to be called by the core
 */
int watchdog_dev_register(struct watchdog_device *);
int watchdog_dev_unregister(struct watchdog_device *);
