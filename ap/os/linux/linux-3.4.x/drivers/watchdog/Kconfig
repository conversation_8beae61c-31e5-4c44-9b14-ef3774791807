#
# Watchdog device configuration
#

menuconfig WATCHDOG
	bool "Watchdog Timer Support"
	---help---
	  If you say Y here (and to one of the following options) and create a
	  character special file /dev/watchdog with major number 10 and minor
	  number 130 using mknod ("man mknod"), you will get a watchdog, i.e.:
	  subsequently opening the file and then failing to write to it for
	  longer than 1 minute will result in rebooting the machine. This
	  could be useful for a networked machine that needs to come back
	  on-line as fast as possible after a lock-up. There's both a watchdog
	  implementation entirely in software (which can sometimes fail to
	  reboot the machine) and a driver for hardware watchdog boards, which
	  are more robust and can also keep track of the temperature inside
	  your computer. For details, read
	  <file:Documentation/watchdog/watchdog-api.txt> in the kernel source.

	  The watchdog is usually used together with the watchdog daemon
	  which is available from
	  <ftp://ibiblio.org/pub/Linux/system/daemons/watchdog/>. This daemon can
	  also monitor NFS connections and can reboot the machine when the process
	  table is full.

	  If unsure, say N.

if WATCHDOG

config WATCHDOG_CORE
	bool "WatchDog Timer Driver Core"
	---help---
	  Say Y here if you want to use the new watchdog timer driver core.
	  This driver provides a framework for all watchdog timer drivers
	  and gives them the /dev/watchdog interface (and later also the
	  sysfs interface).

config WATCHDOG_NOWAYOUT
	bool "Disable watchdog shutdown on close"
	help
	  The default watchdog behaviour (which you get if you say N here) is
	  to stop the timer if the process managing it closes the file
	  /dev/watchdog. It's always remotely possible that this process might
	  get killed. If you say Y here, the watchdog cannot be stopped once
	  it has been started.

#
# General Watchdog drivers
#

comment "Watchdog Device Drivers"

# Architecture Independent

config SOFT_WATCHDOG
	tristate "Software watchdog"
	select WATCHDOG_CORE
	help
	  A software monitoring watchdog. This will fail to reboot your system
	  from some situations that the hardware watchdog will recover
	  from. Equally it's a lot cheaper to install.

	  To compile this driver as a module, choose M here: the
	  module will be called softdog.

config WM831X_WATCHDOG
	tristate "WM831x watchdog"
	depends on MFD_WM831X
	select WATCHDOG_CORE
	help
	  Support for the watchdog in the WM831x AudioPlus PMICs.  When
	  the watchdog triggers the system will be reset.

config WM8350_WATCHDOG
	tristate "WM8350 watchdog"
	depends on MFD_WM8350
	select WATCHDOG_CORE
	help
	  Support for the watchdog in the WM8350 AudioPlus PMIC.  When
	  the watchdog triggers the system will be reset.

# ALPHA Architecture

# ARM Architecture

config ARM_SP805_WATCHDOG
	tristate "ARM SP805 Watchdog"
	depends on ARM_AMBA
	help
	  ARM Primecell SP805 Watchdog timer. This will reboot your system when
	  the timeout is reached.

config AT91RM9200_WATCHDOG
	tristate "AT91RM9200 watchdog"
	depends on ARCH_AT91RM9200
	help
	  Watchdog timer embedded into AT91RM9200 chips. This will reboot your
	  system when the timeout is reached.

config AT91SAM9X_WATCHDOG
	tristate "AT91SAM9X / AT91CAP9 watchdog"
	depends on ARCH_AT91 && !ARCH_AT91RM9200
	help
	  Watchdog timer embedded into AT91SAM9X and AT91CAP9 chips. This will
	  reboot your system when the timeout is reached.

config 21285_WATCHDOG
	tristate "DC21285 watchdog"
	depends on FOOTBRIDGE
	help
	  The Intel Footbridge chip contains a built-in watchdog circuit. Say Y
	  here if you wish to use this. Alternatively say M to compile the
	  driver as a module, which will be called wdt285.

	  This driver does not work on all machines. In particular, early CATS
	  boards have hardware problems that will cause the machine to simply
	  lock up if the watchdog fires.

	  "If in doubt, leave it out" - say N.

config 977_WATCHDOG
	tristate "NetWinder WB83C977 watchdog"
	depends on FOOTBRIDGE && ARCH_NETWINDER
	help
	  Say Y here to include support for the WB977 watchdog included in
	  NetWinder machines. Alternatively say M to compile the driver as
	  a module, which will be called wdt977.

	  Not sure? It's safe to say N.

config IXP2000_WATCHDOG
	tristate "IXP2000 Watchdog"
	depends on ARCH_IXP2000
	help
	  Say Y here if to include support for the watchdog timer
	  in the Intel IXP2000(2400, 2800, 2850) network processors.
	  This driver can be built as a module by choosing M. The module
	  will be called ixp2000_wdt.

	  Say N if you are unsure.

config IXP4XX_WATCHDOG
	tristate "IXP4xx Watchdog"
	depends on ARCH_IXP4XX
	help
	  Say Y here if to include support for the watchdog timer
	  in the Intel IXP4xx network processors. This driver can
	  be built as a module by choosing M. The module will
	  be called ixp4xx_wdt.

	  Note: The internal IXP4xx watchdog does a soft CPU reset
	  which doesn't reset any peripherals. There are circumstances
	  where the watchdog will fail to reset the board correctly
	  (e.g., if the boot ROM is in an unreadable state).

	  Say N if you are unsure.

config KS8695_WATCHDOG
	tristate "KS8695 watchdog"
	depends on ARCH_KS8695
	help
	  Watchdog timer embedded into KS8695 processor. This will reboot your
	  system when the timeout is reached.

config HAVE_S3C2410_WATCHDOG
	bool
	help
	  This will include watchdog timer support for Samsung SoCs. If
	  you want to include watchdog support for any machine, kindly
	  select this in the respective mach-XXXX/Kconfig file.

config S3C2410_WATCHDOG
	tristate "S3C2410 Watchdog"
	depends on HAVE_S3C2410_WATCHDOG
	select WATCHDOG_CORE
	help
	  Watchdog timer block in the Samsung SoCs. This will reboot
	  the system when the timer expires with the watchdog enabled.

	  The driver is limited by the speed of the system's PCLK
	  signal, so with reasonably fast systems (PCLK around 50-66MHz)
	  then watchdog intervals of over approximately 20seconds are
	  unavailable.

	  The driver can be built as a module by choosing M, and will
	  be called s3c2410_wdt

config SA1100_WATCHDOG
	tristate "SA1100/PXA2xx watchdog"
	depends on ARCH_SA1100 || ARCH_PXA
	help
	  Watchdog timer embedded into SA11x0 and PXA2xx chips. This will
	  reboot your system when timeout is reached.

	  NOTE: once enabled, this timer cannot be disabled.

	  To compile this driver as a module, choose M here: the
	  module will be called sa1100_wdt.

config DW_WATCHDOG
	tristate "Synopsys DesignWare watchdog"
	depends on ARM && HAVE_CLK
	help
	  Say Y here if to include support for the Synopsys DesignWare
	  watchdog timer found in many ARM chips.
	  To compile this driver as a module, choose M here: the
	  module will be called dw_wdt.

config MPCORE_WATCHDOG
	tristate "MPcore watchdog"
	depends on HAVE_ARM_TWD
	help
	  Watchdog timer embedded into the MPcore system.

	  To compile this driver as a module, choose M here: the
	  module will be called mpcore_wdt.

config EP93XX_WATCHDOG
	tristate "EP93xx Watchdog"
	depends on ARCH_EP93XX
	select WATCHDOG_CORE
	help
	  Say Y here if to include support for the watchdog timer
	  embedded in the Cirrus Logic EP93xx family of devices.

	  To compile this driver as a module, choose M here: the
	  module will be called ep93xx_wdt.

config OMAP_WATCHDOG
	tristate "OMAP Watchdog"
	depends on ARCH_OMAP16XX || ARCH_OMAP2PLUS
	help
	  Support for TI OMAP1610/OMAP1710/OMAP2420/OMAP3430/OMAP4430 watchdog.  Say 'Y'
	  here to enable the OMAP1610/OMAP1710/OMAP2420/OMAP3430/OMAP4430 watchdog timer.

config PNX4008_WATCHDOG
	tristate "PNX4008 and LPC32XX Watchdog"
	depends on ARCH_PNX4008 || ARCH_LPC32XX
	select WATCHDOG_CORE
	help
	  Say Y here if to include support for the watchdog timer
	  in the PNX4008 or LPC32XX processor.
	  This driver can be built as a module by choosing M. The module
	  will be called pnx4008_wdt.

	  Say N if you are unsure.

config IOP_WATCHDOG
	tristate "IOP Watchdog"
	depends on PLAT_IOP
	select WATCHDOG_NOWAYOUT if (ARCH_IOP32X || ARCH_IOP33X)
	help
	  Say Y here if to include support for the watchdog timer
	  in the Intel IOP3XX & IOP13XX I/O Processors.  This driver can
	  be built as a module by choosing M. The module will
	  be called iop_wdt.

	  Note: The IOP13XX watchdog does an Internal Bus Reset which will
	  affect both cores and the peripherals of the IOP.  The ATU-X
	  and/or ATUe configuration registers will remain intact, but if
	  operating as an Root Complex and/or Central Resource, the PCI-X
	  and/or PCIe busses will also be reset.  THIS IS A VERY BIG HAMMER.

config DAVINCI_WATCHDOG
	tristate "DaVinci watchdog"
	depends on ARCH_DAVINCI
	help
	  Say Y here if to include support for the watchdog timer
	  in the DaVinci DM644x/DM646x processors.
	  To compile this driver as a module, choose M here: the
	  module will be called davinci_wdt.

	  NOTE: once enabled, this timer cannot be disabled.
	  Say N if you are unsure.

config ORION_WATCHDOG
	tristate "Orion watchdog"
	depends on ARCH_ORION5X || ARCH_KIRKWOOD
	help
	  Say Y here if to include support for the watchdog timer
	  in the Marvell Orion5x and Kirkwood ARM SoCs.
	  To compile this driver as a module, choose M here: the
	  module will be called orion_wdt.

config COH901327_WATCHDOG
	bool "ST-Ericsson COH 901 327 watchdog"
	depends on ARCH_U300
	default y if MACH_U300
	select WATCHDOG_CORE
	help
	  Say Y here to include Watchdog timer support for the
	  watchdog embedded into the ST-Ericsson U300 series platforms.
	  This watchdog is used to reset the system and thus cannot be
	  compiled as a module.

config TWL4030_WATCHDOG
	tristate "TWL4030 Watchdog"
	depends on TWL4030_CORE
	help
	  Support for TI TWL4030 watchdog.  Say 'Y' here to enable the
	  watchdog timer support for TWL4030 chips.

config STMP3XXX_WATCHDOG
	tristate "Freescale STMP3XXX watchdog"
	depends on ARCH_STMP3XXX
	help
	  Say Y here if to include support for the watchdog timer
	  for the Sigmatel STMP37XX/378X SoC.
	  To compile this driver as a module, choose M here: the
	  module will be called stmp3xxx_wdt.

config NUC900_WATCHDOG
	tristate "Nuvoton NUC900 watchdog"
	depends on ARCH_W90X900
	help
	  Say Y here if to include support for the watchdog timer
	  for the Nuvoton NUC900 series SoCs.
	  To compile this driver as a module, choose M here: the
	  module will be called nuc900_wdt.

config TS72XX_WATCHDOG
	tristate "TS-72XX SBC Watchdog"
	depends on MACH_TS72XX
	help
	  Technologic Systems TS-7200, TS-7250 and TS-7260 boards have
	  watchdog timer implemented in a external CPLD chip. Say Y here
	  if you want to support for the watchdog timer on TS-72XX boards.

	  To compile this driver as a module, choose M here: the
	  module will be called ts72xx_wdt.

config MAX63XX_WATCHDOG
	tristate "Max63xx watchdog"
	depends on ARM && HAS_IOMEM
	select WATCHDOG_CORE
	help
	  Support for memory mapped max63{69,70,71,72,73,74} watchdog timer.

config IMX2_WDT
	tristate "IMX2+ Watchdog"
	depends on IMX_HAVE_PLATFORM_IMX2_WDT
	help
	  This is the driver for the hardware watchdog
	  on the Freescale IMX2 and later processors.
	  If you have one of these processors and wish to have
	  watchdog support enabled, say Y, otherwise say N.

	  To compile this driver as a module, choose M here: the
	  module will be called imx2_wdt.

# AVR32 Architecture

config AT32AP700X_WDT
	tristate "AT32AP700x watchdog"
	depends on CPU_AT32AP700X
	help
	  Watchdog timer embedded into AT32AP700x devices. This will reboot
	  your system when the timeout is reached.

# BLACKFIN Architecture

config BFIN_WDT
	tristate "Blackfin On-Chip Watchdog Timer"
	depends on BLACKFIN
	---help---
	  If you say yes here you will get support for the Blackfin On-Chip
	  Watchdog Timer. If you have one of these processors and wish to
	  have watchdog support enabled, say Y, otherwise say N.

	  To compile this driver as a module, choose M here: the
	  module will be called bfin_wdt.

# CRIS Architecture

# FRV Architecture

# H8300 Architecture

# X86 (i386 + ia64 + x86_64) Architecture

config ACQUIRE_WDT
	tristate "Acquire SBC Watchdog Timer"
	depends on X86
	---help---
	  This is the driver for the hardware watchdog on Single Board
	  Computers produced by Acquire Inc (and others). This watchdog
	  simply watches your kernel to make sure it doesn't freeze, and if
	  it does, it reboots your computer after a certain amount of time.

	  To compile this driver as a module, choose M here: the
	  module will be called acquirewdt.

	  Most people will say N.

config ADVANTECH_WDT
	tristate "Advantech SBC Watchdog Timer"
	depends on X86
	help
	  If you are configuring a Linux kernel for the Advantech single-board
	  computer, say `Y' here to support its built-in watchdog timer
	  feature. More information can be found at
	  <http://www.advantech.com.tw/products/>

config ALIM1535_WDT
	tristate "ALi M1535 PMU Watchdog Timer"
	depends on X86 && PCI
	---help---
	  This is the driver for the hardware watchdog on the ALi M1535 PMU.

	  To compile this driver as a module, choose M here: the
	  module will be called alim1535_wdt.

	  Most people will say N.

config ALIM7101_WDT
	tristate "ALi M7101 PMU Computer Watchdog"
	depends on PCI
	help
	  This is the driver for the hardware watchdog on the ALi M7101 PMU
	  as used in the x86 Cobalt servers and also found in some
	  SPARC Netra servers too.

	  To compile this driver as a module, choose M here: the
	  module will be called alim7101_wdt.

	  Most people will say N.

config F71808E_WDT
	tristate "Fintek F71808E, F71862FG, F71869, F71882FG and F71889FG Watchdog"
	depends on X86 && EXPERIMENTAL
	help
	  This is the driver for the hardware watchdog on the Fintek
	  F71808E, F71862FG, F71869, F71882FG and F71889FG Super I/O controllers.

	  You can compile this driver directly into the kernel, or use
	  it as a module.  The module will be called f71808e_wdt.

config SP5100_TCO
	tristate "AMD/ATI SP5100 TCO Timer/Watchdog"
	depends on X86 && PCI
	---help---
	  Hardware watchdog driver for the AMD/ATI SP5100 chipset. The TCO
	  (Total Cost of Ownership) timer is a watchdog timer that will reboot
	  the machine after its expiration. The expiration time can be
	  configured with the "heartbeat" parameter.

	  To compile this driver as a module, choose M here: the
	  module will be called sp5100_tco.

config GEODE_WDT
	tristate "AMD Geode CS5535/CS5536 Watchdog"
	depends on CS5535_MFGPT
	help
	  This driver enables a watchdog capability built into the
	  CS5535/CS5536 companion chips for the AMD Geode GX and LX
	  processors.  This watchdog watches your kernel to make sure
	  it doesn't freeze, and if it does, it reboots your computer after
	  a certain amount of time.

	  You can compile this driver directly into the kernel, or use
	  it as a module.  The module will be called geodewdt.

config SC520_WDT
	tristate "AMD Elan SC520 processor Watchdog"
	depends on X86
	help
	  This is the driver for the hardware watchdog built in to the
	  AMD "Elan" SC520 microcomputer commonly used in embedded systems.
	  This watchdog simply watches your kernel to make sure it doesn't
	  freeze, and if it does, it reboots your computer after a certain
	  amount of time.

	  You can compile this driver directly into the kernel, or use
	  it as a module.  The module will be called sc520_wdt.

config SBC_FITPC2_WATCHDOG
	tristate "Compulab SBC-FITPC2 watchdog"
	depends on X86
	---help---
	  This is the driver for the built-in watchdog timer on the fit-PC2,
	  fit-PC2i, CM-iAM single-board computers made by Compulab.

	  It`s possible to enable watchdog timer either from BIOS (F2) or from booted Linux.
	  When "Watchdog Timer Value" enabled one can set 31-255 s operational range.

	  Entering BIOS setup temporary disables watchdog operation regardless to current state,
	  so system will not be restarted while user in BIOS setup.

	  Once watchdog was enabled the system will be restarted every
	  "Watchdog Timer Value" period, so to prevent it user can restart or
	  disable the watchdog.

	  To compile this driver as a module, choose M here: the
	  module will be called sbc_fitpc2_wdt.

	  Most people will say N.

config EUROTECH_WDT
	tristate "Eurotech CPU-1220/1410 Watchdog Timer"
	depends on X86
	help
	  Enable support for the watchdog timer on the Eurotech CPU-1220 and
	  CPU-1410 cards.  These are PC/104 SBCs. Spec sheets and product
	  information are at <http://www.eurotech.it/>.

config IB700_WDT
	tristate "IB700 SBC Watchdog Timer"
	depends on X86
	---help---
	  This is the driver for the hardware watchdog on the IB700 Single
	  Board Computer produced by TMC Technology (www.tmc-uk.com). This watchdog
	  simply watches your kernel to make sure it doesn't freeze, and if
	  it does, it reboots your computer after a certain amount of time.

	  This driver is like the WDT501 driver but for slightly different hardware.

	  To compile this driver as a module, choose M here: the
	  module will be called ib700wdt.

	  Most people will say N.

config IBMASR
	tristate "IBM Automatic Server Restart"
	depends on X86
	help
	  This is the driver for the IBM Automatic Server Restart watchdog
	  timer built-in into some eServer xSeries machines.

	  To compile this driver as a module, choose M here: the
	  module will be called ibmasr.

config WAFER_WDT
	tristate "ICP Single Board Computer Watchdog Timer"
	depends on X86
	help
	  This is a driver for the hardware watchdog on the ICP Single
	  Board Computer. This driver is working on (at least) the following
	  IPC SBC's: Wafer 5823, Rocky 4783, Rocky 3703 and Rocky 3782.

	  To compile this driver as a module, choose M here: the
	  module will be called wafer5823wdt.

config I6300ESB_WDT
	tristate "Intel 6300ESB Timer/Watchdog"
	depends on X86 && PCI
	---help---
	  Hardware driver for the watchdog timer built into the Intel
	  6300ESB controller hub.

	  To compile this driver as a module, choose M here: the
	  module will be called i6300esb.

config INTEL_SCU_WATCHDOG
	bool "Intel SCU Watchdog for Mobile Platforms"
	depends on X86_MRST
	---help---
	  Hardware driver for the watchdog time built into the Intel SCU
	  for Intel Mobile Platforms.

	  To compile this driver as a module, choose M here.

config ITCO_WDT
	tristate "Intel TCO Timer/Watchdog"
	depends on (X86 || IA64) && PCI
	---help---
	  Hardware driver for the intel TCO timer based watchdog devices.
	  These drivers are included in the Intel 82801 I/O Controller
	  Hub family (from ICH0 up to ICH10) and in the Intel 63xxESB
	  controller hub.

	  The TCO (Total Cost of Ownership) timer is a watchdog timer
	  that will reboot the machine after its second expiration. The
	  expiration time can be configured with the "heartbeat" parameter.

	  On some motherboards the driver may fail to reset the chipset's
	  NO_REBOOT flag which prevents the watchdog from rebooting the
	  machine. If this is the case you will get a kernel message like
	  "failed to reset NO_REBOOT flag, reboot disabled by hardware".

	  To compile this driver as a module, choose M here: the
	  module will be called iTCO_wdt.

config ITCO_VENDOR_SUPPORT
	bool "Intel TCO Timer/Watchdog Specific Vendor Support"
	depends on ITCO_WDT
	---help---
	  Add vendor specific support to the intel TCO timer based watchdog
	  devices. At this moment we only have additional support for some
	  SuperMicro Inc. motherboards.

config IT8712F_WDT
	tristate "IT8712F (Smart Guardian) Watchdog Timer"
	depends on X86
	---help---
	  This is the driver for the built-in watchdog timer on the IT8712F
	  Super I/0 chipset used on many motherboards.

	  If the driver does not work, then make sure that the game port in
	  the BIOS is enabled.

	  To compile this driver as a module, choose M here: the
	  module will be called it8712f_wdt.

config IT87_WDT
	tristate "IT87 Watchdog Timer"
	depends on X86 && EXPERIMENTAL
	---help---
	  This is the driver for the hardware watchdog on the ITE IT8702,
	  IT8712, IT8716, IT8718, IT8720, IT8721, IT8726 Super I/O chips.
	  This watchdog simply watches your kernel to make sure it doesn't
	  freeze, and if it does, it reboots your computer after a certain
	  amount of time.

	  To compile this driver as a module, choose M here: the module will
	  be called it87_wdt.

config HP_WATCHDOG
	tristate "HP ProLiant iLO2+ Hardware Watchdog Timer"
	depends on X86 && PCI
	help
	  A software monitoring watchdog and NMI sourcing driver. This driver
	  will detect lockups and provide a stack trace. This is a driver that
	  will only load on an HP ProLiant system with a minimum of iLO2 support.
	  To compile this driver as a module, choose M here: the module will be
	  called hpwdt.

config HPWDT_NMI_DECODING
	bool "NMI decoding support for the HP ProLiant iLO2+ Hardware Watchdog Timer"
	depends on HP_WATCHDOG
	default y
	help
	  When an NMI occurs this feature will make the necessary BIOS calls to
	  log the cause of the NMI.

config SC1200_WDT
	tristate "National Semiconductor PC87307/PC97307 (ala SC1200) Watchdog"
	depends on X86
	help
	  This is a driver for National Semiconductor PC87307/PC97307 hardware
	  watchdog cards as found on the SC1200. This watchdog is mainly used
	  for power management purposes and can be used to power down the device
	  during inactivity periods (includes interrupt activity monitoring).

	  To compile this driver as a module, choose M here: the
	  module will be called sc1200wdt.

	  Most people will say N.

config SCx200_WDT
	tristate "National Semiconductor SCx200 Watchdog"
	depends on SCx200 && PCI
	help
	  Enable the built-in watchdog timer support on the National
	  Semiconductor SCx200 processors.

	  If compiled as a module, it will be called scx200_wdt.

config PC87413_WDT
	tristate "NS PC87413 watchdog"
	depends on X86
	---help---
	  This is the driver for the hardware watchdog on the PC87413 chipset
	  This watchdog simply watches your kernel to make sure it doesn't
	  freeze, and if it does, it reboots your computer after a certain
	  amount of time.

	  To compile this driver as a module, choose M here: the
	  module will be called pc87413_wdt.

	  Most people will say N.

config NV_TCO
	tristate "nVidia TCO Timer/Watchdog"
	depends on X86 && PCI
	---help---
	  Hardware driver for the TCO timer built into the nVidia Hub family
	  (such as the MCP51).  The TCO (Total Cost of Ownership) timer is a
	  watchdog timer that will reboot the machine after its second
	  expiration. The expiration time can be configured with the
	  "heartbeat" parameter.

	  On some motherboards the driver may fail to reset the chipset's
	  NO_REBOOT flag which prevents the watchdog from rebooting the
	  machine. If this is the case you will get a kernel message like
	  "failed to reset NO_REBOOT flag, reboot disabled by hardware".

	  To compile this driver as a module, choose M here: the
	  module will be called nv_tco.

config RDC321X_WDT
	tristate "RDC R-321x SoC watchdog"
	depends on X86_RDC321X
	help
	  This is the driver for the built in hardware watchdog
	  in the RDC R-321x SoC.

	  To compile this driver as a module, choose M here: the
	  module will be called rdc321x_wdt.

config 60XX_WDT
	tristate "SBC-60XX Watchdog Timer"
	depends on X86
	help
	  This driver can be used with the watchdog timer found on some
	  single board computers, namely the 6010 PII based computer.
	  It may well work with other cards.  It reads port 0x443 to enable
	  and re-set the watchdog timer, and reads port 0x45 to disable
	  the watchdog.  If you have a card that behave in similar ways,
	  you can probably make this driver work with your card as well.

	  You can compile this driver directly into the kernel, or use
	  it as a module.  The module will be called sbc60xxwdt.

config SBC8360_WDT
	tristate "SBC8360 Watchdog Timer"
	depends on X86
	---help---

	  This is the driver for the hardware watchdog on the SBC8360 Single
	  Board Computer produced by Axiomtek Co., Ltd. (www.axiomtek.com).

	  To compile this driver as a module, choose M here: the
	  module will be called sbc8360.

	  Most people will say N.

config SBC7240_WDT
	tristate "SBC Nano 7240 Watchdog Timer"
	depends on X86_32 && !UML
	---help---
	  This is the driver for the hardware watchdog found on the IEI
	  single board computers EPIC Nano 7240 (and likely others). This
	  watchdog simply watches your kernel to make sure it doesn't freeze,
	  and if it does, it reboots your computer after a certain amount of
	  time.

	  To compile this driver as a module, choose M here: the
	  module will be called sbc7240_wdt.

config CPU5_WDT
	tristate "SMA CPU5 Watchdog"
	depends on X86
	---help---
	  TBD.
	  To compile this driver as a module, choose M here: the
	  module will be called cpu5wdt.

config SMSC_SCH311X_WDT
	tristate "SMSC SCH311X Watchdog Timer"
	depends on X86
	---help---
	  This is the driver for the hardware watchdog timer on the
	  SMSC SCH3112, SCH3114 and SCH3116 Super IO chipset
	  (LPC IO with 8042 KBC, Reset Generation, HWM and multiple
	  serial ports).

	  To compile this driver as a module, choose M here: the
	  module will be called sch311x_wdt.

config SMSC37B787_WDT
	tristate "Winbond SMsC37B787 Watchdog Timer"
	depends on X86
	---help---
	  This is the driver for the hardware watchdog component on the
	  Winbond SMsC37B787 chipset as used on the NetRunner Mainboard
	  from Vision Systems and maybe others.

	  This watchdog simply watches your kernel to make sure it doesn't
	  freeze, and if it does, it reboots your computer after a certain
	  amount of time.

	  Usually a userspace daemon will notify the kernel WDT driver that
	  userspace is still alive, at regular intervals.

	  To compile this driver as a module, choose M here: the
	  module will be called smsc37b787_wdt.

	  Most people will say N.

config VIA_WDT
	tristate "VIA Watchdog Timer"
	depends on X86
	select WATCHDOG_CORE
	---help---
	This is the driver for the hardware watchdog timer on VIA
	southbridge chipset CX700, VX800/VX820 or VX855/VX875.

	To compile this driver as a module, choose M here; the module
	will be called via_wdt.

	Most people will say N.

config W83627HF_WDT
	tristate "W83627HF/W83627DHG Watchdog Timer"
	depends on X86
	---help---
	  This is the driver for the hardware watchdog on the W83627HF chipset
	  as used in Advantech PC-9578 and Tyan S2721-533 motherboards
	  (and likely others). The driver also supports the W83627DHG chip.
	  This watchdog simply watches your kernel to make sure it doesn't
	  freeze, and if it does, it reboots your computer after a certain
	  amount of time.

	  To compile this driver as a module, choose M here: the
	  module will be called w83627hf_wdt.

	  Most people will say N.

config W83697HF_WDT
	tristate "W83697HF/W83697HG Watchdog Timer"
	depends on X86
	---help---
	  This is the driver for the hardware watchdog on the W83697HF/HG
	  chipset as used in Dedibox/VIA motherboards (and likely others).
	  This watchdog simply watches your kernel to make sure it doesn't
	  freeze, and if it does, it reboots your computer after a certain
	  amount of time.

	  To compile this driver as a module, choose M here: the
	  module will be called w83697hf_wdt.

	  Most people will say N.

config W83697UG_WDT
	tristate "W83697UG/W83697UF Watchdog Timer"
	depends on X86
	---help---
	  This is the driver for the hardware watchdog on the W83697UG/UF
	  chipset as used in MSI Fuzzy CX700 VIA motherboards (and likely others).
	  This watchdog simply watches your kernel to make sure it doesn't
	  freeze, and if it does, it reboots your computer after a certain
	  amount of time.

	  To compile this driver as a module, choose M here: the
	  module will be called w83697ug_wdt.

	  Most people will say N.

config W83877F_WDT
	tristate "W83877F (EMACS) Watchdog Timer"
	depends on X86
	---help---
	  This is the driver for the hardware watchdog on the W83877F chipset
	  as used in EMACS PC-104 motherboards (and likely others).  This
	  watchdog simply watches your kernel to make sure it doesn't freeze,
	  and if it does, it reboots your computer after a certain amount of
	  time.

	  To compile this driver as a module, choose M here: the
	  module will be called w83877f_wdt.

	  Most people will say N.

config W83977F_WDT
	tristate "W83977F (PCM-5335) Watchdog Timer"
	depends on X86
	---help---
	  This is the driver for the hardware watchdog on the W83977F I/O chip
	  as used in AAEON's PCM-5335 SBC (and likely others).  This
	  watchdog simply watches your kernel to make sure it doesn't freeze,
	  and if it does, it reboots your computer after a certain amount of
	  time.

	  To compile this driver as a module, choose M here: the
	  module will be called w83977f_wdt.

config MACHZ_WDT
	tristate "ZF MachZ Watchdog"
	depends on X86
	---help---
	  If you are using a ZF Micro MachZ processor, say Y here, otherwise
	  N.  This is the driver for the watchdog timer built-in on that
	  processor using ZF-Logic interface.  This watchdog simply watches
	  your kernel to make sure it doesn't freeze, and if it does, it
	  reboots your computer after a certain amount of time.

	  To compile this driver as a module, choose M here: the
	  module will be called machzwd.

config SBC_EPX_C3_WATCHDOG
	tristate "Winsystems SBC EPX-C3 watchdog"
	depends on X86
	---help---
	  This is the driver for the built-in watchdog timer on the EPX-C3
	  Single-board computer made by Winsystems, Inc.

	  *Note*: This hardware watchdog is not probeable and thus there
	  is no way to know if writing to its IO address will corrupt
	  your system or have any real effect.  The only way to be sure
	  that this driver does what you want is to make sure you
	  are running it on an EPX-C3 from Winsystems with the watchdog
	  timer at IO address 0x1ee and 0x1ef.  It will write to both those
	  IO ports.  Basically, the assumption is made that if you compile
	  this driver into your kernel and/or load it as a module, that you
	  know what you are doing and that you are in fact running on an
	  EPX-C3 board!

	  To compile this driver as a module, choose M here: the
	  module will be called sbc_epx_c3.

# M32R Architecture

# M68K Architecture

config M54xx_WATCHDOG
	tristate "MCF54xx watchdog support"
	depends on M548x
	help
	  To compile this driver as a module, choose M here: the
	  module will be called m54xx_wdt.

# MicroBlaze Architecture

config XILINX_WATCHDOG
	tristate "Xilinx Watchdog timer"
	depends on MICROBLAZE
	---help---
	  Watchdog driver for the xps_timebase_wdt ip core.

	  IMPORTANT: The xps_timebase_wdt parent must have the property
	  "clock-frequency" at device tree.

	  To compile this driver as a module, choose M here: the
	  module will be called of_xilinx_wdt.

# MIPS Architecture

config ATH79_WDT
	tristate "Atheros AR71XX/AR724X/AR913X hardware watchdog"
	depends on ATH79
	help
	  Hardware driver for the built-in watchdog timer on the Atheros
	  AR71XX/AR724X/AR913X SoCs.

config BCM47XX_WDT
	tristate "Broadcom BCM47xx Watchdog Timer"
	depends on BCM47XX
	help
	  Hardware driver for the Broadcom BCM47xx Watchog Timer.

config RC32434_WDT
	tristate "IDT RC32434 SoC Watchdog Timer"
	depends on MIKROTIK_RB532
	help
	  Hardware driver for the IDT RC32434 SoC built-in
	  watchdog timer.

	  To compile this driver as a module, choose M here: the
	  module will be called rc32434_wdt.

config INDYDOG
	tristate "Indy/I2 Hardware Watchdog"
	depends on SGI_HAS_INDYDOG
	help
	  Hardware driver for the Indy's/I2's watchdog. This is a
	  watchdog timer that will reboot the machine after a 60 second
	  timer expired and no process has written to /dev/watchdog during
	  that time.

config JZ4740_WDT
	tristate "Ingenic jz4740 SoC hardware watchdog"
	depends on MACH_JZ4740
	select WATCHDOG_CORE
	help
	  Hardware driver for the built-in watchdog timer on Ingenic jz4740 SoCs.

config WDT_MTX1
	tristate "MTX-1 Hardware Watchdog"
	depends on MIPS_MTX1
	help
	  Hardware driver for the MTX-1 boards. This is a watchdog timer that
	  will reboot the machine after a 100 seconds timer expired.

config PNX833X_WDT
	tristate "PNX833x Hardware Watchdog"
	depends on SOC_PNX8335
	help
	  Hardware driver for the PNX833x's watchdog. This is a
	  watchdog timer that will reboot the machine after a programmable
	  timer has expired and no process has written to /dev/watchdog during
	  that time.

config SIBYTE_WDOG
	tristate "Sibyte SoC hardware watchdog"
	depends on CPU_SB1
	help
	  Watchdog driver for the built in watchdog hardware in Sibyte
	  SoC processors.  There are apparently two watchdog timers
	  on such processors; this driver supports only the first one,
	  because currently Linux only supports exporting one watchdog
	  to userspace.

	  To compile this driver as a loadable module, choose M here.
	  The module will be called sb_wdog.

config AR7_WDT
	tristate "TI AR7 Watchdog Timer"
	depends on AR7
	help
	  Hardware driver for the TI AR7 Watchdog Timer.

config TXX9_WDT
	tristate "Toshiba TXx9 Watchdog Timer"
	depends on CPU_TX39XX || CPU_TX49XX
	select WATCHDOG_CORE
	help
	  Hardware driver for the built-in watchdog timer on TXx9 MIPS SoCs.

config OCTEON_WDT
	tristate "Cavium OCTEON SOC family Watchdog Timer"
	depends on CPU_CAVIUM_OCTEON
	default y
	select EXPORT_UASM if OCTEON_WDT = m
	help
	  Hardware driver for OCTEON's on chip watchdog timer.
	  Enables the watchdog for all cores running Linux. It
	  installs a NMI handler and pokes the watchdog based on an
	  interrupt.  On first expiration of the watchdog, the
	  interrupt handler pokes it.  The second expiration causes an
	  NMI that prints a message. The third expiration causes a
	  global soft reset.

	  When userspace has /dev/watchdog open, no poking is done
	  from the first interrupt, it is then only poked when the
	  device is written.

config BCM63XX_WDT
	tristate "Broadcom BCM63xx hardware watchdog"
	depends on BCM63XX
	help
	  Watchdog driver for the built in watchdog hardware in Broadcom
	  BCM63xx SoC.

	  To compile this driver as a loadable module, choose M here.
	  The module will be called bcm63xx_wdt.

config LANTIQ_WDT
	tristate "Lantiq SoC watchdog"
	depends on LANTIQ
	help
	  Hardware driver for the Lantiq SoC Watchdog Timer.

# PARISC Architecture

# POWERPC Architecture

config GEF_WDT
	tristate "GE Watchdog Timer"
	depends on GE_FPGA
	---help---
	  Watchdog timer found in a number of GE single board computers.

config MPC5200_WDT
	bool "MPC52xx Watchdog Timer"
	depends on PPC_MPC52xx
	help
	  Use General Purpose Timer (GPT) 0 on the MPC5200 as Watchdog.

config 8xxx_WDT
	tristate "MPC8xxx Platform Watchdog Timer"
	depends on PPC_8xx || PPC_83xx || PPC_86xx
	help
	  This driver is for a SoC level watchdog that exists on some
	  Freescale PowerPC processors. So far this driver supports:
	  - MPC8xx watchdogs
	  - MPC83xx watchdogs
	  - MPC86xx watchdogs

	  For BookE processors (MPC85xx) use the BOOKE_WDT driver instead.

config MV64X60_WDT
	tristate "MV64X60 (Marvell Discovery) Watchdog Timer"
	depends on MV64X60

config PIKA_WDT
	tristate "PIKA FPGA Watchdog"
	depends on WARP
	default y
	help
	  This enables the watchdog in the PIKA FPGA. Currently used on
	  the Warp platform.

config BOOKE_WDT
	tristate "PowerPC Book-E Watchdog Timer"
	depends on BOOKE || 4xx
	---help---
	  Watchdog driver for PowerPC Book-E chips, such as the Freescale
	  MPC85xx SOCs and the IBM PowerPC 440.

	  Please see Documentation/watchdog/watchdog-api.txt for
	  more information.

config BOOKE_WDT_DEFAULT_TIMEOUT
	int "PowerPC Book-E Watchdog Timer Default Timeout"
	depends on BOOKE_WDT
	default 38 if FSL_BOOKE
	range 0 63 if FSL_BOOKE
	default 3 if !FSL_BOOKE
	range 0 3 if !FSL_BOOKE
	help
	  Select the default watchdog timer period to be used by the PowerPC
	  Book-E watchdog driver.  A watchdog "event" occurs when the bit
	  position represented by this number transitions from zero to one.

	  For Freescale Book-E processors, this is a number between 0 and 63.
	  For other Book-E processors, this is a number between 0 and 3.

	  The value can be overridden by the wdt_period command-line parameter.

# PPC64 Architecture

config WATCHDOG_RTAS
	tristate "RTAS watchdog"
	depends on PPC_RTAS
	help
	  This driver adds watchdog support for the RTAS watchdog.

	  To compile this driver as a module, choose M here. The module
	  will be called wdrtas.

# S390 Architecture

config ZVM_WATCHDOG
	tristate "z/VM Watchdog Timer"
	depends on S390
	help
	  IBM s/390 and zSeries machines running under z/VM 5.1 or later
	  provide a virtual watchdog timer to their guest that cause a
	  user define Control Program command to be executed after a
	  timeout.

	  To compile this driver as a module, choose M here. The module
	  will be called vmwatchdog.

# SUPERH (sh + sh64) Architecture

config SH_WDT
	tristate "SuperH Watchdog"
	depends on SUPERH && (CPU_SH3 || CPU_SH4)
	help
	  This driver adds watchdog support for the integrated watchdog in the
	  SuperH processors. If you have one of these processors and wish
	  to have watchdog support enabled, say Y, otherwise say N.

	  As a side note, saying Y here will automatically boost HZ to 1000
	  so that the timer has a chance to clear the overflow counter. On
	  slower systems (such as the SH-2 and SH-3) this will likely yield
	  some performance issues. As such, the WDT should be avoided here
	  unless it is absolutely necessary.

	  To compile this driver as a module, choose M here: the
	  module will be called shwdt.

# SPARC Architecture

# SPARC64 Architecture

config WATCHDOG_CP1XXX
	tristate "CP1XXX Hardware Watchdog support"
	depends on SPARC64 && PCI
	---help---
	  This is the driver for the hardware watchdog timers present on
	  Sun Microsystems CompactPCI models CP1400 and CP1500.

	  To compile this driver as a module, choose M here: the
	  module will be called cpwatchdog.

	  If you do not have a CompactPCI model CP1400 or CP1500, or
	  another UltraSPARC-IIi-cEngine boardset with hardware watchdog,
	  you should say N to this option.

config WATCHDOG_RIO
	tristate "RIO Hardware Watchdog support"
	depends on SPARC64 && PCI
	help
	  Say Y here to support the hardware watchdog capability on Sun RIO
	  machines.  The watchdog timeout period is normally one minute but
	  can be changed with a boot-time parameter.

# XTENSA Architecture

# Xen Architecture

config XEN_WDT
	tristate "Xen Watchdog support"
	depends on XEN
	help
	  Say Y here to support the hypervisor watchdog capability provided
	  by Xen 4.0 and newer.  The watchdog timeout period is normally one
	  minute but can be changed with a boot-time parameter.

config UML_WATCHDOG
	tristate "UML watchdog"
	depends on UML

#
# ISA-based Watchdog Cards
#

comment "ISA-based Watchdog Cards"
	depends on ISA

config PCWATCHDOG
	tristate "Berkshire Products ISA-PC Watchdog"
	depends on ISA
	---help---
	  This is the driver for the Berkshire Products ISA-PC Watchdog card.
	  This card simply watches your kernel to make sure it doesn't freeze,
	  and if it does, it reboots your computer after a certain amount of
	  time. This driver is like the WDT501 driver but for different
	  hardware. Please read <file:Documentation/watchdog/pcwd-watchdog.txt>. The PC
	  watchdog cards can be ordered from <http://www.berkprod.com/>.

	  To compile this driver as a module, choose M here: the
	  module will be called pcwd.

	  Most people will say N.

config MIXCOMWD
	tristate "Mixcom Watchdog"
	depends on ISA
	---help---
	  This is a driver for the Mixcom hardware watchdog cards.  This
	  watchdog simply watches your kernel to make sure it doesn't freeze,
	  and if it does, it reboots your computer after a certain amount of
	  time.

	  To compile this driver as a module, choose M here: the
	  module will be called mixcomwd.

	  Most people will say N.

config WDT
	tristate "WDT Watchdog timer"
	depends on ISA
	---help---
	  If you have a WDT500P or WDT501P watchdog board, say Y here,
	  otherwise N. It is not possible to probe for this board, which means
	  that you have to inform the kernel about the IO port and IRQ that
	  is needed (you can do this via the io and irq parameters)

	  To compile this driver as a module, choose M here: the
	  module will be called wdt.

#
# PCI-based Watchdog Cards
#

comment "PCI-based Watchdog Cards"
	depends on PCI

config PCIPCWATCHDOG
	tristate "Berkshire Products PCI-PC Watchdog"
	depends on PCI
	---help---
	  This is the driver for the Berkshire Products PCI-PC Watchdog card.
	  This card simply watches your kernel to make sure it doesn't freeze,
	  and if it does, it reboots your computer after a certain amount of
	  time. The card can also monitor the internal temperature of the PC.
	  More info is available at <http://www.berkprod.com/pci_pc_watchdog.htm>.

	  To compile this driver as a module, choose M here: the
	  module will be called pcwd_pci.

	  Most people will say N.

config WDTPCI
	tristate "PCI-WDT500/501 Watchdog timer"
	depends on PCI
	---help---
	  If you have a PCI-WDT500/501 watchdog board, say Y here, otherwise N.

	  If you have a PCI-WDT501 watchdog board then you can enable the
	  temperature sensor by setting the type parameter to 501.

	  If you want to enable the Fan Tachometer on the PCI-WDT501, then you
	  can do this via the tachometer parameter. Only do this if you have a
	  fan tachometer actually set up.

	  To compile this driver as a module, choose M here: the
	  module will be called wdt_pci.

#
# USB-based Watchdog Cards
#

comment "USB-based Watchdog Cards"
	depends on USB

config USBPCWATCHDOG
	tristate "Berkshire Products USB-PC Watchdog"
	depends on USB
	---help---
	  This is the driver for the Berkshire Products USB-PC Watchdog card.
	  This card simply watches your kernel to make sure it doesn't freeze,
	  and if it does, it reboots your computer after a certain amount of
	  time. The card can also monitor the internal temperature of the PC.
	  More info is available at <http://www.berkprod.com/usb_pc_watchdog.htm>.

	  To compile this driver as a module, choose M here: the
	  module will be called pcwd_usb.

	  Most people will say N.

config ZX29_WATCHDOG
	tristate "ZX29 Watchdog"
	depends on PLAT_ZTE
	help
	  Support for TI zx29xx watchdog.  
	  Say 'Y' here to enable the watchdog timer.

config ZX29_WDT_TEST
	tristate "WDT driver"
	depends on PLAT_ZTE
	---help---
	  Communicate between cores with each other!

config WATCHDOG_RESTART
	tristate "Watchdog restart"
	depends on ZX29_WATCHDOG
	---help---
	  soft restart by wdt!	  

endif # WATCHDOG
