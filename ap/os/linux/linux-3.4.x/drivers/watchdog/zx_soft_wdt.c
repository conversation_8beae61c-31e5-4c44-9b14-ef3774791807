
#include <linux/module.h>	/* For module stuff/... */
#include <linux/errno.h>	/* For the -ENODEV/... values */
#include <linux/miscdevice.h>	/* For handling misc devices */
#include <linux/fs.h>		/* For file operations */
#include <linux/kthread.h>	/*For kthread_run()*/
#include <linux/init.h>		/* For __init/__exit/... */
#include <linux/soc/zte/rpm/rpmsg.h> /* For icp operation*/
#include <linux/slab.h>
#include <mach/iomap.h>
#include <linux/zx_soft_wdt.h>
#include <linux/delay.h>
#include <linux/syscalls.h>/*For sys_open operation*/
#include <mach/spinlock.h>
#include <linux/cpps_init2.h>
#include <linux/cp_types.h>
#include "NvParam_drv.h"

#define WDT_DEFAULT 	(30)
#define WDT_INT_TIME 	(5)
#define WDT_SLEEP_TIME 	(10)
#define WDT_MARGIN 		(2*WDT_INT_TIME)
#define WDT_DIABLE		(0x44495341) /*ascii: DISA*/
#define MAX_SLEEP_TIME  (60*30)		 /*30 min*/

#define WDT_NV_ADDR				(WDT_IRAM_BASE)
#define WDT_NV_SIZE				(0x4)
#define WDT_GLOBAL_COUNT_ADDR	(WDT_NV_ADDR + WDT_NV_SIZE)
#define WDT_GLOBAL_COUNT_SIZE	(0x4)
#define WDT_PS_TIMEOUT_ADDR		(WDT_GLOBAL_COUNT_ADDR + WDT_GLOBAL_COUNT_SIZE)
#define WDT_PS_TIMEOUT_SIZE		(0x4)
#define WDT_AP_TIMEOUT_ADDR		(WDT_PS_TIMEOUT_ADDR + WDT_PS_TIMEOUT_SIZE)
#define WDT_AP_TIMEOUT_SIZE		(0x4)
#define WDT_PHY_TIMEOUT_ADDR	(WDT_AP_TIMEOUT_ADDR + WDT_AP_TIMEOUT_SIZE)
#define WDT_PHY_TIMEOUT_SIZE	(0x4)
#define WDT_M0_SWITCH_ADDR		(WDT_PHY_TIMEOUT_ADDR + WDT_PHY_TIMEOUT_SIZE)
#define WDT_M0_SWITCH_SIZE		(0x4)
#define WDT_PS_SWITCH_ADDR		(WDT_M0_SWITCH_ADDR + WDT_M0_SWITCH_SIZE)
#define WDT_PS_SWITCH_SIZE		(0x4)
#define WDT_AP_SWITCH_ADDR		(WDT_PS_SWITCH_ADDR + WDT_PS_SWITCH_SIZE)
#define WDT_AP_SWITCH_SIZE		(0x4)
#define WDT_PHY_SWITCH_ADDR		(WDT_AP_SWITCH_ADDR + WDT_AP_SWITCH_SIZE)
#define WDT_PHY_SWITCH_SIZE		(0x4)

/*wdt  nv  flag*/
#define WDT_OFF 			(0x57445446) //ascii:WDTF
#define WDT_ON 				(0x5744544F) //ascii:WDTO
#define RM_WDT_STATE_REG    (ZX_RM_WDT_BASE + 0x10)  /* Watchdog Status Register*/
#define RM_WDT_SET_EN_REG   (ZX_RM_WDT_BASE + 0x18)  /* Watchdog enable refresh work clock domain register */
#define RM_WDT_START_REG    (ZX_RM_WDT_BASE + 0x1c)  /* Watchdog start or stop register */


MODULE_AUTHOR("ZTE");
MODULE_LICENSE("GPL");

/* Each of a wdt's open files has private_data pointing to soft_wdt_file_private */
struct soft_wdt_file_private {
	struct list_head list;
	/*uint is s*/
	unsigned int interval;
	/*uint is s*/
	unsigned int handle_timeout;
	unsigned int handle_timeout_cnt;
	bool wakeup;
	bool is_check;
	struct task_struct * current_task;
};

static bool g_wdt_wakeup_flag = false;
static u32 g_wdt_wakeup_min_time = MAX_SLEEP_TIME;
static volatile u32 g_wdt_sleep_pre_time;
static unsigned int g_wdt_nv = 0;
volatile unsigned int g_wdt_priority = 37;

#ifdef CONFIG_PREEMPT_RT_FULL

static unsigned int g_wdt_nvdata = 0;
static volatile unsigned int g_ctrm_wdt_nv = 0;

//extern int nand_NvRead(int dwStart, int dwLen, char* to);
//extern int nand_NvProgram(int dwStart, int dwLen, char* from);
#ifndef USE_CPPS_KO
#ifndef CONFIG_ARCH_ZX297520V3_CAP
extern UINT32 zOss_NvItemRead(UINT32 NvItemID, UINT8 * NvItemData, UINT32 NvItemLen);
extern UINT32 zOss_NvItemWrite(UINT32 NvItemID, UINT8 * NvItemData, UINT32 NvItemLen);
#endif
#endif

#endif

/*used for test*/
//#define ZX_WDT_DBG

static LIST_HEAD(zx_wdt_list);

DEFINE_SPINLOCK(zx_wdt_lock);

#ifdef CONFIG_PREEMPT_RT_FULL

/*operate m0 wdt*/
void zx_wdt_m0_refresh_cfg(void)
{	
	volatile u32 tmp;

	tmp = zx_read_reg(RM_WDT_SET_EN_REG);
    tmp ^= 0x3f;
	
	reg_spin_lock();
	
	zx_write_reg(RM_WDT_SET_EN_REG, tmp|0x12340000);

	reg_spin_unlock();

   	/*wait for refresh ack*/
	while (1) {
		if(((zx_read_reg(RM_WDT_STATE_REG) >> 4) & 0x3f) == tmp)
			break;
	}
}

/*only used by ramdump*/
void zx_wdt_m0_stop(void)
{
	zx_wdt_m0_refresh_cfg();

	reg_spin_lock();
	zx_write_reg(RM_WDT_START_REG, 0x12340000);
	reg_spin_unlock();	
}
#ifndef CONFIG_ARCH_ZX297520V3_CAP
static int zx_wdt_set_nv(bool nv_flag)
{
	int ret = 0;

	if (nv_flag) {
		g_wdt_nvdata = WDT_ON;
	} else {
		g_wdt_nvdata = WDT_OFF;
	}

	//ret = nand_NvProgram(DRV_SYS_NV_ITEM_ADDR(wdtSwitch), DRV_SYS_NV_ITEM_SIZE(wdtSwitch), (u8 *)(&g_wdt_nvdata));
	#ifndef CONFIG_SYSTEM_RECOVERY
	ret = CPPS_FUNC(cpps_callbacks, zOss_NvItemWrite)(DRV_SYS_NV_ITEM_ADDR(wdtSwitch), (u8 *)(&g_wdt_nvdata), DRV_SYS_NV_ITEM_SIZE(wdtSwitch));
	#endif
	if (ret) {
		printk(KERN_ERR"[zx soft wdt]: zOss_NvItemWrite failed [err=%d]!\n", ret);
	}
	
	return ret;
}

static int zx_wdt_get_nv(void)
{
	int ret = 0;
	int g_wdt_nv_priority = 0;

	//ret = nand_NvRead(DRV_SYS_NV_ITEM_ADDR(wdtSwitch), DRV_SYS_NV_ITEM_SIZE(wdtSwitch), (u8 *)(&g_wdt_nv));
	#ifndef CONFIG_SYSTEM_RECOVERY
	ret = CPPS_FUNC(cpps_callbacks, zOss_NvItemRead)(DRV_SYS_NV_ITEM_ADDR(wdtSwitch), (u8 *)(&g_wdt_nv), DRV_SYS_NV_ITEM_SIZE(wdtSwitch));
	#endif
    if (ret) {
		printk(KERN_ERR"[zx soft wdt]: zOss_NvItemRead failed [err=%d]!\n", ret);
		return ret;
	}

	//g_wdt_nv = WDT_OFF;

	if (g_wdt_nv == WDT_OFF) {
		zx_write_reg(WDT_NV_ADDR, WDT_OFF);
	} else if (g_wdt_nv == WDT_ON) {
		zx_write_reg(WDT_NV_ADDR, WDT_ON);
	} else {
		printk(KERN_ERR"[zx soft wdt]: g_wdt_nv=%x invalid !\n", g_wdt_nv);
		return -EINVAL;
	}

	//ret = nand_NvRead(DRV_SYS_NV_ITEM_ADDR(wdtPriority), DRV_SYS_NV_ITEM_SIZE(wdtPriority), (u8 *)(&g_wdt_nv_priority));
	#ifndef CONFIG_SYSTEM_RECOVERY	
	ret = CPPS_FUNC(cpps_callbacks, zOss_NvItemRead)(DRV_SYS_NV_ITEM_ADDR(wdtPriority), (u8 *)(&g_wdt_nv_priority), DRV_SYS_NV_ITEM_SIZE(wdtPriority));
	#endif
	if (ret) {
		printk(KERN_ERR"[zx soft wdt]: zOss_NvItemRead Priority failed [err=%d]!\n", ret);
		return ret;
	}

	if((g_wdt_nv_priority > 99)||(g_wdt_nv_priority <= 0))
	{
		g_wdt_priority = 37;
		printk(KERN_ERR"[zx soft wdt]: g_wdt_nv_priority=%x invalid!\n", g_wdt_nv_priority);
	}
	else
		g_wdt_priority = 99 - g_wdt_nv_priority;

	return 0;
}

static bool zx_wdt_get_wdtnv_for_ctrm(void)
{
	int ret= 0;

	//ret = nand_NvRead(DRV_SYS_NV_ITEM_ADDR(wdtSwitch), DRV_SYS_NV_ITEM_SIZE(wdtSwitch), (u8 *)(&g_ctrm_wdt_nv));
	#ifndef CONFIG_SYSTEM_RECOVERY	
	ret = CPPS_FUNC(cpps_callbacks, zOss_NvItemRead)(DRV_SYS_NV_ITEM_ADDR(wdtSwitch), (u8 *)(&g_ctrm_wdt_nv), DRV_SYS_NV_ITEM_SIZE(wdtSwitch));
	#endif
	if (ret) {
		printk(KERN_ERR"[zx soft wdt]: zOss_NvItemRead failed [err=%d]!\n", ret);
		return ret;
	}
	
	if (g_ctrm_wdt_nv == WDT_OFF) {
		return false;
	} else if (g_ctrm_wdt_nv == WDT_ON) {
		return true;
	} else {
		printk(KERN_ERR"[zx soft wdt]: g_ctrm_wdt_nv=0x%x invalid!\n", g_ctrm_wdt_nv);
		return -EINVAL;
	}
}
#endif
#endif

static inline void zx_wdt_enbale(bool flag)
{
	unsigned int addr = 0;
	
#ifdef CONFIG_PREEMPT_RT_FULL
	#ifndef CONFIG_ARCH_ZX297520V3_CAP
	addr = (unsigned int)WDT_PS_SWITCH_ADDR;
	#else
	addr = (unsigned int)WDT_AP_SWITCH_ADDR;
	#endif
#else
	addr = (unsigned int)WDT_AP_SWITCH_ADDR;
#endif

	if (flag) {
		zx_write_reg(addr, 0);
	} else {
		zx_write_reg(addr, WDT_DIABLE);
	}
}

static void zx_wdt_set_time_out(unsigned int val)
{
#ifdef CONFIG_PREEMPT_RT_FULL
	#ifndef CONFIG_ARCH_ZX297520V3_CAP
	zx_write_reg(WDT_PS_TIMEOUT_ADDR, val);
	#else
	zx_write_reg(WDT_AP_TIMEOUT_ADDR, val);	
	#endif
#else	
	zx_write_reg(WDT_AP_TIMEOUT_ADDR, val);
#endif
}

static unsigned int zx_wdt_get_time_out(void)
{
#ifdef CONFIG_PREEMPT_RT_FULL
	#ifndef CONFIG_ARCH_ZX297520V3_CAP
	return zx_read_reg(WDT_PS_TIMEOUT_ADDR);
	#else
	return zx_read_reg(WDT_AP_TIMEOUT_ADDR);	
	#endif
#else
	return zx_read_reg(WDT_AP_TIMEOUT_ADDR);
#endif
}

static inline unsigned int zx_wdt_get_global_cnt(void)
{
	return zx_read_reg(WDT_GLOBAL_COUNT_ADDR);
}

void zx_wdt_handle_before_psm(void)
{
	unsigned int tmp_val = zx_wdt_get_global_cnt();
	
	if (g_wdt_wakeup_flag == false) {
		zx_wdt_set_time_out(tmp_val + MAX_SLEEP_TIME);
	} else {
		zx_wdt_set_time_out(tmp_val + g_wdt_wakeup_min_time);
	}
}

void zx_wdt_handle_after_psm(void)
{
	unsigned int val = 0;
	
	val = zx_wdt_get_global_cnt();
	zx_wdt_set_time_out(val + WDT_SLEEP_TIME + WDT_MARGIN);
}

static int zx_wdt_alloc_file(struct file *file)
{
	struct soft_wdt_file_private *priv;

	priv = kmalloc(sizeof(*priv), GFP_KERNEL);
	if (!priv) {
		printk(KERN_ERR"[zx soft wdt]:kmalloc priv failed!\n");
		return -ENOMEM;
	}

	priv->interval = WDT_DEFAULT;
	priv->wakeup = false;
	priv->handle_timeout = zx_wdt_get_global_cnt() + priv->interval;
	priv->handle_timeout_cnt = 0;
	priv->current_task = current;
	priv->is_check = true;
	file->private_data = priv;

	return 0;
}

static void zx_wdt_free_file(struct file *file)
{
	struct soft_wdt_file_private *priv = file->private_data;

	file->private_data = NULL;
	kfree(priv);

}

static void zx_wdt_add_list(struct file *file, struct list_head *list)
{
	struct soft_wdt_file_private *priv = file->private_data;

	spin_lock(&zx_wdt_lock);
	list_add_tail(&priv->list, list);
	spin_unlock(&zx_wdt_lock);
}

static void zx_wdt_del_list(struct file *file)
{
	struct soft_wdt_file_private *priv = file->private_data;

	spin_lock(&zx_wdt_lock);
	list_del(&priv->list);
	spin_unlock(&zx_wdt_lock);
}


static int zx_wdt_open(struct inode *inode, struct file *file)
{
	int retval;
	
	retval = zx_wdt_alloc_file(file);
	if (retval) {
		printk(KERN_ERR"[zx soft wdt]:zx_wdt_alloc_file failed [err=%d]!\n", retval);
		return -ENOMEM;
	}

	zx_wdt_add_list(file, &zx_wdt_list);
	
	return 0; 
}

static int zx_wdt_close(struct inode *inode, struct file *file)
{
	zx_wdt_del_list(file);
	zx_wdt_free_file(file);

	return 0;
}

static long zx_wdt_ioctl(struct file *file, unsigned int cmd, unsigned long arg)
{
	
	unsigned int ret = 0;
	unsigned int temp;

	struct soft_wdt_file_private *priv = file->private_data;

	switch(cmd)
	{
		case ZX_WDT_SET_INTERNAL:
			if(arg < WDT_SLEEP_TIME)
			{
				printk(KERN_ERR"[zx soft wdt]: wrong internal val (val must >= %d)!\n", WDT_SLEEP_TIME);
				return -ENXIO;
			}
			priv->interval = arg;
			break;

		case ZX_WDT_SET_WAKEUP:
			priv->wakeup = (bool)arg;
			break;

		case ZX_WDT_FEED_DOG:
			priv->handle_timeout_cnt = 0;
			priv->handle_timeout = priv->interval + zx_wdt_get_global_cnt();
			break;

		case ZX_WDT_SET_AP_SWITCH:
			zx_wdt_enbale((bool)arg);
			break;

		case ZX_WDT_GET_HANDLE_TIMEOUT:
			temp = priv->handle_timeout;
			*(unsigned int *)arg = temp;
			break;

		case ZX_WDT_GET_GLOBAL_CNT:
			temp = zx_wdt_get_global_cnt();
			*(unsigned int *)arg = temp;
			break;

		case ZX_WDT_GET_AP_TIMEOUT:
			temp = zx_wdt_get_time_out();
			*(unsigned int *)arg = temp;
			break;
		case ZX_WDT_SET_NV:		
#ifdef CONFIG_PREEMPT_RT_FULL
#ifndef CONFIG_ARCH_ZX297520V3_CAP
			ret = zx_wdt_set_nv((bool)arg);
#endif
#endif
			break;
		case ZX_WDT_GET_NV:
#ifdef CONFIG_PREEMPT_RT_FULL
#ifndef CONFIG_ARCH_ZX297520V3_CAP
			temp = zx_wdt_get_wdtnv_for_ctrm();
			*(bool *)arg = (bool)temp;
#endif
#endif
			break;
		case ZX_WDT_SET_CHECK:
			priv->is_check = (bool)arg;
			break;

		default:
			return -EPERM;
	}
	
	return ret;
}

static const struct file_operations zx_wdt_fops = {
	.owner		= THIS_MODULE,
	.unlocked_ioctl	= zx_wdt_ioctl,
	.open		= zx_wdt_open,
	.release	= zx_wdt_close,
};

static struct miscdevice zx_wdt_miscdev = {
	.minor		= MISC_DYNAMIC_MINOR,
	.name		= "zx_soft_wdt",
	.fops		= &zx_wdt_fops,
};

static void zx_wdt_icp_wake_cb(void *buf, unsigned int len)
{	
	volatile int wdt_icp_buf = 0;
	
	wdt_icp_buf = *(int *)(buf);

	if (zx_wdt_get_time_out() <= zx_wdt_get_global_cnt())
	{
	    printk(KERN_ERR"WDT ERR: timeout(%d),current(%d)\n", zx_wdt_get_time_out(), zx_wdt_get_global_cnt());
		BUG();
	}
}

#define TEST_CNTTT  20
volatile u32 test_flagggg1=0;
volatile u32 test_flagggg2=0;
volatile u32 test_timeglobal[TEST_CNTTT];
volatile u32 test_timeps[TEST_CNTTT];

void zx_wdt_icp_wake(void)
{	
	test_timeps[test_flagggg1] = zx_wdt_get_time_out();
	test_timeglobal[test_flagggg1] = zx_wdt_get_global_cnt();
	test_flagggg1++;
	if(test_flagggg1>=TEST_CNTTT) 
		test_flagggg1=0;
	
	if (zx_wdt_get_time_out() <= zx_wdt_get_global_cnt())
	{
		test_flagggg2++;
	   	// printk(KERN_ERR"WDT ERR: timeout(%d),current(%d)\n", zx_wdt_get_time_out(), zx_wdt_get_global_cnt());
		panic("wdt feed is delayed by others!!!!!\n "); //BUG(); //bug cost too long time so it will restart sometime
	}
}
EXPORT_SYMBOL(zx_wdt_icp_wake);

#ifdef CONFIG_WATCHDOG_RESTART
void wdt_restart(void)
{
	volatile u32 tmp = 0;

	#ifdef CONFIG_SYSTEM_RECOVERY
	//reset
	zx_write_reg(ZX_TOP_CRM_BASE+0x2c, zx_read_reg(ZX_TOP_CRM_BASE+0x2c)|0x3);  
	//clk sel default, clk div
	tmp =zx_read_reg(ZX_RM_WDT_BASE + 0x04);
	tmp &=~(0xff<<8);
	tmp |=(31<<8);
	zx_write_reg(ZX_RM_WDT_BASE + 0x04, 0x12340000|tmp);	
	
	#else
	/*modify global*/
	if(zx_read_reg(WDT_GLOBAL_COUNT_ADDR)< 10)
		zx_write_reg(WDT_PS_TIMEOUT_ADDR, 0);
	else
		zx_write_reg(WDT_PS_TIMEOUT_ADDR, zx_read_reg(WDT_GLOBAL_COUNT_ADDR)-10);
	#endif
	
	/*stop wdt*/
	zx_write_reg(ZX_RM_WDT_BASE + 0x1c, 0x12340000);
	
	/*set value int */
	zx_write_reg(ZX_RM_WDT_BASE + 0x14, 0x12340000|0x8);

	/*set load value*/
	zx_write_reg(ZX_RM_WDT_BASE + 0x08, 0x12340000|0x10);
	
	/*refresh config*/		
	tmp =zx_read_reg(ZX_RM_WDT_BASE + 0x18);
	tmp ^=0x3f;
	zx_write_reg(ZX_RM_WDT_BASE + 0x18, 0x12340000|tmp);
	
   	/*wait for refresh ack*/
	while(1)
	{
		if(((zx_read_reg(ZX_RM_WDT_BASE + 0x10)>>4)&0x3f) == tmp)
			break;
	}

	/*start  wdt*/
	zx_write_reg(ZX_RM_WDT_BASE + 0x1c, 0x12340001);
	while(1);
}
EXPORT_SYMBOL(wdt_restart);
#endif

static void zx_wdt_process_handle(void)
{	
	struct list_head *pos;
	struct soft_wdt_file_private *priv;

    g_wdt_wakeup_flag = false;
    g_wdt_wakeup_min_time = MAX_SLEEP_TIME;
    
	spin_lock(&zx_wdt_lock);
	list_for_each(pos, &zx_wdt_list) {
		priv = container_of(pos, struct soft_wdt_file_private, list);

		if (priv->is_check == false) {
			continue;
		}
		
		if (priv->wakeup) {
            g_wdt_wakeup_flag = true;
			
            if (g_wdt_wakeup_min_time > priv->interval) {
                g_wdt_wakeup_min_time = priv->interval;
            }
        }
	
		if (zx_wdt_get_global_cnt() > priv->handle_timeout) {
		    priv->handle_timeout_cnt++;

		    if (priv->handle_timeout_cnt >= 3) {
    			printk(KERN_ERR"[zx soft wdt]: zx soft wdt handle time-out(thread name:%s, timeout val = %d)!\n", priv->current_task->comm, priv->handle_timeout);
    			BUG();
			}
		}
	}
	spin_unlock(&zx_wdt_lock);
}

static int zx_wdt_thread(void * arg)
{
	struct sched_param param = { .sched_priority = g_wdt_priority };
	
	sched_setscheduler(current, SCHED_FIFO, &param);
	
	while (1) {
		zx_wdt_process_handle();
			
        g_wdt_sleep_pre_time = cpu_clock(0)>>10;
		if (g_wdt_nv == WDT_ON) {
        	//printk(KERN_ERR"ZXWDT Global Count = %d, time=%dus\n", zx_wdt_get_global_cnt(), g_wdt_sleep_pre_time);
		}
        
		zx_wdt_set_time_out(zx_wdt_get_global_cnt() + WDT_SLEEP_TIME + WDT_MARGIN);		
		//msleep(WDT_SLEEP_TIME * 1000);
		usleep_range(WDT_SLEEP_TIME * 1000*1000, (WDT_SLEEP_TIME+1) * 1000*1000);
	}

	return 0;
}

#ifdef ZX_WDT_DBG
int wdt_test_open_handle = -1;
static void zx_wdt_test_thread(void * arg)
{
	msleep(5000);
		
	wdt_test_open_handle = sys_open(ZX_SOFT_WDT_DEV, O_RDWR, 0);
	printk(KERN_ERR"[zx soft wdt]:open success ret = %d \n",wdt_test_open_handle);
	sys_ioctl(wdt_test_open_handle, ZX_WDT_SET_INTERNAL, 12);
	printk(KERN_ERR"[zx soft wdt]:set internal \n");

	while (1) {
		sys_ioctl(wdt_test_open_handle, ZX_WDT_FEED_DOG, 30);
		printk(KERN_ERR"[zx soft wdt]:feed dog \n");
		msleep(5000);
	}
}
#endif

#ifdef CONFIG_ZX29_WDT_TEST_MODULE
#ifndef CONFIG_ARCH_ZX297520V3_CAP
void zx_wdt_test(u32 core_id)
{
	int ret1=0;
	T_ZDrvRpMsg_Msg  test_Msg={0};
	u32  test_buf = 0x74736574; /*ASCII:"test"*/

	if(0==core_id){  //m0  wdt test
		test_Msg.actorID = M0_ID;
		test_Msg.chID = channel_2;
		test_Msg.flag = 1;
		test_Msg.buf = (u32 *)&test_buf;	
		test_Msg.len = 4;
		pr_info("[zx soft wdt]: m0 wdt test!!! \n");	

		ret1 = CPPS_FUNC(cpps_callbacks, zDrvRpMsg_Write)(&test_Msg);

	}else if(1==core_id){//phy wdt test
		test_Msg.actorID = ICP_MSG_ACTOR_ZSP;
		test_Msg.chID = channel_32;   //RP_MSG_W_PHY_PS_WAKEUP;
		test_Msg.flag = 1;
		test_Msg.buf = (u32 *)&test_buf;	
		test_Msg.len = 4;
		pr_info("[zx soft wdt]: phy wdt test!!! \n");	

		ret1 = CPPS_FUNC(cpps_callbacks, zDrvRpMsg_Write)(&test_Msg);

	}else if(2==core_id){//ps wdt test
		pr_info("[zx soft wdt]: ps wdt test!!! \n");	
		local_irq_disable();
		while(1);
	}
	#ifdef _USE_CAP_SYS
	else if(3==core_id){//cap wdt test
		test_Msg.actorID = CAP_ID;
		test_Msg.chID = channel_2;   
		test_Msg.flag = 1;
		test_Msg.buf = (u32 *)&test_buf;	
		test_Msg.len = 4;
		pr_info("[zx soft wdt]: cap wdt test!!! \n");	

		ret1 =zDrvRpMsg_Write_Cap(&test_Msg);

	}
	#endif
	else {
		
	}


	if(ret1 < 0)
	{
		pr_info("[zx soft wdt]: zDrvRpMsg_Write ERROR!!! \n");
    		BUG();
	}

}

EXPORT_SYMBOL(zx_wdt_test);
#endif
#endif

#ifdef CONFIG_ARCH_ZX297520V3_CAP
static void zx_wdt_icp_test_cb(void *buf, unsigned int len)
{	
	volatile int wdt_icp_buf = 0;
	
	wdt_icp_buf = *(int *)(buf);
	if(0x74736574==wdt_icp_buf) {
		local_irq_disable();
		while(1);
	}
}
#endif

static int  zx_wdt_init(void)
{
	int err = 0;

#ifdef CONFIG_PREEMPT_RT_FULL
#ifndef CONFIG_ARCH_ZX297520V3_CAP
	err = zx_wdt_get_nv();
#endif	
	if (err) {
		printk(KERN_ERR"[zx soft wdt]: zx_wdt_get_nv failed (err=%d)!\n", err);
		return -EPERM;
	}
#endif

#ifndef CONFIG_ARCH_ZX297520V3_CAP
	err = CPPS_FUNC(cpps_callbacks, zDrvRpMsg_CreateChannel)(M0_ID, channel_2, 0x20);
#else
	err = zDrvRpMsg_CreateChannel(M0_ID, channel_2, 0x20);
#endif
	if (err) {
		printk(KERN_ERR"[zx soft wdt]: Fail to create AP 2 M0 icp channel 2 used by soft wdt (err=%d)!\n", err);
		return -EPERM;
	}
	
#ifndef CONFIG_ARCH_ZX297520V3_CAP
	err = CPPS_FUNC(cpps_callbacks, zDrvRpMsg_RegCallBack)(M0_ID, channel_2, zx_wdt_icp_wake_cb);
#else
	err = zDrvRpMsg_RegCallBack(M0_ID, channel_2, zx_wdt_icp_wake_cb);
#endif	
	if (err) {
		printk(KERN_ERR"[zx soft wdt]: Fail to register M0 icp callback (err=%d)!\n", err);
		return -EPERM;
	}

#ifdef CONFIG_ARCH_ZX297520V3_CAP
	err = zDrvRpMsg_CreateChannel(AP_ID, channel_2, 0x20);
	if (err) {
		printk(KERN_ERR"[zx soft wdt]: Fail to create CAP 2 AP icp channel 2 used by soft wdt (err=%d)!\n", err);
		return -EPERM;
	}
	err = zDrvRpMsg_RegCallBack(AP_ID, channel_2, zx_wdt_icp_test_cb);
	if (err) {
		printk(KERN_ERR"[zx soft wdt]: Fail to register AP icp callback (err=%d)!\n", err);
		return -EPERM;
	}	
#endif

#if defined(_USE_CAP_SYS) && !defined(CONFIG_ARCH_ZX297520V3_CAP)
	err = zDrvRpMsg_CreateChannel_Cap(CAP_ID, channel_2, 0x10);
	if (err) {
		printk(KERN_ERR"[zx soft wdt]: Fail to AP 2 CAP create chan (err=%d)!\n", err);
		return -EPERM;
	}
#endif

	err = misc_register(&zx_wdt_miscdev);
	if (err != 0) {
		printk(KERN_ERR"[zx soft wdt]: cannot register miscdev on minor=%d (err=%d)\n", WATCHDOG_MINOR, err);
		return err;
	}
	
	err = (int)kthread_run(zx_wdt_thread, NULL, "zx_wdt_thread");
	if (IS_ERR((const void *)err)) {
		printk(KERN_ERR"[zx soft wdt]: Fail to create and run soft wdt thread!\n");
        return PTR_ERR((const void *)err);
    }

#ifdef ZX_WDT_DBG
	err = kthread_run(zx_wdt_test_thread, NULL, "zx_wdt_test_thread");
	if (IS_ERR((const void *)err)) {
		printk(KERN_ERR"[zx soft wdt]: Fail to create and run soft wdt test thread.");
        return PTR_ERR((const void *)err);
    }
	printk("[zx soft wdt]: Success to create and run soft wdt test thread!!! \n");
#endif

	printk("[zx soft wdt]: zx_wdt_init  Success!!! \n");

	return 0;
}

static int __init zx_softwdt_init(void)
{
	#ifdef CONFIG_ARCH_ZX297520V3_CAP
	zx_wdt_init();
	#else
	cpps_init2_register(zx_wdt_init);
	#endif
	return 0;
}


static void __exit zx_soft_wdt_exit(void)
{
	misc_deregister(&zx_wdt_miscdev);
}

#ifndef CONFIG_SYSTEM_RECOVERY
//late_initcall_sync(zx_softwdt_init);
//module_exit(zx_soft_wdt_exit);
module_init(zx_softwdt_init);
module_exit(zx_soft_wdt_exit);
#endif

