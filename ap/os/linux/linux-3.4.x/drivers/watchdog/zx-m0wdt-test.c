/*
 * ZTE ddr driver
 *
 * Copyright (C) 2013 ZTE Ltd.
 * 	by tsp
 *
 */
#ifdef CONFIG_ZX29_WDT_TEST_MODULE

#include <linux/module.h>
#include <linux/kernel.h>
#include <linux/init.h>
#include <linux/spinlock.h>
#include <linux/interrupt.h>
#include <linux/types.h>
#include <linux/sched.h>
#include <linux/io.h>
#include <mach/iomap.h>
#include <linux/slab.h>
#include <linux/syscalls.h>
#include <asm/uaccess.h>
#include <linux/zx_soft_wdt.h>
#include <linux/miscdevice.h>	/* For handling misc devices */
#include <linux/soc/zte/rpm/rpmsg.h> /* For icp operation*/

extern void zx_wdt_test(u32 core_id);

static void __init zx_m0wdttest_init(void)
{	
	//m0 wdt test
	zx_wdt_test(0);
	
}

static void __exit zx_m0wdttest_exit(void)
{
    //misc_deregister(&zx_ddr_miscdev);
}


module_init(zx_m0wdttest_init);
module_exit(zx_m0wdttest_exit);
#endif
