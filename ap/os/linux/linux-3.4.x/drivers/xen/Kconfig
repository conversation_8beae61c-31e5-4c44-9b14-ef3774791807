menu "Xen driver support"
	depends on XEN

config XEN_BALLOON
	bool "Xen memory balloon driver"
	default y
	help
	  The balloon driver allows the Xen domain to request more memory from
	  the system to expand the domain's memory allocation, or alternatively
	  return unneeded memory to the system.

config XEN_SELFBALLOONING
	bool "Dynamically self-balloon kernel memory to target"
	depends on XEN && XEN_BALLOON && CLEANCACHE && SWAP && XEN_TMEM
	default n
	help
	  Self-ballooning dynamically balloons available kernel memory driven
	  by the current usage of anonymous memory ("committed AS") and
	  controlled by various sysfs-settable parameters.  Configuring
	  FRONTSWAP is highly recommended; if it is not configured, self-
	  ballooning is disabled by default but can be enabled with the
	  'selfballooning' kernel boot parameter.  If FRONTSWAP is configured,
	  frontswap-selfshrinking is enabled by default but can be disabled
	  with the 'noselfshrink' kernel boot parameter; and self-ballooning
	  is enabled by default but can be disabled with the 'noselfballooning'
	  kernel boot parameter.  Note that systems without a sufficiently
	  large swap device should not enable self-ballooning.

config XEN_BALLOON_MEMORY_HOTPLUG
	bool "Memory hotplug support for Xen balloon driver"
	default n
	depends on XEN_BALLOON && MEMORY_HOTPLUG
	help
	  Memory hotplug support for Xen balloon driver allows expanding memory
	  available for the system above limit declared at system startup.
	  It is very useful on critical systems which require long
	  run without rebooting.

	  Memory could be hotplugged in following steps:

	    1) dom0: xl mem-max <domU> <maxmem>
	       where <maxmem> is >= requested memory size,

	    2) dom0: xl mem-set <domU> <memory>
	       where <memory> is requested memory size; alternatively memory
	       could be added by writing proper value to
	       /sys/devices/system/xen_memory/xen_memory0/target or
	       /sys/devices/system/xen_memory/xen_memory0/target_kb on dumU,

	    3) domU: for i in /sys/devices/system/memory/memory*/state; do \
	               [ "`cat "$i"`" = offline ] && echo online > "$i"; done

	  Memory could be onlined automatically on domU by adding following line to udev rules:

	  SUBSYSTEM=="memory", ACTION=="add", RUN+="/bin/sh -c '[ -f /sys$devpath/state ] && echo online > /sys$devpath/state'"

	  In that case step 3 should be omitted.

config XEN_SCRUB_PAGES
	bool "Scrub pages before returning them to system"
	depends on XEN_BALLOON
	default y
	help
	  Scrub pages before returning them to the system for reuse by
	  other domains.  This makes sure that any confidential data
	  is not accidentally visible to other domains.  Is it more
	  secure, but slightly less efficient.
	  If in doubt, say yes.

config XEN_DEV_EVTCHN
	tristate "Xen /dev/xen/evtchn device"
	default y
	help
	  The evtchn driver allows a userspace process to triger event
	  channels and to receive notification of an event channel
	  firing.
	  If in doubt, say yes.

config XEN_BACKEND
	bool "Backend driver support"
	depends on XEN_DOM0
	default y
	help
	  Support for backend device drivers that provide I/O services
	  to other virtual machines.

config XENFS
	tristate "Xen filesystem"
	select XEN_PRIVCMD
	default y
	help
	  The xen filesystem provides a way for domains to share
	  information with each other and with the hypervisor.
	  For example, by reading and writing the "xenbus" file, guests
	  may pass arbitrary information to the initial domain.
	  If in doubt, say yes.

config XEN_COMPAT_XENFS
       bool "Create compatibility mount point /proc/xen"
       depends on XENFS
       default y
       help
         The old xenstore userspace tools expect to find "xenbus"
         under /proc/xen, but "xenbus" is now found at the root of the
         xenfs filesystem.  Selecting this causes the kernel to create
         the compatibility mount point /proc/xen if it is running on
         a xen platform.
         If in doubt, say yes.

config XEN_SYS_HYPERVISOR
       bool "Create xen entries under /sys/hypervisor"
       depends on SYSFS
       select SYS_HYPERVISOR
       default y
       help
         Create entries under /sys/hypervisor describing the Xen
	 hypervisor environment.  When running native or in another
	 virtual environment, /sys/hypervisor will still be present,
	 but will have no xen contents.

config XEN_XENBUS_FRONTEND
	tristate

config XEN_GNTDEV
	tristate "userspace grant access device driver"
	depends on XEN
	default m
	select MMU_NOTIFIER
	help
	  Allows userspace processes to use grants.

config XEN_GRANT_DEV_ALLOC
	tristate "User-space grant reference allocator driver"
	depends on XEN
	default m
	help
	  Allows userspace processes to create pages with access granted
	  to other domains. This can be used to implement frontend drivers
	  or as part of an inter-domain shared memory channel.

config SWIOTLB_XEN
	def_bool y
	depends on PCI
	select SWIOTLB

config XEN_TMEM
	bool
	default y if (CLEANCACHE || FRONTSWAP)
	help
	  Shim to interface in-kernel Transcendent Memory hooks
	  (e.g. cleancache and frontswap) to Xen tmem hypercalls.

config XEN_PCIDEV_BACKEND
	tristate "Xen PCI-device backend driver"
	depends on PCI && X86 && XEN
	depends on XEN_BACKEND
	default m
	help
	  The PCI device backend driver allows the kernel to export arbitrary
	  PCI devices to other guests. If you select this to be a module, you
	  will need to make sure no other driver has bound to the device(s)
	  you want to make visible to other guests.

	  The parameter "passthrough" allows you specify how you want the PCI
	  devices to appear in the guest. You can choose the default (0) where
	  PCI topology starts at 00.00.0, or (1) for passthrough if you want
	  the PCI devices topology appear the same as in the host.

	  The "hide" parameter (only applicable if backend driver is compiled
	  into the kernel) allows you to bind the PCI devices to this module
	  from the default device drivers. The argument is the list of PCI BDFs:
	  xen-pciback.hide=(03:00.0)(04:00.0)

	  If in doubt, say m.

config XEN_PRIVCMD
	tristate
	depends on XEN
	default m

config XEN_ACPI_PROCESSOR
	tristate "Xen ACPI processor"
	depends on XEN && X86 && ACPI_PROCESSOR && CPU_FREQ
	default m
	help
          This ACPI processor uploads Power Management information to the Xen
	  hypervisor.

	  To do that the driver parses the Power Management data and uploads
	  said information to the Xen hypervisor. Then the Xen hypervisor can
	  select the proper Cx and Pxx states. It also registers itslef as the
	  SMM so that other drivers (such as ACPI cpufreq scaling driver) will
	  not load.

          To compile this driver as a module, choose M here: the module will be
	  called xen_acpi_processor  If you do not know what to choose, select
	  M here. If the CPUFREQ drivers are built in, select Y here.

endmenu
