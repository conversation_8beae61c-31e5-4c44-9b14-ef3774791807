#
# Video configuration
#

menu "Graphics support"
	depends on HAS_IOMEM

config HAVE_FB_ATMEL
	bool

config SH_MIPI_DSI
	tristate
	depends on (SUPERH || ARCH_SHMOBILE) && HAVE_CLK

config SH_LCD_MIPI_DSI
	bool

source "drivers/char/agp/Kconfig"

source "drivers/gpu/vga/Kconfig"

source "drivers/gpu/drm/Kconfig"

source "drivers/gpu/stub/Kconfig"

config VGASTATE
       tristate
       default n

config VIDEO_OUTPUT_CONTROL
	tristate "Lowlevel video output switch controls"
	help
	  This framework adds support for low-level control of the video 
	  output switch.

menuconfig FB
	tristate "Support for frame buffer devices"
	---help---
	  The frame buffer device provides an abstraction for the graphics
	  hardware. It represents the frame buffer of some video hardware and
	  allows application software to access the graphics hardware through
	  a well-defined interface, so the software doesn't need to know
	  anything about the low-level (hardware register) stuff.

	  Frame buffer devices work identically across the different
	  architectures supported by Linux and make the implementation of
	  application programs easier and more portable; at this point, an X
	  server exists which uses the frame buffer device exclusively.
	  On several non-X86 architectures, the frame buffer device is the
	  only way to use the graphics hardware.

	  The device is accessed through special device nodes, usually located
	  in the /dev directory, i.e. /dev/fb*.

	  You need an utility program called fbset to make full use of frame
	  buffer devices. Please read <file:Documentation/fb/framebuffer.txt>
	  and the Framebuffer-HOWTO at
	  <http://www.munted.org.uk/programming/Framebuffer-HOWTO-1.3.html> for more
	  information.

	  Say Y here and to the driver for your graphics board below if you
	  are compiling a kernel for a non-x86 architecture.

	  If you are compiling for the x86 architecture, you can say Y if you
	  want to play with it, but it is not essential. Please note that
	  running graphical applications that directly touch the hardware
	  (e.g. an accelerated X server) and that are not frame buffer
	  device-aware may cause unexpected results. If unsure, say N.

config FIRMWARE_EDID
       bool "Enable firmware EDID"
       depends on FB
       default n
       ---help---
         This enables access to the EDID transferred from the firmware.
	 On the i386, this is from the Video BIOS. Enable this if DDC/I2C
	 transfers do not work for your driver and if you are using
	 nvidiafb, i810fb or savagefb.

	 In general, choosing Y for this option is safe.  If you
	 experience extremely long delays while booting before you get
	 something on your display, try setting this to N.  Matrox cards in
	 combination with certain motherboards and monitors are known to
	 suffer from this problem.

config FB_DDC
       tristate
       depends on FB
       select I2C_ALGOBIT
       select I2C
       default n

config FB_BOOT_VESA_SUPPORT
	bool
	depends on FB
	default n
	---help---
	  If true, at least one selected framebuffer driver can take advantage
	  of VESA video modes set at an early boot stage via the vga= parameter.

config FB_CFB_FILLRECT
	tristate
	depends on FB
	default n
	---help---
	  Include the cfb_fillrect function for generic software rectangle
	  filling. This is used by drivers that don't provide their own
	  (accelerated) version.

config FB_CFB_COPYAREA
	tristate
	depends on FB
	default n
	---help---
	  Include the cfb_copyarea function for generic software area copying.
	  This is used by drivers that don't provide their own (accelerated)
	  version.

config FB_CFB_IMAGEBLIT
	tristate
	depends on FB
	default n
	---help---
	  Include the cfb_imageblit function for generic software image
	  blitting. This is used by drivers that don't provide their own
	  (accelerated) version.

config FB_CFB_REV_PIXELS_IN_BYTE
	bool
	depends on FB
	default n
	---help---
	  Allow generic frame-buffer functions to work on displays with 1, 2
	  and 4 bits per pixel depths which has opposite order of pixels in
	  byte order to bytes in long order.

config FB_SYS_FILLRECT
	tristate
	depends on FB
	default n
	---help---
	  Include the sys_fillrect function for generic software rectangle
	  filling. This is used by drivers that don't provide their own
	  (accelerated) version and the framebuffer is in system RAM.

config FB_SYS_COPYAREA
	tristate
	depends on FB
	default n
	---help---
	  Include the sys_copyarea function for generic software area copying.
	  This is used by drivers that don't provide their own (accelerated)
	  version and the framebuffer is in system RAM.

config FB_SYS_IMAGEBLIT
	tristate
	depends on FB
	default n
	---help---
	  Include the sys_imageblit function for generic software image
	  blitting. This is used by drivers that don't provide their own
	  (accelerated) version and the framebuffer is in system RAM.

menuconfig FB_FOREIGN_ENDIAN
	bool "Framebuffer foreign endianness support"
	depends on FB
	---help---
	  This menu will let you enable support for the framebuffers with
	  non-native endianness (e.g. Little-Endian framebuffer on a
	  Big-Endian machine). Most probably you don't have such hardware,
	  so it's safe to say "n" here.

choice
	prompt "Choice endianness support"
	depends on FB_FOREIGN_ENDIAN

config FB_BOTH_ENDIAN
	bool "Support for Big- and Little-Endian framebuffers"

config FB_BIG_ENDIAN
	bool "Support for Big-Endian framebuffers only"

config FB_LITTLE_ENDIAN
	bool "Support for Little-Endian framebuffers only"

endchoice

config FB_SYS_FOPS
       tristate
       depends on FB
       default n

config FB_LCD_TE_ON
	bool
	depends on FB
config FB_WMT_GE_ROPS
	tristate
	depends on FB
	default n
	---help---
	  Include functions for accelerated rectangle filling and area
	  copying using WonderMedia Graphics Engine operations.

config FB_DEFERRED_IO
	bool
	depends on FB

config FB_HECUBA
	tristate
	depends on FB
	depends on FB_DEFERRED_IO

config FB_SVGALIB
	tristate
	depends on FB
	default n
	---help---
	  Common utility functions useful to fbdev drivers of VGA-based
	  cards.

config FB_MACMODES
       tristate
       depends on FB
       default n

config FB_BACKLIGHT
	bool
	depends on FB
	select BACKLIGHT_LCD_SUPPORT
	select BACKLIGHT_CLASS_DEVICE
	default n

config FB_MODE_HELPERS
        bool "Enable Video Mode Handling Helpers"
        depends on FB
	default n
	---help---
	  This enables functions for handling video modes using the
	  Generalized Timing Formula and the EDID parser. A few drivers rely
          on this feature such as the radeonfb, rivafb, and the i810fb. If
	  your driver does not take advantage of this feature, choosing Y will
	  just increase the kernel size by about 5K.

config FB_TILEBLITTING
       bool "Enable Tile Blitting Support"
       depends on FB
       default n
       ---help---
         This enables tile blitting.  Tile blitting is a drawing technique
	 where the screen is divided into rectangular sections (tiles), whereas
	 the standard blitting divides the screen into pixels. Because the
	 default drawing element is a tile, drawing functions will be passed
	 parameters in terms of number of tiles instead of number of pixels.
	 For example, to draw a single character, instead of using bitmaps,
	 an index to an array of bitmaps will be used.  To clear or move a
	 rectangular section of a screen, the rectangle will be described in
	 terms of number of tiles in the x- and y-axis.

	 This is particularly important to one driver, matroxfb.  If
	 unsure, say N.

comment "Frame buffer hardware drivers"
	depends on FB

config FB_GRVGA
	tristate "Aeroflex Gaisler framebuffer support"
	depends on FB && SPARC
	select FB_CFB_FILLRECT
	select FB_CFB_COPYAREA
	select FB_CFB_IMAGEBLIT
	---help---
	This enables support for the SVGACTRL framebuffer in the GRLIB IP library from Aeroflex Gaisler.

config FB_CIRRUS
	tristate "Cirrus Logic support"
	depends on FB && (ZORRO || PCI)
	select FB_CFB_FILLRECT
	select FB_CFB_COPYAREA
	select FB_CFB_IMAGEBLIT
	---help---
	  This enables support for Cirrus Logic GD542x/543x based boards on
	  Amiga: SD64, Piccolo, Picasso II/II+, Picasso IV, or EGS Spectrum.

	  If you have a PCI-based system, this enables support for these
	  chips: GD-543x, GD-544x, GD-5480.

	  Please read the file <file:Documentation/fb/cirrusfb.txt>.

	  Say N unless you have such a graphics board or plan to get one
	  before you next recompile the kernel.

config FB_PM2
	tristate "Permedia2 support"
	depends on FB && ((AMIGA && BROKEN) || PCI)
	select FB_CFB_FILLRECT
	select FB_CFB_COPYAREA
	select FB_CFB_IMAGEBLIT
	help
	  This is the frame buffer device driver for cards based on
	  the 3D Labs Permedia, Permedia 2 and Permedia 2V chips.
	  The driver was tested on the following cards:
		Diamond FireGL 1000 PRO AGP
		ELSA Gloria Synergy PCI
		Appian Jeronimo PRO (both heads) PCI
		3DLabs Oxygen ACX aka EONtronics Picasso P2 PCI
		Techsource Raptor GFX-8P (aka Sun PGX-32) on SPARC
		ASK Graphic Blaster Exxtreme AGP

	  To compile this driver as a module, choose M here: the
	  module will be called pm2fb.

config FB_PM2_FIFO_DISCONNECT
	bool "enable FIFO disconnect feature"
	depends on FB_PM2 && PCI
	help
	  Support the Permedia2 FIFO disconnect feature.

config FB_ARMCLCD
	tristate "ARM PrimeCell PL110 support"
	depends on FB && ARM && ARM_AMBA
	select FB_CFB_FILLRECT
	select FB_CFB_COPYAREA
	select FB_CFB_IMAGEBLIT
	help
	  This framebuffer device driver is for the ARM PrimeCell PL110
	  Colour LCD controller.  ARM PrimeCells provide the building
	  blocks for System on a Chip devices.

	  If you want to compile this as a module (=code which can be
	  inserted into and removed from the running kernel), say M
	  here and read <file:Documentation/kbuild/modules.txt>.  The module
	  will be called amba-clcd.

config FB_ACORN
	bool "Acorn VIDC support"
	depends on (FB = y) && ARM && ARCH_ACORN
	select FB_CFB_FILLRECT
	select FB_CFB_COPYAREA
	select FB_CFB_IMAGEBLIT
	help
	  This is the frame buffer device driver for the Acorn VIDC graphics
	  hardware found in Acorn RISC PCs and other ARM-based machines.  If
	  unsure, say N.

config FB_CLPS711X
	bool "CLPS711X LCD support"
	depends on (FB = y) && ARM && ARCH_CLPS711X
	select FB_CFB_FILLRECT
	select FB_CFB_COPYAREA
	select FB_CFB_IMAGEBLIT
	help
	  Say Y to enable the Framebuffer driver for the CLPS7111 and
	  EP7212 processors.

config FB_SA1100
	bool "SA-1100 LCD support"
	depends on (FB = y) && ARM && ARCH_SA1100
	select FB_CFB_FILLRECT
	select FB_CFB_COPYAREA
	select FB_CFB_IMAGEBLIT
	help
	  This is a framebuffer device for the SA-1100 LCD Controller.
	  See <http://www.linux-fbdev.org/> for information on framebuffer
	  devices.

	  If you plan to use the LCD display with your SA-1100 system, say
	  Y here.

config FB_IMX
	tristate "Freescale i.MX LCD support"
	depends on FB && IMX_HAVE_PLATFORM_IMX_FB
	select FB_CFB_FILLRECT
	select FB_CFB_COPYAREA
	select FB_CFB_IMAGEBLIT

config FB_CYBER2000
	tristate "CyberPro 2000/2010/5000 support"
	depends on FB && PCI && (BROKEN || !SPARC64)
	select FB_CFB_FILLRECT
	select FB_CFB_COPYAREA
	select FB_CFB_IMAGEBLIT
	help
	  This enables support for the Integraphics CyberPro 20x0 and 5000
	  VGA chips used in the Rebel.com Netwinder and other machines.
	  Say Y if you have a NetWinder or a graphics card containing this
	  device, otherwise say N.

config FB_CYBER2000_DDC
	bool "DDC for CyberPro support"
	depends on FB_CYBER2000
	select FB_DDC
	default y
	help
	  Say Y here if you want DDC support for your CyberPro graphics
	  card. This is only I2C bus support, driver does not use EDID.

config FB_CYBER2000_I2C
	bool "CyberPro 2000/2010/5000 I2C support"
	depends on FB_CYBER2000 && I2C && ARCH_NETWINDER
	select I2C_ALGOBIT
	help
	  Enable support for the I2C video decoder interface on the
	  Integraphics CyberPro 20x0 and 5000 VGA chips.  This is used
	  on the Netwinder machines for the SAA7111 video capture.

config FB_APOLLO
	bool
	depends on (FB = y) && APOLLO
	default y
	select FB_CFB_FILLRECT
	select FB_CFB_IMAGEBLIT

config FB_Q40
	bool
	depends on (FB = y) && Q40
	default y
	select FB_CFB_FILLRECT
	select FB_CFB_COPYAREA
	select FB_CFB_IMAGEBLIT

config FB_AMIGA
	tristate "Amiga native chipset support"
	depends on FB && AMIGA
	help
	  This is the frame buffer device driver for the builtin graphics
	  chipset found in Amigas.

	  To compile this driver as a module, choose M here: the
	  module will be called amifb.

config FB_AMIGA_OCS
	bool "Amiga OCS chipset support"
	depends on FB_AMIGA
	help
	  This enables support for the original Agnus and Denise video chips,
	  found in the Amiga 1000 and most A500's and A2000's. If you intend
	  to run Linux on any of these systems, say Y; otherwise say N.

config FB_AMIGA_ECS
	bool "Amiga ECS chipset support"
	depends on FB_AMIGA
	help
	  This enables support for the Enhanced Chip Set, found in later
	  A500's, later A2000's, the A600, the A3000, the A3000T and CDTV. If
	  you intend to run Linux on any of these systems, say Y; otherwise
	  say N.

config FB_AMIGA_AGA
	bool "Amiga AGA chipset support"
	depends on FB_AMIGA
	help
	  This enables support for the Advanced Graphics Architecture (also
	  known as the AGA or AA) Chip Set, found in the A1200, A4000, A4000T
	  and CD32. If you intend to run Linux on any of these systems, say Y;
	  otherwise say N.

config FB_FM2
	bool "Amiga FrameMaster II/Rainbow II support"
	depends on (FB = y) && ZORRO
	select FB_CFB_FILLRECT
	select FB_CFB_COPYAREA
	select FB_CFB_IMAGEBLIT
	help
	  This is the frame buffer device driver for the Amiga FrameMaster
	  card from BSC (exhibited 1992 but not shipped as a CBM product).

config FB_ARC
	tristate "Arc Monochrome LCD board support"
	depends on FB && X86
	select FB_SYS_FILLRECT
	select FB_SYS_COPYAREA
	select FB_SYS_IMAGEBLIT
	select FB_SYS_FOPS
	help
	  This enables support for the Arc Monochrome LCD board. The board
	  is based on the KS-108 lcd controller and is typically a matrix
	  of 2*n chips. This driver was tested with a 128x64 panel. This
	  driver supports it for use with x86 SBCs through a 16 bit GPIO
	  interface (8 bit data, 8 bit control). If you anticipate using
	  this driver, say Y or M; otherwise say N. You must specify the
	  GPIO IO address to be used for setting control and data.

config FB_ATARI
	bool "Atari native chipset support"
	depends on (FB = y) && ATARI
	select FB_CFB_FILLRECT
	select FB_CFB_COPYAREA
	select FB_CFB_IMAGEBLIT
	help
	  This is the frame buffer device driver for the builtin graphics
	  chipset found in Ataris.

config FB_OF
	bool "Open Firmware frame buffer device support"
	depends on (FB = y) && (PPC64 || PPC_OF) && (!PPC_PSERIES || PCI)
	select FB_CFB_FILLRECT
	select FB_CFB_COPYAREA
	select FB_CFB_IMAGEBLIT
	select FB_MACMODES
	help
	  Say Y if you want support with Open Firmware for your graphics
	  board.

config FB_CONTROL
	bool "Apple \"control\" display support"
	depends on (FB = y) && PPC_PMAC && PPC32
	select FB_CFB_FILLRECT
	select FB_CFB_COPYAREA
	select FB_CFB_IMAGEBLIT
	select FB_MACMODES
	help
	  This driver supports a frame buffer for the graphics adapter in the
	  Power Macintosh 7300 and others.

config FB_PLATINUM
	bool "Apple \"platinum\" display support"
	depends on (FB = y) && PPC_PMAC && PPC32
	select FB_CFB_FILLRECT
	select FB_CFB_COPYAREA
	select FB_CFB_IMAGEBLIT
	select FB_MACMODES
	help
	  This driver supports a frame buffer for the "platinum" graphics
	  adapter in some Power Macintoshes.

config FB_VALKYRIE
	bool "Apple \"valkyrie\" display support"
	depends on (FB = y) && (MAC || (PPC_PMAC && PPC32))
	select FB_CFB_FILLRECT
	select FB_CFB_COPYAREA
	select FB_CFB_IMAGEBLIT
	select FB_MACMODES
	help
	  This driver supports a frame buffer for the "valkyrie" graphics
	  adapter in some Power Macintoshes.

config FB_CT65550
	bool "Chips 65550 display support"
	depends on (FB = y) && PPC32 && PCI
	select FB_CFB_FILLRECT
	select FB_CFB_COPYAREA
	select FB_CFB_IMAGEBLIT
	help
	  This is the frame buffer device driver for the Chips & Technologies
	  65550 graphics chip in PowerBooks.

config FB_ASILIANT
	bool "Asiliant (Chips) 69000 display support"
	depends on (FB = y) && PCI
	select FB_CFB_FILLRECT
	select FB_CFB_COPYAREA
	select FB_CFB_IMAGEBLIT
	help
	  This is the frame buffer device driver for the Asiliant 69030 chipset

config FB_IMSTT
	bool "IMS Twin Turbo display support"
	depends on (FB = y) && PCI
	select FB_CFB_IMAGEBLIT
	select FB_MACMODES if PPC
	help
	  The IMS Twin Turbo is a PCI-based frame buffer card bundled with
	  many Macintosh and compatible computers.

config FB_VGA16
	tristate "VGA 16-color graphics support"
	depends on FB && (X86 || PPC)
	select FB_CFB_FILLRECT
	select FB_CFB_COPYAREA
	select FB_CFB_IMAGEBLIT
	select VGASTATE
	select FONT_8x16 if FRAMEBUFFER_CONSOLE
	help
	  This is the frame buffer device driver for VGA 16 color graphic
	  cards. Say Y if you have such a card.

	  To compile this driver as a module, choose M here: the
	  module will be called vga16fb.

config FB_BF54X_LQ043
	tristate "SHARP LQ043 TFT LCD (BF548 EZKIT)"
	depends on FB && (BF54x) && !BF542
	select FB_CFB_FILLRECT
	select FB_CFB_COPYAREA
	select FB_CFB_IMAGEBLIT
	help
	 This is the framebuffer device driver for a SHARP LQ043T1DG01 TFT LCD

config FB_BFIN_T350MCQB
	tristate "Varitronix COG-T350MCQB TFT LCD display (BF527 EZKIT)"
	depends on FB && BLACKFIN
	select BFIN_GPTIMERS
	select FB_CFB_FILLRECT
	select FB_CFB_COPYAREA
	select FB_CFB_IMAGEBLIT
	help
	 This is the framebuffer device driver for a Varitronix VL-PS-COG-T350MCQB-01 display TFT LCD
	 This display is a QVGA 320x240 24-bit RGB display interfaced by an 8-bit wide PPI
	 It uses PPI[0..7] PPI_FS1, PPI_FS2 and PPI_CLK.

config FB_BFIN_LQ035Q1
	tristate "SHARP LQ035Q1DH02 TFT LCD"
	depends on FB && BLACKFIN && SPI
	select FB_CFB_FILLRECT
	select FB_CFB_COPYAREA
	select FB_CFB_IMAGEBLIT
	select BFIN_GPTIMERS
	help
	  This is the framebuffer device driver for a SHARP LQ035Q1DH02 TFT display found on
	  the Blackfin Landscape LCD EZ-Extender Card.
	  This display is a QVGA 320x240 18-bit RGB display interfaced by an 16-bit wide PPI
	  It uses PPI[0..15] PPI_FS1, PPI_FS2 and PPI_CLK.

	  To compile this driver as a module, choose M here: the
	  module will be called bfin-lq035q1-fb.

config FB_BF537_LQ035
	tristate "SHARP LQ035 TFT LCD (BF537 STAMP)"
	depends on FB && (BF534 || BF536 || BF537) && I2C_BLACKFIN_TWI
	select FB_CFB_FILLRECT
	select FB_CFB_COPYAREA
	select FB_CFB_IMAGEBLIT
	select BFIN_GPTIMERS
	help
	  This is the framebuffer device for a SHARP LQ035Q7DB03 TFT LCD
	  attached to a BF537.

	  To compile this driver as a module, choose M here: the
	  module will be called bf537-lq035.

config FB_BFIN_7393
	tristate "Blackfin ADV7393 Video encoder"
	depends on FB && BLACKFIN
	select I2C
	select FB_CFB_FILLRECT
	select FB_CFB_COPYAREA
	select FB_CFB_IMAGEBLIT
	help
	  This is the framebuffer device for a ADV7393 video encoder
	  attached to a Blackfin on the PPI port.
	  If your Blackfin board has a ADV7393 select Y.

	  To compile this driver as a module, choose M here: the
	  module will be called bfin_adv7393fb.

choice
	prompt  "Video mode support"
	depends on FB_BFIN_7393
	default NTSC

config NTSC
	bool 'NTSC 720x480'

config PAL
	bool 'PAL 720x576'

config NTSC_640x480
	bool 'NTSC 640x480 (Experimental)'

config PAL_640x480
	bool 'PAL 640x480 (Experimental)'

config NTSC_YCBCR
	bool 'NTSC 720x480 YCbCR input'

config PAL_YCBCR
	bool 'PAL 720x576 YCbCR input'

endchoice

choice
	prompt  "Size of ADV7393 frame buffer memory Single/Double Size"
	depends on (FB_BFIN_7393)
	default ADV7393_1XMEM

config ADV7393_1XMEM
	bool 'Single'

config ADV7393_2XMEM
	bool 'Double'
endchoice

config FB_STI
	tristate "HP STI frame buffer device support"
	depends on FB && PARISC
	select FB_CFB_FILLRECT
	select FB_CFB_COPYAREA
	select FB_CFB_IMAGEBLIT
	select STI_CONSOLE
	select VT
	default y
	---help---
	  STI refers to the HP "Standard Text Interface" which is a set of
	  BIOS routines contained in a ROM chip in HP PA-RISC based machines.
	  Enabling this option will implement the linux framebuffer device
	  using calls to the STI BIOS routines for initialisation.
	
	  If you enable this option, you will get a planar framebuffer device
	  /dev/fb which will work on the most common HP graphic cards of the
	  NGLE family, including the artist chips (in the 7xx and Bxxx series),
	  HCRX, HCRX24, CRX, CRX24 and VisEG series.

	  It is safe to enable this option, so you should probably say "Y".

config FB_MAC
	bool "Generic Macintosh display support"
	depends on (FB = y) && MAC
	select FB_CFB_FILLRECT
	select FB_CFB_COPYAREA
	select FB_CFB_IMAGEBLIT
	select FB_MACMODES

config FB_HP300
	bool
	depends on (FB = y) && DIO
	select FB_CFB_IMAGEBLIT
	default y

config FB_TGA
	tristate "TGA/SFB+ framebuffer support"
	depends on FB && (ALPHA || TC)
	select FB_CFB_FILLRECT
	select FB_CFB_COPYAREA
	select FB_CFB_IMAGEBLIT
	select BITREVERSE
	---help---
	  This is the frame buffer device driver for generic TGA and SFB+
	  graphic cards.  These include DEC ZLXp-E1, -E2 and -E3 PCI cards,
	  also known as PBXGA-A, -B and -C, and DEC ZLX-E1, -E2 and -E3
	  TURBOchannel cards, also known as PMAGD-A, -B and -C.

	  Due to hardware limitations ZLX-E2 and E3 cards are not supported
	  for DECstation 5000/200 systems.  Additionally due to firmware
	  limitations these cards may cause troubles with booting DECstation
	  5000/240 and /260 systems, but are fully supported under Linux if
	  you manage to get it going. ;-)

	  Say Y if you have one of those.

config FB_UVESA
	tristate "Userspace VESA VGA graphics support"
	depends on FB && CONNECTOR
	select FB_CFB_FILLRECT
	select FB_CFB_COPYAREA
	select FB_CFB_IMAGEBLIT
	select FB_MODE_HELPERS
	help
	  This is the frame buffer driver for generic VBE 2.0 compliant
	  graphic cards. It can also take advantage of VBE 3.0 features,
	  such as refresh rate adjustment.

	  This driver generally provides more features than vesafb but
	  requires a userspace helper application called 'v86d'. See
	  <file:Documentation/fb/uvesafb.txt> for more information.

	  If unsure, say N.

config FB_VESA
	bool "VESA VGA graphics support"
	depends on (FB = y) && X86
	select FB_CFB_FILLRECT
	select FB_CFB_COPYAREA
	select FB_CFB_IMAGEBLIT
	select FB_BOOT_VESA_SUPPORT
	help
	  This is the frame buffer device driver for generic VESA 2.0
	  compliant graphic cards. The older VESA 1.2 cards are not supported.
	  You will get a boot time penguin logo at no additional cost. Please
	  read <file:Documentation/fb/vesafb.txt>. If unsure, say Y.

config FB_EFI
	bool "EFI-based Framebuffer Support"
	depends on (FB = y) && X86 && EFI
	select FB_CFB_FILLRECT
	select FB_CFB_COPYAREA
	select FB_CFB_IMAGEBLIT
	help
	  This is the EFI frame buffer device driver. If the firmware on
	  your platform is EFI 1.10 or UEFI 2.0, select Y to add support for
	  using the EFI framebuffer as your console.

config FB_N411
       tristate "N411 Apollo/Hecuba devkit support"
       depends on FB && X86 && MMU
       select FB_SYS_FILLRECT
       select FB_SYS_COPYAREA
       select FB_SYS_IMAGEBLIT
       select FB_SYS_FOPS
       select FB_DEFERRED_IO
       select FB_HECUBA
       help
         This enables support for the Apollo display controller in its
         Hecuba form using the n411 devkit.

config FB_HGA
	tristate "Hercules mono graphics support"
	depends on FB && X86
	help
	  Say Y here if you have a Hercules mono graphics card.

	  To compile this driver as a module, choose M here: the
	  module will be called hgafb.

	  As this card technology is at least 25 years old,
	  most people will answer N here.

config FB_SGIVW
	tristate "SGI Visual Workstation framebuffer support"
	depends on FB && X86_VISWS
	select FB_CFB_FILLRECT
	select FB_CFB_COPYAREA
	select FB_CFB_IMAGEBLIT
	help
	  SGI Visual Workstation support for framebuffer graphics.

config FB_GBE
	bool "SGI Graphics Backend frame buffer support"
	depends on (FB = y) && (SGI_IP32 || X86_VISWS)
	select FB_CFB_FILLRECT
	select FB_CFB_COPYAREA
	select FB_CFB_IMAGEBLIT
 	help
	  This is the frame buffer device driver for SGI Graphics Backend.
	  This chip is used in SGI O2 and Visual Workstation 320/540.

config FB_GBE_MEM
	int "Video memory size in MB"
	depends on FB_GBE
	default 4
	help
	  This is the amount of memory reserved for the framebuffer,
	  which can be any value between 1MB and 8MB.

config FB_SBUS
	bool "SBUS and UPA framebuffers"
	depends on (FB = y) && SPARC
	help
	  Say Y if you want support for SBUS or UPA based frame buffer device.

config FB_BW2
	bool "BWtwo support"
	depends on (FB = y) && (SPARC && FB_SBUS)
	select FB_CFB_FILLRECT
	select FB_CFB_COPYAREA
	select FB_CFB_IMAGEBLIT
	help
	  This is the frame buffer device driver for the BWtwo frame buffer.

config FB_CG3
	bool "CGthree support"
	depends on (FB = y) && (SPARC && FB_SBUS)
	select FB_CFB_FILLRECT
	select FB_CFB_COPYAREA
	select FB_CFB_IMAGEBLIT
	help
	  This is the frame buffer device driver for the CGthree frame buffer.

config FB_CG6
	bool "CGsix (GX,TurboGX) support"
	depends on (FB = y) && (SPARC && FB_SBUS)
	select FB_CFB_COPYAREA
	select FB_CFB_IMAGEBLIT
	help
	  This is the frame buffer device driver for the CGsix (GX, TurboGX)
	  frame buffer.

config FB_FFB
	bool "Creator/Creator3D/Elite3D support"
	depends on FB_SBUS && SPARC64
	select FB_CFB_COPYAREA
	select FB_CFB_IMAGEBLIT
	help
	  This is the frame buffer device driver for the Creator, Creator3D,
	  and Elite3D graphics boards.

config FB_TCX
	bool "TCX (SS4/SS5 only) support"
	depends on FB_SBUS
	select FB_CFB_FILLRECT
	select FB_CFB_COPYAREA
	select FB_CFB_IMAGEBLIT
	help
	  This is the frame buffer device driver for the TCX 24/8bit frame
	  buffer.

config FB_CG14
	bool "CGfourteen (SX) support"
	depends on FB_SBUS
	select FB_CFB_FILLRECT
	select FB_CFB_COPYAREA
	select FB_CFB_IMAGEBLIT
	help
	  This is the frame buffer device driver for the CGfourteen frame
	  buffer on Desktop SPARCsystems with the SX graphics option.

config FB_P9100
	bool "P9100 (Sparcbook 3 only) support"
	depends on FB_SBUS
	select FB_CFB_FILLRECT
	select FB_CFB_COPYAREA
	select FB_CFB_IMAGEBLIT
	help
	  This is the frame buffer device driver for the P9100 card
	  supported on Sparcbook 3 machines.

config FB_LEO
	bool "Leo (ZX) support"
	depends on FB_SBUS
	select FB_CFB_FILLRECT
	select FB_CFB_COPYAREA
	select FB_CFB_IMAGEBLIT
	help
	  This is the frame buffer device driver for the SBUS-based Sun ZX
	  (leo) frame buffer cards.

config FB_IGA
	bool "IGA 168x display support"
	depends on (FB = y) && SPARC32
	select FB_CFB_FILLRECT
	select FB_CFB_COPYAREA
	select FB_CFB_IMAGEBLIT
	help
	  This is the framebuffer device for the INTERGRAPHICS 1680 and
	  successor frame buffer cards.

config FB_XVR500
	bool "Sun XVR-500 3DLABS Wildcat support"
	depends on (FB = y) && PCI && SPARC64
	select FB_CFB_FILLRECT
	select FB_CFB_COPYAREA
	select FB_CFB_IMAGEBLIT
	help
	  This is the framebuffer device for the Sun XVR-500 and similar
	  graphics cards based upon the 3DLABS Wildcat chipset.  The driver
	  only works on sparc64 systems where the system firmware has
	  mostly initialized the card already.  It is treated as a
	  completely dumb framebuffer device.

config FB_XVR2500
	bool "Sun XVR-2500 3DLABS Wildcat support"
	depends on (FB = y) && PCI && SPARC64
	select FB_CFB_FILLRECT
	select FB_CFB_COPYAREA
	select FB_CFB_IMAGEBLIT
	help
	  This is the framebuffer device for the Sun XVR-2500 and similar
	  graphics cards based upon the 3DLABS Wildcat chipset.  The driver
	  only works on sparc64 systems where the system firmware has
	  mostly initialized the card already.  It is treated as a
	  completely dumb framebuffer device.

config FB_XVR1000
	bool "Sun XVR-1000 support"
	depends on (FB = y) && SPARC64
	select FB_CFB_FILLRECT
	select FB_CFB_COPYAREA
	select FB_CFB_IMAGEBLIT
	help
	  This is the framebuffer device for the Sun XVR-1000 and similar
	  graphics cards.  The driver only works on sparc64 systems where
	  the system firmware has mostly initialized the card already.  It
	  is treated as a completely dumb framebuffer device.

config FB_PVR2
	tristate "NEC PowerVR 2 display support"
	depends on FB && SH_DREAMCAST
	select FB_CFB_FILLRECT
	select FB_CFB_COPYAREA
	select FB_CFB_IMAGEBLIT
	---help---
	  Say Y here if you have a PowerVR 2 card in your box.  If you plan to
	  run linux on your Dreamcast, you will have to say Y here.
	  This driver may or may not work on other PowerVR 2 cards, but is
	  totally untested.  Use at your own risk.  If unsure, say N.

	  To compile this driver as a module, choose M here: the
	  module will be called pvr2fb.

	  You can pass several parameters to the driver at boot time or at
	  module load time.  The parameters look like "video=pvr2:XXX", where
	  the meaning of XXX can be found at the end of the main source file
	  (<file:drivers/video/pvr2fb.c>). Please see the file
	  <file:Documentation/fb/pvr2fb.txt>.

config FB_EPSON1355
	bool "Epson 1355 framebuffer support"
	depends on (FB = y) && ARCH_CEIVA
	select FB_CFB_FILLRECT
	select FB_CFB_COPYAREA
	select FB_CFB_IMAGEBLIT
	help
	  Build in support for the SED1355 Epson Research Embedded RAMDAC
	  LCD/CRT Controller (since redesignated as the S1D13505) as a
	  framebuffer.  Product specs at
	  <http://vdc.epson.com/>.

config FB_S1D13XXX
	tristate "Epson S1D13XXX framebuffer support"
	depends on FB
	select FB_CFB_FILLRECT
	select FB_CFB_COPYAREA
	select FB_CFB_IMAGEBLIT
	help
	  Support for S1D13XXX framebuffer device family (currently only
	  working with S1D13806). Product specs at
	  <http://vdc.epson.com/>

config FB_ATMEL
	tristate "AT91/AT32 LCD Controller support"
	depends on FB && HAVE_FB_ATMEL
	select FB_CFB_FILLRECT
	select FB_CFB_COPYAREA
	select FB_CFB_IMAGEBLIT
	help
	  This enables support for the AT91/AT32 LCD Controller.

config FB_INTSRAM
	bool "Frame Buffer in internal SRAM"
	depends on FB_ATMEL && ARCH_AT91SAM9261
	help
	  Say Y if you want to map Frame Buffer in internal SRAM. Say N if you want
	  to let frame buffer in external SDRAM.

config FB_ATMEL_STN
	bool "Use a STN display with AT91/AT32 LCD Controller"
	depends on FB_ATMEL && (MACH_AT91SAM9261EK || MACH_AT91SAM9G10EK)
	default n
	help
	  Say Y if you want to connect a STN LCD display to the AT91/AT32 LCD
	  Controller. Say N if you want to connect a TFT.

	  If unsure, say N.

config FB_NVIDIA
	tristate "nVidia Framebuffer Support"
	depends on FB && PCI
	select FB_BACKLIGHT if FB_NVIDIA_BACKLIGHT
	select FB_MODE_HELPERS
	select FB_CFB_FILLRECT
	select FB_CFB_COPYAREA
	select FB_CFB_IMAGEBLIT
	select BITREVERSE
	select VGASTATE
	help
	  This driver supports graphics boards with the nVidia chips, TNT
	  and newer. For very old chipsets, such as the RIVA128, then use
	  the rivafb.
	  Say Y if you have such a graphics board.

	  To compile this driver as a module, choose M here: the
	  module will be called nvidiafb.

config FB_NVIDIA_I2C
       bool "Enable DDC Support"
       depends on FB_NVIDIA
       select FB_DDC
       help
	  This enables I2C support for nVidia Chipsets.  This is used
	  only for getting EDID information from the attached display
	  allowing for robust video mode handling and switching.

	  Because fbdev-2.6 requires that drivers must be able to
	  independently validate video mode parameters, you should say Y
	  here.

config FB_NVIDIA_DEBUG
	bool "Lots of debug output"
	depends on FB_NVIDIA
	default n
	help
	  Say Y here if you want the nVidia driver to output all sorts
	  of debugging information to provide to the maintainer when
	  something goes wrong.

config FB_NVIDIA_BACKLIGHT
	bool "Support for backlight control"
	depends on FB_NVIDIA
	default y
	help
	  Say Y here if you want to control the backlight of your display.

config FB_RIVA
	tristate "nVidia Riva support"
	depends on FB && PCI
	select FB_BACKLIGHT if FB_RIVA_BACKLIGHT
	select FB_MODE_HELPERS
	select FB_CFB_FILLRECT
	select FB_CFB_COPYAREA
	select FB_CFB_IMAGEBLIT
	select BITREVERSE
	select VGASTATE
	help
	  This driver supports graphics boards with the nVidia Riva/Geforce
	  chips.
	  Say Y if you have such a graphics board.

	  To compile this driver as a module, choose M here: the
	  module will be called rivafb.

config FB_RIVA_I2C
       bool "Enable DDC Support"
       depends on FB_RIVA
       select FB_DDC
       help
	  This enables I2C support for nVidia Chipsets.  This is used
	  only for getting EDID information from the attached display
	  allowing for robust video mode handling and switching.

	  Because fbdev-2.6 requires that drivers must be able to
	  independently validate video mode parameters, you should say Y
	  here.

config FB_RIVA_DEBUG
	bool "Lots of debug output"
	depends on FB_RIVA
	default n
	help
	  Say Y here if you want the Riva driver to output all sorts
	  of debugging information to provide to the maintainer when
	  something goes wrong.

config FB_RIVA_BACKLIGHT
	bool "Support for backlight control"
	depends on FB_RIVA
	default y
	help
	  Say Y here if you want to control the backlight of your display.

config FB_I740
	tristate "Intel740 support (EXPERIMENTAL)"
	depends on EXPERIMENTAL && FB && PCI
	select FB_MODE_HELPERS
	select FB_CFB_FILLRECT
	select FB_CFB_COPYAREA
	select FB_CFB_IMAGEBLIT
	select VGASTATE
	select FB_DDC
	help
	  This driver supports graphics cards based on Intel740 chip.

config FB_I810
	tristate "Intel 810/815 support (EXPERIMENTAL)"
	depends on EXPERIMENTAL && FB && PCI && X86_32 && AGP_INTEL
	select FB_MODE_HELPERS
	select FB_CFB_FILLRECT
	select FB_CFB_COPYAREA
	select FB_CFB_IMAGEBLIT
	select VGASTATE
	help
	  This driver supports the on-board graphics built in to the Intel 810 
          and 815 chipsets.  Say Y if you have and plan to use such a board.

          To compile this driver as a module, choose M here: the
	  module will be called i810fb.

          For more information, please read 
	  <file:Documentation/fb/intel810.txt>

config FB_I810_GTF
	bool "use VESA Generalized Timing Formula"
	depends on FB_I810
	help
	  If you say Y, then the VESA standard, Generalized Timing Formula 
          or GTF, will be used to calculate the required video timing values
	  per video mode.  Since the GTF allows nondiscrete timings 
          (nondiscrete being a range of values as opposed to discrete being a
          set of values), you'll be able to use any combination of horizontal 
	  and vertical resolutions, and vertical refresh rates without having
	  to specify your own timing parameters.  This is especially useful
	  to maximize the performance of an aging display, or if you just 
          have a display with nonstandard dimensions. A VESA compliant 
	  monitor is recommended, but can still work with non-compliant ones.
	  If you need or want this, then select this option. The timings may 
	  not be compliant with Intel's recommended values. Use at your own 
	  risk.

          If you say N, the driver will revert to discrete video timings 
	  using a set recommended by Intel in their documentation.
  
          If unsure, say N.

config FB_I810_I2C
	bool "Enable DDC Support"
	depends on FB_I810 && FB_I810_GTF
	select FB_DDC
	help

config FB_LE80578
	tristate "Intel LE80578 (Vermilion) support"
	depends on FB && PCI && X86
	select FB_MODE_HELPERS
	select FB_CFB_FILLRECT
	select FB_CFB_COPYAREA
	select FB_CFB_IMAGEBLIT
	help
	  This driver supports the LE80578 (Vermilion Range) chipset

config FB_CARILLO_RANCH
	tristate "Intel Carillo Ranch support"
	depends on FB_LE80578 && FB && PCI && X86
	help
	  This driver supports the LE80578 (Carillo Ranch) board

config FB_INTEL
	tristate "Intel 830M/845G/852GM/855GM/865G/915G/945G/945GM/965G/965GM support (EXPERIMENTAL)"
	depends on EXPERIMENTAL && FB && PCI && X86 && AGP_INTEL && EXPERT
	select FB_MODE_HELPERS
	select FB_CFB_FILLRECT
	select FB_CFB_COPYAREA
	select FB_CFB_IMAGEBLIT
	select FB_BOOT_VESA_SUPPORT if FB_INTEL = y
	depends on !DRM_I915
	help
	  This driver supports the on-board graphics built in to the Intel
          830M/845G/852GM/855GM/865G/915G/915GM/945G/945GM/965G/965GM chipsets.
          Say Y if you have and plan to use such a board.

	  To make FB_INTELFB=Y work you need to say AGP_INTEL=y too.

	  To compile this driver as a module, choose M here: the
	  module will be called intelfb.

	  For more information, please read <file:Documentation/fb/intelfb.txt>

config FB_INTEL_DEBUG
	bool "Intel driver Debug Messages"
	depends on FB_INTEL
	---help---
	  Say Y here if you want the Intel driver to output all sorts
	  of debugging information to provide to the maintainer when
	  something goes wrong.

config FB_INTEL_I2C
	bool "DDC/I2C for Intel framebuffer support"
	depends on FB_INTEL
	select FB_DDC
	default y
	help
	  Say Y here if you want DDC/I2C support for your on-board Intel graphics.

config FB_MATROX
	tristate "Matrox acceleration"
	depends on FB && PCI
	select FB_CFB_FILLRECT
	select FB_CFB_COPYAREA
	select FB_CFB_IMAGEBLIT
	select FB_TILEBLITTING
	select FB_MACMODES if PPC_PMAC
	---help---
	  Say Y here if you have a Matrox Millennium, Matrox Millennium II,
	  Matrox Mystique, Matrox Mystique 220, Matrox Productiva G100, Matrox
	  Mystique G200, Matrox Millennium G200, Matrox Marvel G200 video,
	  Matrox G400, G450 or G550 card in your box.

	  To compile this driver as a module, choose M here: the
	  module will be called matroxfb.

	  You can pass several parameters to the driver at boot time or at
	  module load time. The parameters look like "video=matroxfb:XXX", and
	  are described in <file:Documentation/fb/matroxfb.txt>.

config FB_MATROX_MILLENIUM
	bool "Millennium I/II support"
	depends on FB_MATROX
	help
	  Say Y here if you have a Matrox Millennium or Matrox Millennium II
	  video card. If you select "Advanced lowlevel driver options" below,
	  you should check 4 bpp packed pixel, 8 bpp packed pixel, 16 bpp
	  packed pixel, 24 bpp packed pixel and 32 bpp packed pixel. You can
	  also use font widths different from 8.

config FB_MATROX_MYSTIQUE
	bool "Mystique support"
	depends on FB_MATROX
	help
	  Say Y here if you have a Matrox Mystique or Matrox Mystique 220
	  video card. If you select "Advanced lowlevel driver options" below,
	  you should check 8 bpp packed pixel, 16 bpp packed pixel, 24 bpp
	  packed pixel and 32 bpp packed pixel. You can also use font widths
	  different from 8.

config FB_MATROX_G
	bool "G100/G200/G400/G450/G550 support"
	depends on FB_MATROX
	---help---
	  Say Y here if you have a Matrox G100, G200, G400, G450 or G550 based
	  video card. If you select "Advanced lowlevel driver options", you
	  should check 8 bpp packed pixel, 16 bpp packed pixel, 24 bpp packed
	  pixel and 32 bpp packed pixel. You can also use font widths
	  different from 8.

	  If you need support for G400 secondary head, you must say Y to
	  "Matrox I2C support" and "G400 second head support" right below.
	  G450/G550 secondary head and digital output are supported without
	  additional modules.

	  The driver starts in monitor mode. You must use the matroxset tool 
	  (available at <ftp://platan.vc.cvut.cz/pub/linux/matrox-latest/>) to 
	  swap primary and secondary head outputs, or to change output mode.  
	  Secondary head driver always start in 640x480 resolution and you 
	  must use fbset to change it.

	  Do not forget that second head supports only 16 and 32 bpp
	  packed pixels, so it is a good idea to compile them into the kernel
	  too. You can use only some font widths, as the driver uses generic
	  painting procedures (the secondary head does not use acceleration
	  engine).

	  G450/G550 hardware can display TV picture only from secondary CRTC,
	  and it performs no scaling, so picture must have 525 or 625 lines.

config FB_MATROX_I2C
	tristate "Matrox I2C support"
	depends on FB_MATROX
	select FB_DDC
	---help---
	  This drivers creates I2C buses which are needed for accessing the
	  DDC (I2C) bus present on all Matroxes, an I2C bus which
	  interconnects Matrox optional devices, like MGA-TVO on G200 and
	  G400, and the secondary head DDC bus, present on G400 only.

	  You can say Y or M here if you want to experiment with monitor
	  detection code. You must say Y or M here if you want to use either
	  second head of G400 or MGA-TVO on G200 or G400.

	  If you compile it as module, it will create a module named
	  i2c-matroxfb.

config FB_MATROX_MAVEN
	tristate "G400 second head support"
	depends on FB_MATROX_G && FB_MATROX_I2C
	---help---
	  WARNING !!! This support does not work with G450 !!!

	  Say Y or M here if you want to use a secondary head (meaning two
	  monitors in parallel) on G400 or MGA-TVO add-on on G200. Secondary
	  head is not compatible with accelerated XFree 3.3.x SVGA servers -
	  secondary head output is blanked while you are in X. With XFree
	  3.9.17 preview you can use both heads if you use SVGA over fbdev or
	  the fbdev driver on first head and the fbdev driver on second head.

	  If you compile it as module, two modules are created,
	  matroxfb_crtc2 and matroxfb_maven. Matroxfb_maven is needed for
	  both G200 and G400, matroxfb_crtc2 is needed only by G400. You must
	  also load i2c-matroxfb to get it to run.

	  The driver starts in monitor mode and you must use the matroxset
	  tool (available at
	  <ftp://platan.vc.cvut.cz/pub/linux/matrox-latest/>) to switch it to
	  PAL or NTSC or to swap primary and secondary head outputs.
	  Secondary head driver also always start in 640x480 resolution, you
	  must use fbset to change it.

	  Also do not forget that second head supports only 16 and 32 bpp
	  packed pixels, so it is a good idea to compile them into the kernel
	  too.  You can use only some font widths, as the driver uses generic
	  painting procedures (the secondary head does not use acceleration
	  engine).

config FB_RADEON
	tristate "ATI Radeon display support"
	depends on FB && PCI
	select FB_BACKLIGHT if FB_RADEON_BACKLIGHT
	select FB_MODE_HELPERS
	select FB_CFB_FILLRECT
	select FB_CFB_COPYAREA
	select FB_CFB_IMAGEBLIT
	select FB_MACMODES if PPC_OF
	help
	  Choose this option if you want to use an ATI Radeon graphics card as
	  a framebuffer device.  There are both PCI and AGP versions.  You
	  don't need to choose this to run the Radeon in plain VGA mode.

	  There is a product page at
	  http://products.amd.com/en-us/GraphicCardResult.aspx

config FB_RADEON_I2C
	bool "DDC/I2C for ATI Radeon support"
	depends on FB_RADEON
	select FB_DDC
	default y
	help
	  Say Y here if you want DDC/I2C support for your Radeon board. 

config FB_RADEON_BACKLIGHT
	bool "Support for backlight control"
	depends on FB_RADEON
	default y
	help
	  Say Y here if you want to control the backlight of your display.

config FB_RADEON_DEBUG
	bool "Lots of debug output from Radeon driver"
	depends on FB_RADEON
	default n
	help
	  Say Y here if you want the Radeon driver to output all sorts
	  of debugging information to provide to the maintainer when
	  something goes wrong.

config FB_ATY128
	tristate "ATI Rage128 display support"
	depends on FB && PCI
	select FB_CFB_FILLRECT
	select FB_CFB_COPYAREA
	select FB_CFB_IMAGEBLIT
	select FB_BACKLIGHT if FB_ATY128_BACKLIGHT
	select FB_MACMODES if PPC_PMAC
	help
	  This driver supports graphics boards with the ATI Rage128 chips.
	  Say Y if you have such a graphics board and read
	  <file:Documentation/fb/aty128fb.txt>.

	  To compile this driver as a module, choose M here: the
	  module will be called aty128fb.

config FB_ATY128_BACKLIGHT
	bool "Support for backlight control"
	depends on FB_ATY128
	default y
	help
	  Say Y here if you want to control the backlight of your display.

config FB_ATY
	tristate "ATI Mach64 display support" if PCI || ATARI
	depends on FB && !SPARC32
	select FB_CFB_FILLRECT
	select FB_CFB_COPYAREA
	select FB_CFB_IMAGEBLIT
	select FB_BACKLIGHT if FB_ATY_BACKLIGHT
	select FB_MACMODES if PPC
	help
	  This driver supports graphics boards with the ATI Mach64 chips.
	  Say Y if you have such a graphics board.

	  To compile this driver as a module, choose M here: the
	  module will be called atyfb.

config FB_ATY_CT
	bool "Mach64 CT/VT/GT/LT (incl. 3D RAGE) support"
	depends on PCI && FB_ATY
	default y if SPARC64 && PCI
	help
	  Say Y here to support use of ATI's 64-bit Rage boards (or other
	  boards based on the Mach64 CT, VT, GT, and LT chipsets) as a
	  framebuffer device.  The ATI product support page for these boards
	  is at <http://support.ati.com/products/pc/mach64/mach64.html>.

config FB_ATY_GENERIC_LCD
	bool "Mach64 generic LCD support (EXPERIMENTAL)"
	depends on FB_ATY_CT
	help
	  Say Y if you have a laptop with an ATI Rage LT PRO, Rage Mobility,
	  Rage XC, or Rage XL chipset.

config FB_ATY_GX
	bool "Mach64 GX support" if PCI
	depends on FB_ATY
	default y if ATARI
	help
	  Say Y here to support use of the ATI Mach64 Graphics Expression
	  board (or other boards based on the Mach64 GX chipset) as a
	  framebuffer device.  The ATI product support page for these boards
	  is at
	  <http://support.ati.com/products/pc/mach64/graphics_xpression.html>.

config FB_ATY_BACKLIGHT
	bool "Support for backlight control"
	depends on FB_ATY
	default y
	help
	  Say Y here if you want to control the backlight of your display.

config FB_S3
	tristate "S3 Trio/Virge support"
	depends on FB && PCI
	select FB_CFB_FILLRECT
	select FB_CFB_COPYAREA
	select FB_CFB_IMAGEBLIT
	select FB_TILEBLITTING
	select FB_SVGALIB
	select VGASTATE
	select FONT_8x16 if FRAMEBUFFER_CONSOLE
	---help---
	  Driver for graphics boards with S3 Trio / S3 Virge chip.

config FB_S3_DDC
	bool "DDC for S3 support"
	depends on FB_S3
	select FB_DDC
	default y
	help
	  Say Y here if you want DDC support for your S3 graphics card.

config FB_SAVAGE
	tristate "S3 Savage support"
	depends on FB && PCI && EXPERIMENTAL
	select FB_MODE_HELPERS
	select FB_CFB_FILLRECT
	select FB_CFB_COPYAREA
	select FB_CFB_IMAGEBLIT
	select VGASTATE
	help
	  This driver supports notebooks and computers with S3 Savage PCI/AGP
	  chips.

	  Say Y if you have such a graphics card.

	  To compile this driver as a module, choose M here; the module
	  will be called savagefb.

config FB_SAVAGE_I2C
       bool "Enable DDC2 Support"
       depends on FB_SAVAGE
       select FB_DDC
       help
	  This enables I2C support for S3 Savage Chipsets.  This is used
	  only for getting EDID information from the attached display
	  allowing for robust video mode handling and switching.

	  Because fbdev-2.6 requires that drivers must be able to
	  independently validate video mode parameters, you should say Y
	  here.

config FB_SAVAGE_ACCEL
       bool "Enable Console Acceleration"
       depends on FB_SAVAGE
       default n
       help
          This option will compile in console acceleration support. If
          the resulting framebuffer console has bothersome glitches, then
          choose N here.

config FB_SIS
	tristate "SiS/XGI display support"
	depends on FB && PCI
	select FB_CFB_FILLRECT
	select FB_CFB_COPYAREA
	select FB_CFB_IMAGEBLIT
	select FB_BOOT_VESA_SUPPORT if FB_SIS = y
	help
	  This is the frame buffer device driver for the SiS 300, 315, 330
	  and 340 series as well as XGI V3XT, V5, V8, Z7 graphics chipsets.
	  Specs available at <http://www.sis.com> and <http://www.xgitech.com>.

	  To compile this driver as a module, choose M here; the module
	  will be called sisfb.

config FB_SIS_300
	bool "SiS 300 series support"
	depends on FB_SIS
	help
	  Say Y here to support use of the SiS 300/305, 540, 630 and 730.

config FB_SIS_315
	bool "SiS 315/330/340 series and XGI support"
	depends on FB_SIS
	help
	  Say Y here to support use of the SiS 315, 330 and 340 series
	  (315/H/PRO, 55x, 650, 651, 740, 330, 661, 741, 760, 761) as well
	  as XGI V3XT, V5, V8 and Z7.

config FB_VIA
       tristate "VIA UniChrome (Pro) and Chrome9 display support"
       depends on FB && PCI && X86
       select FB_CFB_FILLRECT
       select FB_CFB_COPYAREA
       select FB_CFB_IMAGEBLIT
       select I2C_ALGOBIT
       select I2C
       select GPIOLIB
       help
	  This is the frame buffer device driver for Graphics chips of VIA
	  UniChrome (Pro) Family (CLE266,PM800/CN400,P4M800CE/P4M800Pro/
	  CN700/VN800,CX700/VX700,P4M890) and Chrome9 Family (K8M890,CN896
 	  /P4M900,VX800)
	  Say Y if you have a VIA UniChrome graphics board.

	  To compile this driver as a module, choose M here: the
	  module will be called viafb.

if FB_VIA

config FB_VIA_DIRECT_PROCFS
	bool "direct hardware access via procfs (DEPRECATED)(DANGEROUS)"
	depends on FB_VIA
	default n
	help
	  Allow direct hardware access to some output registers via procfs.
	  This is dangerous but may provide the only chance to get the
	  correct output device configuration.
	  Its use is strongly discouraged.

config FB_VIA_X_COMPATIBILITY
	bool "X server compatibility"
	depends on FB_VIA
	default n
	help
	  This option reduces the functionality (power saving, ...) of the
	  framebuffer to avoid negative impact on the OpenChrome X server.
	  If you use any X server other than fbdev you should enable this
	  otherwise it should be safe to disable it and allow using all
	  features.

endif

config FB_NEOMAGIC
	tristate "NeoMagic display support"
	depends on FB && PCI
	select FB_MODE_HELPERS
	select FB_CFB_FILLRECT
	select FB_CFB_COPYAREA
	select FB_CFB_IMAGEBLIT
	select VGASTATE
	help
	  This driver supports notebooks with NeoMagic PCI chips.
	  Say Y if you have such a graphics card. 

	  To compile this driver as a module, choose M here: the
	  module will be called neofb.

config FB_KYRO
	tristate "IMG Kyro support"
	depends on FB && PCI
	select FB_CFB_FILLRECT
	select FB_CFB_COPYAREA
	select FB_CFB_IMAGEBLIT
	help
	  Say Y here if you have a STG4000 / Kyro / PowerVR 3 based
	  graphics board.

	  To compile this driver as a module, choose M here: the
	  module will be called kyrofb.

config FB_3DFX
	tristate "3Dfx Banshee/Voodoo3/Voodoo5 display support"
	depends on FB && PCI
	select FB_CFB_IMAGEBLIT
	select FB_CFB_FILLRECT
	select FB_CFB_COPYAREA
	select FB_MODE_HELPERS
	help
	  This driver supports graphics boards with the 3Dfx Banshee,
	  Voodoo3 or VSA-100 (aka Voodoo4/5) chips. Say Y if you have
	  such a graphics board.

	  To compile this driver as a module, choose M here: the
	  module will be called tdfxfb.

config FB_3DFX_ACCEL
	bool "3Dfx Acceleration functions (EXPERIMENTAL)"
	depends on FB_3DFX && EXPERIMENTAL
	---help---
	This will compile the 3Dfx Banshee/Voodoo3/VSA-100 frame buffer
	device driver with acceleration functions.

config FB_3DFX_I2C
	bool "Enable DDC/I2C support"
	depends on FB_3DFX && EXPERIMENTAL
	select FB_DDC
	default y
	help
	  Say Y here if you want DDC/I2C support for your 3dfx Voodoo3.

config FB_VOODOO1
	tristate "3Dfx Voodoo Graphics (sst1) support"
	depends on FB && PCI
	select FB_CFB_FILLRECT
	select FB_CFB_COPYAREA
	select FB_CFB_IMAGEBLIT
	---help---
	  Say Y here if you have a 3Dfx Voodoo Graphics (Voodoo1/sst1) or 
	  Voodoo2 (cvg) based graphics card.

	  To compile this driver as a module, choose M here: the
	  module will be called sstfb.

	  WARNING: Do not use any application that uses the 3D engine
	  (namely glide) while using this driver.
	  Please read the <file:Documentation/fb/sstfb.txt> for supported
	  options and other important info  support.

config FB_VT8623
	tristate "VIA VT8623 support"
	depends on FB && PCI
	select FB_CFB_FILLRECT
	select FB_CFB_COPYAREA
	select FB_CFB_IMAGEBLIT
	select FB_TILEBLITTING
	select FB_SVGALIB
	select VGASTATE
	select FONT_8x16 if FRAMEBUFFER_CONSOLE
	---help---
	  Driver for CastleRock integrated graphics core in the
	  VIA VT8623 [Apollo CLE266] chipset.

config FB_TRIDENT
	tristate "Trident/CyberXXX/CyberBlade support"
	depends on FB && PCI
	select FB_CFB_FILLRECT
	select FB_CFB_COPYAREA
	select FB_CFB_IMAGEBLIT
	---help---
	  This is the frame buffer device driver for Trident PCI/AGP chipsets.
	  Supported chipset families are TGUI 9440/96XX, 3DImage, Blade3D
	  and Blade XP.
	  There are also integrated versions of these chips called CyberXXXX,
	  CyberImage or CyberBlade. These chips are mostly found in laptops
	  but also on some motherboards including early VIA EPIA motherboards.
	  For more information, read <file:Documentation/fb/tridentfb.txt>

	  Say Y if you have such a graphics board.

	  To compile this driver as a module, choose M here: the
	  module will be called tridentfb.

config FB_ARK
	tristate "ARK 2000PV support"
	depends on FB && PCI
	select FB_CFB_FILLRECT
	select FB_CFB_COPYAREA
	select FB_CFB_IMAGEBLIT
	select FB_TILEBLITTING
	select FB_SVGALIB
	select VGASTATE
	select FONT_8x16 if FRAMEBUFFER_CONSOLE
	---help---
	  Driver for PCI graphics boards with ARK 2000PV chip
	  and ICS 5342 RAMDAC.

config FB_PM3
	tristate "Permedia3 support (EXPERIMENTAL)"
	depends on FB && PCI && EXPERIMENTAL
	select FB_CFB_FILLRECT
	select FB_CFB_COPYAREA
	select FB_CFB_IMAGEBLIT
	help
	  This is the frame buffer device driver for the 3DLabs Permedia3
	  chipset, used in Formac ProFormance III, 3DLabs Oxygen VX1 &
	  similar boards, 3DLabs Permedia3 Create!, Appian Jeronimo 2000
	  and maybe other boards.

config FB_CARMINE
	tristate "Fujitsu carmine frame buffer support"
	depends on FB && PCI
	select FB_CFB_FILLRECT
	select FB_CFB_COPYAREA
	select FB_CFB_IMAGEBLIT
	help
	  This is the frame buffer device driver for the Fujitsu Carmine chip.
	  The driver provides two independent frame buffer devices.

choice
	depends on FB_CARMINE
	prompt "DRAM timing"
	default FB_CARMINE_DRAM_EVAL

config FB_CARMINE_DRAM_EVAL
	bool "Eval board timings"
	help
	  Use timings which work on the eval card.

config CARMINE_DRAM_CUSTOM
	bool "Custom board timings"
	help
	  Use custom board timings.
endchoice

config FB_AU1100
	bool "Au1100 LCD Driver"
	depends on (FB = y) && MIPS_ALCHEMY
	select FB_CFB_FILLRECT
	select FB_CFB_COPYAREA
	select FB_CFB_IMAGEBLIT
	help
	  This is the framebuffer driver for the AMD Au1100 SOC.  It can drive
	  various panels and CRTs by passing in kernel cmd line option
	  au1100fb:panel=<name>.

config FB_AU1200
	bool "Au1200/Au1300 LCD Driver"
	depends on (FB = y) && MIPS_ALCHEMY
	select FB_SYS_FILLRECT
	select FB_SYS_COPYAREA
	select FB_SYS_IMAGEBLIT
	select FB_SYS_FOPS
	help
	  This is the framebuffer driver for the Au1200/Au1300 SOCs.
	  It can drive various panels and CRTs by passing in kernel cmd line
	  option au1200fb:panel=<name>.

config FB_VT8500
	bool "VT8500 LCD Driver"
	depends on (FB = y) && ARM && ARCH_VT8500 && VTWM_VERSION_VT8500
	select FB_WMT_GE_ROPS
	select FB_SYS_IMAGEBLIT
	help
	  This is the framebuffer driver for VIA VT8500 integrated LCD
	  controller.

config FB_WM8505
	bool "WM8505 frame buffer support"
	depends on (FB = y) && ARM && ARCH_VT8500 && VTWM_VERSION_WM8505
	select FB_WMT_GE_ROPS
	select FB_SYS_IMAGEBLIT
	help
	  This is the framebuffer driver for WonderMedia WM8505
	  integrated LCD controller.

source "drivers/video/geode/Kconfig"

config FB_HIT
	tristate "HD64461 Frame Buffer support"
	depends on FB && HD64461
	select FB_CFB_FILLRECT
	select FB_CFB_COPYAREA
	select FB_CFB_IMAGEBLIT
	help
	  This is the frame buffer device driver for the Hitachi HD64461 LCD
	  frame buffer card.

config FB_PMAG_AA
	bool "PMAG-AA TURBOchannel framebuffer support"
	depends on (FB = y) && TC
 	select FB_CFB_FILLRECT
 	select FB_CFB_COPYAREA
 	select FB_CFB_IMAGEBLIT
	help
	  Support for the PMAG-AA TURBOchannel framebuffer card (1280x1024x1)
	  used mainly in the MIPS-based DECstation series.

config FB_PMAG_BA
	tristate "PMAG-BA TURBOchannel framebuffer support"
	depends on FB && TC
 	select FB_CFB_FILLRECT
 	select FB_CFB_COPYAREA
 	select FB_CFB_IMAGEBLIT
	help
	  Support for the PMAG-BA TURBOchannel framebuffer card (1024x864x8)
	  used mainly in the MIPS-based DECstation series.

config FB_PMAGB_B
	tristate "PMAGB-B TURBOchannel framebuffer support"
	depends on FB && TC
 	select FB_CFB_FILLRECT
 	select FB_CFB_COPYAREA
 	select FB_CFB_IMAGEBLIT
	help
	  Support for the PMAGB-B TURBOchannel framebuffer card used mainly
	  in the MIPS-based DECstation series. The card is currently only
	  supported in 1280x1024x8 mode.

config FB_MAXINE
	bool "Maxine (Personal DECstation) onboard framebuffer support"
	depends on (FB = y) && MACH_DECSTATION
 	select FB_CFB_FILLRECT
 	select FB_CFB_COPYAREA
 	select FB_CFB_IMAGEBLIT
	help
	  Support for the onboard framebuffer (1024x768x8) in the Personal
	  DECstation series (Personal DECstation 5000/20, /25, /33, /50,
	  Codename "Maxine").

config FB_G364
	bool "G364 frame buffer support"
	depends on (FB = y) && (MIPS_MAGNUM_4000 || OLIVETTI_M700)
 	select FB_CFB_FILLRECT
 	select FB_CFB_COPYAREA
 	select FB_CFB_IMAGEBLIT
	help
	  The G364 driver is the framebuffer used in MIPS Magnum 4000 and
	  Olivetti M700-10 systems.

config FB_68328
	bool "Motorola 68328 native frame buffer support"
	depends on (FB = y) && (M68328 || M68EZ328 || M68VZ328)
 	select FB_CFB_FILLRECT
 	select FB_CFB_COPYAREA
 	select FB_CFB_IMAGEBLIT
	help
	  Say Y here if you want to support the built-in frame buffer of
	  the Motorola 68328 CPU family.

config FB_PXA168
	tristate "PXA168/910 LCD framebuffer support"
	depends on FB && (CPU_PXA168 || CPU_PXA910)
	select FB_CFB_FILLRECT
	select FB_CFB_COPYAREA
	select FB_CFB_IMAGEBLIT
	---help---
	  Frame buffer driver for the built-in LCD controller in the Marvell
	  MMP processor.

config FB_PXA
	tristate "PXA LCD framebuffer support"
	depends on FB && ARCH_PXA
	select FB_CFB_FILLRECT
	select FB_CFB_COPYAREA
	select FB_CFB_IMAGEBLIT
	---help---
	  Frame buffer driver for the built-in LCD controller in the Intel
	  PXA2x0 processor.

	  This driver is also available as a module ( = code which can be
	  inserted and removed from the running kernel whenever you want). The
	  module will be called pxafb. If you want to compile it as a module,
	  say M here and read <file:Documentation/kbuild/modules.txt>.

	  If unsure, say N.

config FB_PXA_OVERLAY
	bool "Support PXA27x/PXA3xx Overlay(s) as framebuffer"
	default n
	depends on FB_PXA && (PXA27x || PXA3xx)

config FB_PXA_SMARTPANEL
	bool "PXA Smartpanel LCD support"
	default n
	depends on FB_PXA

config FB_PXA_PARAMETERS
	bool "PXA LCD command line parameters"
	default n
	depends on FB_PXA
	---help---
	  Enable the use of kernel command line or module parameters
	  to configure the physical properties of the LCD panel when
	  using the PXA LCD driver.

	  This option allows you to override the panel parameters
	  supplied by the platform in order to support multiple
	  different models of flatpanel. If you will only be using a
	  single model of flatpanel then you can safely leave this
	  option disabled.

	  <file:Documentation/fb/pxafb.txt> describes the available parameters.

config PXA3XX_GCU
	tristate "PXA3xx 2D graphics accelerator driver"
	depends on FB_PXA
	help
	  Kernelspace driver for the 2D graphics controller unit (GCU)
	  found on PXA3xx processors. There is a counterpart driver in the
	  DirectFB suite, see http://www.directfb.org/

	  If you compile this as a module, it will be called pxa3xx_gcu.

config FB_MBX
	tristate "2700G LCD framebuffer support"
	depends on FB && ARCH_PXA
	select FB_CFB_FILLRECT
	select FB_CFB_COPYAREA
	select FB_CFB_IMAGEBLIT
	---help---
	  Framebuffer driver for the Intel 2700G (Marathon) Graphics
	  Accelerator

config FB_MBX_DEBUG
       bool "Enable debugging info via debugfs"
       depends on FB_MBX && DEBUG_FS
       default n
       ---help---
         Enable this if you want debugging information using the debug
         filesystem (debugfs)

         If unsure, say N.

config FB_FSL_DIU
	tristate "Freescale DIU framebuffer support"
	depends on FB && FSL_SOC
	select FB_MODE_HELPERS
	select FB_CFB_FILLRECT
	select FB_CFB_COPYAREA
	select FB_CFB_IMAGEBLIT
	select PPC_LIB_RHEAP
	---help---
	  Framebuffer driver for the Freescale SoC DIU

config FB_W100
	tristate "W100 frame buffer support"
	depends on FB && ARCH_PXA
 	select FB_CFB_FILLRECT
 	select FB_CFB_COPYAREA
 	select FB_CFB_IMAGEBLIT
	---help---
	  Frame buffer driver for the w100 as found on the Sharp SL-Cxx series.
	  It can also drive the w3220 chip found on iPAQ hx4700.

	  This driver is also available as a module ( = code which can be
	  inserted and removed from the running kernel whenever you want). The
	  module will be called w100fb. If you want to compile it as a module,
	  say M here and read <file:Documentation/kbuild/modules.txt>.

	  If unsure, say N.

config FB_SH_MOBILE_LCDC
	tristate "SuperH Mobile LCDC framebuffer support"
	depends on FB && (SUPERH || ARCH_SHMOBILE) && HAVE_CLK
	select FB_SYS_FILLRECT
	select FB_SYS_COPYAREA
	select FB_SYS_IMAGEBLIT
	select FB_SYS_FOPS
	select FB_DEFERRED_IO
	select FB_BACKLIGHT
	select SH_MIPI_DSI if SH_LCD_MIPI_DSI
	---help---
	  Frame buffer driver for the on-chip SH-Mobile LCD controller.

config FB_SH_MOBILE_HDMI
	tristate "SuperH Mobile HDMI controller support"
	depends on FB_SH_MOBILE_LCDC
	select FB_MODE_HELPERS
	select SOUND
	select SND
	select SND_SOC
	---help---
	  Driver for the on-chip SH-Mobile HDMI controller.

config FB_TMIO
	tristate "Toshiba Mobile IO FrameBuffer support"
	depends on FB && MFD_CORE
	select FB_CFB_FILLRECT
	select FB_CFB_COPYAREA
	select FB_CFB_IMAGEBLIT
	---help---
	  Frame buffer driver for the Toshiba Mobile IO integrated as found
	  on the Sharp SL-6000 series

	  This driver is also available as a module ( = code which can be
	  inserted and removed from the running kernel whenever you want). The
	  module will be called tmiofb. If you want to compile it as a module,
	  say M here and read <file:Documentation/kbuild/modules.txt>.

	  If unsure, say N.

config FB_TMIO_ACCELL
	bool "tmiofb acceleration"
	depends on FB_TMIO
	default y

config FB_S3C
	tristate "Samsung S3C framebuffer support"
	depends on FB && (S3C_DEV_FB || S5P_DEV_FIMD0)
	select FB_CFB_FILLRECT
	select FB_CFB_COPYAREA
	select FB_CFB_IMAGEBLIT
	---help---
	  Frame buffer driver for the built-in FB controller in the Samsung
	  SoC line from the S3C2443 onwards, including the S3C2416, S3C2450,
	  and the S3C64XX series such as the S3C6400 and S3C6410.

	  These chips all have the same basic framebuffer design with the
	  actual capabilities depending on the chip. For instance the S3C6400
	  and S3C6410 support 4 hardware windows whereas the S3C24XX series
	  currently only have two.

	  Currently the support is only for the S3C6400 and S3C6410 SoCs.

config FB_S3C_DEBUG_REGWRITE
       bool "Debug register writes"
       depends on FB_S3C
       ---help---
         Show all register writes via printk(KERN_DEBUG)

config FB_S3C2410
	tristate "S3C2410 LCD framebuffer support"
	depends on FB && ARCH_S3C24XX
	select FB_CFB_FILLRECT
	select FB_CFB_COPYAREA
	select FB_CFB_IMAGEBLIT
	---help---
	  Frame buffer driver for the built-in LCD controller in the Samsung
	  S3C2410 processor.

	  This driver is also available as a module ( = code which can be
	  inserted and removed from the running kernel whenever you want). The
	  module will be called s3c2410fb. If you want to compile it as a module,
	  say M here and read <file:Documentation/kbuild/modules.txt>.

	  If unsure, say N.
config FB_S3C2410_DEBUG
	bool "S3C2410 lcd debug messages"
	depends on FB_S3C2410
	help
	  Turn on debugging messages. Note that you can set/unset at run time
	  through sysfs

config FB_NUC900
        bool "NUC900 LCD framebuffer support"
        depends on FB && ARCH_W90X900
        select FB_CFB_FILLRECT
        select FB_CFB_COPYAREA
        select FB_CFB_IMAGEBLIT
        ---help---
          Frame buffer driver for the built-in LCD controller in the Nuvoton
          NUC900 processor

config GPM1040A0_320X240
        bool "Giantplus Technology GPM1040A0 320x240 Color TFT LCD"
        depends on FB_NUC900

config FB_NUC900_DEBUG
        bool "NUC900 lcd debug messages"
        depends on FB_NUC900
        help
          Turn on debugging messages. Note that you can set/unset at run time
          through sysfs

config FB_SM501
	tristate "Silicon Motion SM501 framebuffer support"
	depends on FB && MFD_SM501
	select FB_CFB_FILLRECT
	select FB_CFB_COPYAREA
	select FB_CFB_IMAGEBLIT
	---help---
	  Frame buffer driver for the CRT and LCD controllers in the Silicon
	  Motion SM501.

	  This driver is also available as a module ( = code which can be
	  inserted and removed from the running kernel whenever you want). The
	  module will be called sm501fb. If you want to compile it as a module,
	  say M here and read <file:Documentation/kbuild/modules.txt>.

	  If unsure, say N.

config FB_SMSCUFX
	tristate "SMSC UFX6000/7000 USB Framebuffer support"
	depends on FB && USB
	select FB_MODE_HELPERS
	select FB_SYS_FILLRECT
	select FB_SYS_COPYAREA
	select FB_SYS_IMAGEBLIT
	select FB_SYS_FOPS
	select FB_DEFERRED_IO
	---help---
	  This is a kernel framebuffer driver for SMSC UFX USB devices.
	  Supports fbdev clients like xf86-video-fbdev, kdrive, fbi, and
	  mplayer -vo fbdev. Supports both UFX6000 (USB 2.0) and UFX7000
	  (USB 3.0) devices.
	  To compile as a module, choose M here: the module name is smscufx.

config FB_UDL
	tristate "Displaylink USB Framebuffer support"
	depends on FB && USB
	select FB_MODE_HELPERS
	select FB_SYS_FILLRECT
	select FB_SYS_COPYAREA
	select FB_SYS_IMAGEBLIT
	select FB_SYS_FOPS
	select FB_DEFERRED_IO
	---help---
	  This is a kernel framebuffer driver for DisplayLink USB devices.
	  Supports fbdev clients like xf86-video-fbdev, kdrive, fbi, and
	  mplayer -vo fbdev. Supports all USB 2.0 era DisplayLink devices.
	  To compile as a module, choose M here: the module name is udlfb.

config FB_PNX4008_DUM
	tristate "Display Update Module support on Philips PNX4008 board"
	depends on FB && ARCH_PNX4008
	---help---
	  Say Y here to enable support for PNX4008 Display Update Module (DUM)

config FB_PNX4008_DUM_RGB
	tristate "RGB Framebuffer support on Philips PNX4008 board"
	depends on FB_PNX4008_DUM
	select FB_CFB_FILLRECT
	select FB_CFB_COPYAREA
	select FB_CFB_IMAGEBLIT
	---help---
	  Say Y here to enable support for PNX4008 RGB Framebuffer

config FB_IBM_GXT4500
	tristate "Framebuffer support for IBM GXT4500P adaptor"
	depends on FB && PPC
	select FB_CFB_FILLRECT
	select FB_CFB_COPYAREA
	select FB_CFB_IMAGEBLIT
	---help---
	  Say Y here to enable support for the IBM GXT4500P display
	  adaptor, found on some IBM System P (pSeries) machines.

config FB_PS3
	tristate "PS3 GPU framebuffer driver"
	depends on FB && PS3_PS3AV
	select FB_SYS_FILLRECT
	select FB_SYS_COPYAREA
	select FB_SYS_IMAGEBLIT
	select FB_SYS_FOPS
	select VT_HW_CONSOLE_BINDING if FRAMEBUFFER_CONSOLE
	---help---
	  Include support for the virtual frame buffer in the PS3 platform.

config FB_PS3_DEFAULT_SIZE_M
	int "PS3 default frame buffer size (in MiB)"
	depends on FB_PS3
	default 9
	---help---
	  This is the default size (in MiB) of the virtual frame buffer in
	  the PS3.
	  The default value can be overridden on the kernel command line
	  using the "ps3fb" option (e.g. "ps3fb=9M");

config FB_XILINX
	tristate "Xilinx frame buffer support"
	depends on FB && (XILINX_VIRTEX || MICROBLAZE)
	select FB_CFB_FILLRECT
	select FB_CFB_COPYAREA
	select FB_CFB_IMAGEBLIT
	---help---
	  Include support for the Xilinx ML300/ML403 reference design
	  framebuffer. ML300 carries a 640*480 LCD display on the board,
	  ML403 uses a standard DB15 VGA connector.

config FB_COBALT
	tristate "Cobalt server LCD frame buffer support"
	depends on FB && MIPS_COBALT

config FB_SH7760
	bool "SH7760/SH7763/SH7720/SH7721 LCDC support"
	depends on FB && (CPU_SUBTYPE_SH7760 || CPU_SUBTYPE_SH7763 \
		|| CPU_SUBTYPE_SH7720 || CPU_SUBTYPE_SH7721)
	select FB_CFB_FILLRECT
	select FB_CFB_COPYAREA
	select FB_CFB_IMAGEBLIT
	---help---
	  Support for the SH7760/SH7763/SH7720/SH7721 integrated
	  (D)STN/TFT LCD Controller.
	  Supports display resolutions up to 1024x1024 pixel, grayscale and
	  color operation, with depths ranging from 1 bpp to 8 bpp monochrome
	  and 8, 15 or 16 bpp color; 90 degrees clockwise display rotation for
	  panels <= 320 pixel horizontal resolution.

config FB_DA8XX
	tristate "DA8xx/OMAP-L1xx Framebuffer support"
	depends on FB && ARCH_DAVINCI_DA8XX
	select FB_CFB_FILLRECT
	select FB_CFB_COPYAREA
	select FB_CFB_IMAGEBLIT
	select FB_CFB_REV_PIXELS_IN_BYTE
	---help---
	  This is the frame buffer device driver for the TI LCD controller
	  found on DA8xx/OMAP-L1xx SoCs.
	  If unsure, say N.

config FB_VIRTUAL
	tristate "Virtual Frame Buffer support (ONLY FOR TESTING!)"
	depends on FB
	select FB_SYS_FILLRECT
	select FB_SYS_COPYAREA
	select FB_SYS_IMAGEBLIT
	select FB_SYS_FOPS
	---help---
	  This is a `virtual' frame buffer device. It operates on a chunk of
	  unswappable kernel memory instead of on the memory of a graphics
	  board. This means you cannot see any output sent to this frame
	  buffer device, while it does consume precious memory. The main use
	  of this frame buffer device is testing and debugging the frame
	  buffer subsystem. Do NOT enable it for normal systems! To protect
	  the innocent, it has to be enabled explicitly at boot time using the
	  kernel option `video=vfb:'.

	  To compile this driver as a module, choose M here: the
	  module will be called vfb. In order to load it, you must use
	  the vfb_enable=1 option.

	  If unsure, say N.

config XEN_FBDEV_FRONTEND
	tristate "Xen virtual frame buffer support"
	depends on FB && XEN
	select FB_SYS_FILLRECT
	select FB_SYS_COPYAREA
	select FB_SYS_IMAGEBLIT
	select FB_SYS_FOPS
	select FB_DEFERRED_IO
	select INPUT_XEN_KBDDEV_FRONTEND
	select XEN_XENBUS_FRONTEND
	default y
	help
	  This driver implements the front-end of the Xen virtual
	  frame buffer driver.  It communicates with a back-end
	  in another domain.

config FB_METRONOME
	tristate "E-Ink Metronome/8track controller support"
	depends on FB
	select FB_SYS_FILLRECT
	select FB_SYS_COPYAREA
	select FB_SYS_IMAGEBLIT
	select FB_SYS_FOPS
	select FB_DEFERRED_IO
	help
	  This driver implements support for the E-Ink Metronome
	  controller. The pre-release name for this device was 8track
	  and could also have been called by some vendors as PVI-nnnn.

config FB_MB862XX
	tristate "Fujitsu MB862xx GDC support"
	depends on FB
	depends on PCI || (OF && PPC)
	select FB_CFB_FILLRECT
	select FB_CFB_COPYAREA
	select FB_CFB_IMAGEBLIT
	---help---
	  Frame buffer driver for Fujitsu Carmine/Coral-P(A)/Lime controllers.

choice
	prompt "GDC variant"
	depends on FB_MB862XX

config FB_MB862XX_PCI_GDC
	bool "Carmine/Coral-P(A) GDC"
	depends on PCI
	---help---
	  This enables framebuffer support for Fujitsu Carmine/Coral-P(A)
	  PCI graphics controller devices.

config FB_MB862XX_LIME
	bool "Lime GDC"
	depends on OF && PPC
	select FB_FOREIGN_ENDIAN
	select FB_LITTLE_ENDIAN
	---help---
	  Framebuffer support for Fujitsu Lime GDC on host CPU bus.

endchoice

config FB_MB862XX_I2C
	bool "Support I2C bus on MB862XX GDC"
	depends on FB_MB862XX && I2C
	default y
	help
	  Selecting this option adds Coral-P(A)/Lime GDC I2C bus adapter
	  driver to support accessing I2C devices on controller's I2C bus.
	  These are usually some video decoder chips.

config FB_EP93XX
	tristate "EP93XX frame buffer support"
	depends on FB && ARCH_EP93XX
	select FB_CFB_FILLRECT
	select FB_CFB_COPYAREA
	select FB_CFB_IMAGEBLIT
	---help---
	  Framebuffer driver for the Cirrus Logic EP93XX series of processors.
	  This driver is also available as a module. The module will be called
	  ep93xx-fb.

config FB_PRE_INIT_FB
	bool "Don't reinitialize, use bootloader's GDC/Display configuration"
	depends on FB && FB_MB862XX_LIME
	---help---
	  Select this option if display contents should be inherited as set by
	  the bootloader.

config FB_MSM
	tristate "MSM Framebuffer support"
	depends on FB && ARCH_MSM
	select FB_CFB_FILLRECT
	select FB_CFB_COPYAREA
	select FB_CFB_IMAGEBLIT

config FB_MX3
	tristate "MX3 Framebuffer support"
	depends on FB && MX3_IPU
	select FB_CFB_FILLRECT
	select FB_CFB_COPYAREA
	select FB_CFB_IMAGEBLIT
	default y
	help
	  This is a framebuffer device for the i.MX31 LCD Controller. So
	  far only synchronous displays are supported. If you plan to use
	  an LCD display with your i.MX31 system, say Y here.

config FB_BROADSHEET
	tristate "E-Ink Broadsheet/Epson S1D13521 controller support"
	depends on FB
	select FB_SYS_FILLRECT
	select FB_SYS_COPYAREA
	select FB_SYS_IMAGEBLIT
	select FB_SYS_FOPS
	select FB_DEFERRED_IO
	help
	  This driver implements support for the E-Ink Broadsheet
	  controller. The release name for this device was Epson S1D13521
	  and could also have been called by other names when coupled with
	  a bridge adapter.
	  
config FB_LEADT15DS26
    tristate "FB_LEADT15DS26"
	depends on FB
	select FB_CFB_FILLRECT
	select FB_CFB_COPYAREA
	select FB_CFB_IMAGEBLIT
	select FB_SYS_FOPS
	help
	  Framebuffer support.
	  
config LCD_BLG_SINK
    bool "LCD_BLG_SINK"
	depends on FB_LEADT15DS26
	help
	  pmu sink support.	
	  
config FB_LCM_LEAD_128_128_F231E
    tristate "FB_LCM_LEAD_128_128_F231E"
	depends on FB
	help
	  Framebuffer support.
	  
config FB_LCM_ST7789V_Z2
    tristate "FB_LCM_ST7789V_Z2"
	depends on FB
	help
	  Framebuffer support.	  
	  
config FB_JZ4740
	tristate "JZ4740 LCD framebuffer support"
	depends on FB && MACH_JZ4740
	select FB_SYS_FILLRECT
	select FB_SYS_COPYAREA
	select FB_SYS_IMAGEBLIT
	help
	  Framebuffer support for the JZ4740 SoC.

config FB_MXS
	tristate "MXS LCD framebuffer support"
	depends on FB && ARCH_MXS
	select FB_CFB_FILLRECT
	select FB_CFB_COPYAREA
	select FB_CFB_IMAGEBLIT
	help
	  Framebuffer support for the MXS SoC.

config FB_PUV3_UNIGFX
	tristate "PKUnity v3 Unigfx framebuffer support"
	depends on FB && UNICORE32 && ARCH_PUV3
	select FB_SYS_FILLRECT
	select FB_SYS_COPYAREA
	select FB_SYS_IMAGEBLIT
	select FB_SYS_FOPS
	help
	  Choose this option if you want to use the Unigfx device as a
	  framebuffer device. Without the support of PCI & AGP.

source "drivers/video/omap/Kconfig"
source "drivers/video/omap2/Kconfig"
source "drivers/video/exynos/Kconfig"
source "drivers/video/backlight/Kconfig"

if VT
	source "drivers/video/console/Kconfig"
endif

if FB || SGI_NEWPORT_CONSOLE
	source "drivers/video/logo/Kconfig"
endif

config FB_SH_MOBILE_MERAM
	tristate "SuperH Mobile MERAM read ahead support"
	depends on (SUPERH || ARCH_SHMOBILE)
	select GENERIC_ALLOCATOR
	---help---
	  Enable MERAM support for the SuperH controller.

	  This will allow for caching of the framebuffer to provide more
	  reliable access under heavy main memory bus traffic situations.
	  Up to 4 memory channels can be configured, allowing 4 RGB or
	  2 YCbCr framebuffers to be configured.

endmenu
