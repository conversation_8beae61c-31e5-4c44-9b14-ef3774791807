# Virtio always gets selected by whoever wants it.
config VIRTIO
	tristate

# Similarly the virtio ring implementation.
config VIRTIO_RING
	tristate
	depends on VIRTIO

menu "Virtio drivers"

config VIRTIO_PCI
	tristate "PCI driver for virtio devices (EXPERIMENTAL)"
	depends on PCI && EXPERIMENTAL
	select VIRT<PERSON>
	select VIRTIO_RING
	---help---
	  This drivers provides support for virtio based paravirtual device
	  drivers over PCI.  This requires that your VMM has appropriate PCI
	  virtio backends.  Most QEMU based VMMs should support these devices
	  (like KVM or Xen).

	  Currently, the ABI is not considered stable so there is no guarantee
	  that this version of the driver will work with your VMM.

	  If unsure, say <PERSON><PERSON>

config VIRTIO_BALLOON
	tristate "Virtio balloon driver (EXPERIMENTAL)"
	select VIRT<PERSON>
	select VIRTIO_RING
	---help---
	 This driver supports increasing and decreasing the amount
	 of memory within a KVM guest.

	 If unsure, say M.

 config VIRTIO_MMIO
 	tristate "Platform bus driver for memory mapped virtio devices (EXPERIMENTAL)"
 	depends on HAS_IOMEM && EXPERIMENTAL
 	select VIRT<PERSON>
 	select VIRTIO_RING
 	---help---
 	 This drivers provides support for memory mapped virtio
	 platform device driver.

 	 If unsure, say <PERSON><PERSON>

endmenu
