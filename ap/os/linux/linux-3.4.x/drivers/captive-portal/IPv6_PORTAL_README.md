# IPv6 Portal Support for speedlimit.c

## Overview

This document describes the IPv6 support added to the captive portal module (`speedlimit.c`). All IPv6 functionality is controlled by the `CONFIG_IPV6_PORTAL` macro.

## Configuration

To enable IPv6 support, define the `CONFIG_IPV6_PORTAL` macro in your build configuration:

```c
#define CONFIG_IPV6_PORTAL
```

## Features Added

### 1. IPv6 Packet Interception
- IPv6 TCP and UDP packet filtering in pre-routing and post-routing hooks
- Support for IPv6 local network detection
- IPv6 gateway traffic handling

### 2. IPv6 Whitelist Support
- IPv6 address whitelist functionality
- Dynamic whitelist management functions
- Default entries for common IPv6 DNS servers

### 3. IPv6 Checksum Calculation
- Proper IPv6 TCP checksum calculation using pseudo-header
- IPv6-specific packet manipulation support

### 4. IPv6 Network Configuration
- Configurable IPv6 local network ranges
- IPv6 gateway address configuration
- Link-local address support (fe80::/16)

## API Functions

### Whitelist Management

```c
// Add IPv6 address to whitelist
int add_ipv6_whitelist(const char *ipv6_str);

// Clear all IPv6 whitelist entries
void clear_ipv6_whitelist(void);

// Initialize IPv6 whitelist with default entries
void init_ipv6_whitelist(void);
```

### Checksum Calculation

```c
// Calculate TCP checksum for IPv6 packets
void calcTcpv6Checksum(struct ipv6hdr* ip6h, struct tcphdr *thd);
```

## Usage Examples

### Adding IPv6 Addresses to Whitelist

```c
// Add Google's IPv6 DNS server
add_ipv6_whitelist("2001:4860:4860::8888");

// Add Cloudflare's IPv6 DNS server  
add_ipv6_whitelist("2606:4700:4700::1111");
```

### Network Configuration

The module automatically configures IPv6 network ranges:

- **Local Network**: `fe80::/16` (Link-local)
- **Gateway**: `fe80::1`
- **Mask**: `ffff::/16`

## Compilation

When compiling with IPv6 support:

```bash
# Enable IPv6 portal support
make CONFIG_IPV6_PORTAL=y
```

## Limitations

1. **IPv6 Redirect**: Currently logs IPv6 redirect attempts but doesn't fully implement packet redirection to IPv6 captive portal
2. **Address Storage**: Client structure still uses IPv4 format for compatibility - IPv6 addresses are stored using lower 32 bits
3. **Whitelist Size**: Limited to `MAX_WHITELIST_IP` entries (20 by default)

## Future Enhancements

1. Full IPv6 packet redirection to IPv6 captive portal
2. Extended client structure for native IPv6 address storage
3. IPv6-specific configuration via procfs
4. IPv6 address range configuration
5. Dual-stack (IPv4/IPv6) client tracking

## Testing

To test IPv6 functionality:

1. Enable `CONFIG_IPV6_PORTAL` in build
2. Load the module
3. Monitor kernel logs for IPv6 packet processing
4. Test with IPv6-enabled clients

## Debugging

Enable debug output by checking kernel logs:

```bash
dmesg | grep -i ipv6
```

Look for messages like:
- "IPv6 portal support enabled"
- "IPv6 whitelist initialized with X entries"
- "IPv6 TCP/UDP packet: [source] -> [dest]"

## Notes

- All IPv6 functionality is conditionally compiled based on `CONFIG_IPV6_PORTAL`
- Backward compatibility with IPv4-only builds is maintained
- IPv6 support requires kernel IPv6 stack to be enabled
