#ifndef __SPEEDLIMIT_H__
#define __SPEEDLIMIT_H__

#define NIPQUAD(addr) \
  ((unsigned char *)&addr)[0], \
  ((unsigned char *)&addr)[1], \
  ((unsigned char *)&addr)[2], \
  ((unsigned char *)&addr)[3]

typedef union {
    int int_ip;
    char char_ip[4];
} IPADR;

struct mac_int {
    int mac1;
    short mac2;
};

struct mac_char {
    char mac[6];
};

typedef union {
    struct mac_int mac_int;
    struct mac_char mac_char;
} MACADR;

typedef struct {
    IPADR ip;
    time_t tm;
} IP_LIST;

typedef struct {
    IPADR ip;
    MACADR mac;
    time_t time;
} CLIENT;

typedef struct client_list {
    struct client_list *prev;
    struct client_list *next;
    CLIENT client;
} CLIENT_LIST;

typedef struct redir {
    unsigned short port;
    int ip;
    time_t time;
} REDDIR;

struct psd_header {

    unsigned long saddr; //源锟斤拷址

    unsigned long daddr; //目锟侥碉拷址

    char mbz;//锟矫匡拷

    char ptcl; //协锟斤拷锟斤拷锟斤拷

    unsigned short tcpl; //TCP锟斤拷锟斤拷

};


//#define WLANDEVICE ("wlan0")
#define WLANDEVICE ("br0")
#define LOCALGATEWAYPORT 9000
#define DEFAULTHTTPPORT 80
#define HTTPS_PPORT 443
//#define CONFIG_IPV6_SPEEDLIMIT

extern int mytime(void);

// Add these function declarations if they don't exist
extern void clearAuthList(void);
extern void move2NewList(CLIENT_LIST *cli);
extern CLIENT_LIST *authClient;
extern CLIENT_LIST *newClient;

#endif
