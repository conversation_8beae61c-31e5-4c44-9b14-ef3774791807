#include <linux/module.h>
#include <linux/kernel.h>
#include <linux/init.h>
#include <linux/proc_fs.h>
#include <linux/seq_file.h>
#include <linux/jiffies.h>
#include <asm/uaccess.h>
#include "speedlimit.h"

#define MODULE_NAME "cjportal"
#define NETRATE_LEN 8
#define PROTAL_LEN 8
#define AUTHMAC_LEN 128


static struct proc_dir_entry *rbMaster_dir,*authClient_file,*protalConf_file;
char authMac_data[AUTHMAC_LEN];
char protalConf_data[PROTAL_LEN];
int portalLifetime = 60000*60;
//int portalLifetime = 60*60;  //default use disable portal
//extern struct mutex clientMutex;
extern atomic_t clientMutex;

extern void register_rbmaster_hook(void);
extern void unregister_rbmaster_hook(void);
extern void clientAuthorized(MACADR mac_adr);
extern void deleteAuthClient(MACADR mac_adr);


static ssize_t proc_write_authMac(struct file *file, const char __user *buffer, size_t count, loff_t *ppos)
{
    int len;
    int mac[6];
    MACADR mac_adr;
    char command[10] = {0};

    if (count > AUTHMAC_LEN)
        len = AUTHMAC_LEN;
    else
        len = count;

    if (copy_from_user(authMac_data, buffer, len))
        return -EFAULT;

    printk("Received command: %s\n", authMac_data);

    // 澶勭悊 clear 鍛戒护
    if (strncmp(authMac_data, "clear", 5) == 0) {
        printk("Clearing all authenticated MACs\n");
        clearAuthList();
        goto cleanup;
    }

    // 澶勭悊 del 鍛戒护
    if (sscanf(authMac_data, "%9s %02x:%02x:%02x:%02x:%02x:%02x", 
              command, &mac[0], &mac[1], &mac[2], 
              &mac[3], &mac[4], &mac[5]) == 7) {
        
        if (strcmp(command, "del") != 0) {
            printk("Invalid command: %s\n", command);
            goto cleanup;
        }

        // 濉�鍏� MAC 缁撴瀯浣�
        mac_adr.mac_char.mac[0] = (u8)mac[0];
        mac_adr.mac_char.mac[1] = (u8)mac[1];
        mac_adr.mac_char.mac[2] = (u8)mac[2];
        mac_adr.mac_char.mac[3] = (u8)mac[3];
        mac_adr.mac_char.mac[4] = (u8)mac[4];
        mac_adr.mac_char.mac[5] = (u8)mac[5];

        printk("Deleting MAC: %02x:%02x:%02x:%02x:%02x:%02x\n",
               mac_adr.mac_char.mac[0], mac_adr.mac_char.mac[1],
               mac_adr.mac_char.mac[2], mac_adr.mac_char.mac[3],
               mac_adr.mac_char.mac[4], mac_adr.mac_char.mac[5]);

        deleteAuthClient(mac_adr); 

        goto cleanup;
    }

    // 榛樿�ゅ�勭悊娣诲姞 MAC 鐨勬祦绋�
    if (sscanf(authMac_data, "%02x:%02x:%02x:%02x:%02x:%02x",
               &mac[0], &mac[1], &mac[2], 
               &mac[3], &mac[4], &mac[5]) == 6) {

        mac_adr.mac_char.mac[0] = (u8)mac[0];
        mac_adr.mac_char.mac[1] = (u8)mac[1];
        mac_adr.mac_char.mac[2] = (u8)mac[2];
        mac_adr.mac_char.mac[3] = (u8)mac[3];
        mac_adr.mac_char.mac[4] = (u8)mac[4];
        mac_adr.mac_char.mac[5] = (u8)mac[5];

        clientAuthorized(mac_adr);
        goto cleanup;
    }

    printk("Invalid command format\n");
    len = -EINVAL;

cleanup:
    memset(authMac_data, 0, AUTHMAC_LEN);
    return len;
}

static int proc_show_auth_mac(struct seq_file *m, void *v)
{
    CLIENT_LIST *currentClient;
    
    seq_printf(m, "=== Authenticated MACs ===\n");
    
    // Lock the client list
    atomic_inc(&clientMutex);
    
    // Print all authenticated MACs
    currentClient = authClient;
    if (currentClient == NULL) {
        seq_printf(m, "No authenticated clients\n");
    } else {
        while (currentClient != NULL) {
            seq_printf(m, "MAC: %02x:%02x:%02x:%02x:%02x:%02x  IP: %d.%d.%d.%d  Time: %ld\n",
                currentClient->client.mac.mac_char.mac[0], currentClient->client.mac.mac_char.mac[1],
                currentClient->client.mac.mac_char.mac[2], currentClient->client.mac.mac_char.mac[3],
                currentClient->client.mac.mac_char.mac[4], currentClient->client.mac.mac_char.mac[5],
                NIPQUAD(currentClient->client.ip.int_ip), currentClient->client.time);
            currentClient = currentClient->next;
        }
    }
    
    seq_printf(m, "\n=== Unauthenticated MACs ===\n");
    
    // Print all unauthenticated MACs
    currentClient = newClient;
    if (currentClient == NULL) {
        seq_printf(m, "No unauthenticated clients\n");
    } else {
        while (currentClient != NULL) {
            seq_printf(m, "MAC: %02x:%02x:%02x:%02x:%02x:%02x  IP: %d.%d.%d.%d  Time: %ld\n",
                currentClient->client.mac.mac_char.mac[0], currentClient->client.mac.mac_char.mac[1],
                currentClient->client.mac.mac_char.mac[2], currentClient->client.mac.mac_char.mac[3],
                currentClient->client.mac.mac_char.mac[4], currentClient->client.mac.mac_char.mac[5],
                NIPQUAD(currentClient->client.ip.int_ip), currentClient->client.time);
            currentClient = currentClient->next;
        }
    }
    
    // Unlock the client list
    atomic_dec(&clientMutex);
    
    return 0;
}


static int proc_auth_open (struct inode *inode, struct file *file)
{
    return single_open(file, proc_show_auth_mac, inode->i_private);
}

static const struct file_operations auth_fops = {
	.owner = THIS_MODULE,
	.open = proc_auth_open,
	.read = seq_read,
	.write = proc_write_authMac,
	.llseek = seq_lseek,
	.release = single_release,
	
};


static ssize_t proc_write_protalConf(struct file *file, const char __user *buffer, size_t count, loff_t *ppos)
{
    int len, portal = 0;
    if (count  > PROTAL_LEN)
        len = PROTAL_LEN;
    else
        len = count;

    if (copy_from_user(&protalConf_data,buffer,len))
        return -EFAULT;
    sscanf(protalConf_data,"%d",&portal);
    printk("cjportal read from input %d\n",portal);
    if(portal>5*60) { // packets not 0, enable rate restrict
        portalLifetime = portal;
//        register_rbmaster_hook();
    }
    else { // packets 0, don't restrict net rate
        portalLifetime = 60*60;
//	unregister_rbmaster_hook();
    }
    return len;
}

static int proc_show_portal_mac(struct seq_file *m, void *v)
{
	//seq_printf(m, "salvikie proc_show_portal_mac \n");
    seq_printf(m, "%d\n", portalLifetime);
	return 0;
}

static int proc_portal_open (struct inode *inode, struct file *file)
{
    return single_open(file, proc_show_portal_mac, inode->i_private);
}

static const struct file_operations portal_fops = {
	.owner = THIS_MODULE,
	.open = proc_portal_open,
	.read = seq_read,
	.write = proc_write_protalConf,
	.llseek = seq_lseek,
	.release = single_release,
};

int init_procfs_cjPortal(void)
{
    int rv = 0;

    rbMaster_dir = proc_mkdir (MODULE_NAME,NULL);
    if (rbMaster_dir == NULL)
    {
        rv = -ENOMEM;
        goto out;
    }

    authClient_file = proc_create ("auth",0644,rbMaster_dir, &auth_fops);
    if (authClient_file == NULL)
    {
        rv = -ENOMEM;
        goto out;  //should remove above file first ??
    }
    //strcpy(authMac_data,"0");
    //authClient_file->data = authMac_data;


    protalConf_file = proc_create ("portal",0644,rbMaster_dir, &portal_fops);
    if (protalConf_file == NULL)
    {
        rv = -ENOMEM;
        goto out;  //should remove above file first ??
    }
    //strcpy(protalConf_data,"0");
    //protalConf_file->data = protalConf_data;

    return 0;

out:
    return rv;
}
EXPORT_SYMBOL(init_procfs_cjPortal);

void cleanup_procfs_cjportal (void)
{
    remove_proc_entry(MODULE_NAME,NULL);
}
EXPORT_SYMBOL(cleanup_procfs_cjportal);
