#
# (C) Copyright 2000-2006
# <PERSON>, DENX Software Engineering, <EMAIL>.
#
# SPDX-License-Identifier:	GPL-2.0+
#

include $(TOPDIR)/config.mk

LIB	:= $(obj)libjffs2.o

COBJS-$(CONFIG_JFFS2_LZO) += compr_lzo.o
COBJS-y += compr_lzma.o
COBJS-y += LzmaEnc.o
COBJS-y += LzFind.o
COBJS-y += LzmaDec.o
COBJS-y += compr_rtime.o
COBJS-y += compr_rubin.o
COBJS-y += compr_zlib.o
COBJS-y += jffs2_1pass.o
#COBJS-$(CONFIG_SYS_JFFS2_SORT_FRAGMENTS) += mergesort.o
COBJS-$(CONFIG_SYS_JFFS2_SORT_FRAGMENTS_DIR) += mergesort.o
COBJS-y += mini_inflate.o

COBJS	:= $(COBJS-y)
SRCS	:= $(COBJS:.o=.c)
OBJS	:= $(addprefix $(obj),$(COBJS))

all:	$(LIB)

$(LIB):	$(obj).depend $(OBJS)
	$(call cmd_link_o_target, $(OBJS))

#########################################################################

# defines $(obj).depend target
include $(SRCTREE)/rules.mk

sinclude $(obj).depend

#########################################################################
