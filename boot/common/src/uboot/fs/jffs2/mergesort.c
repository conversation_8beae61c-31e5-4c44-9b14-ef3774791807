/*
 * This file is copyright 2001 <PERSON>.
 * Rewritten from original source 2006 by <PERSON> for use in u-boot.
 *
 * Original code can be found at:
 * http://www.chiark.greenend.org.uk/~sgtatham/algorithms/listsort.html
 *
 * SPDX-License-Identifier:	MIT
 */

#include <common.h>
#include "jffs2_private.h"

int sort_list(struct b_list *list)
{
	struct b_node *p, *q, *e, **tail;
	int k, psize, qsize;

	if (!list->listHead)
		return 0;

	for (k = 1; k < list->listCount; k *= 2) {
		tail = &list->listHead;
		for (p = q = list->listHead; p; p = q) {
			/* step 'k' places from p; */
			for (psize = 0; q && psize < k; psize++)
				q = q->next;
			qsize = k;

			/* two lists, merge them. */
			while (psize || (qsize && q)) {
				/* merge the next element */
				if (psize == 0 ||
				    ((qsize && q) &&
				     list->listCompare(p, q))) {
					/* p is empty, or p > q, so q next */
					e = q;
					q = q->next;
					qsize--;
				} else {
					e = p;
					p = p->next;
					psize--;
				}
				e->next = NULL; /* break accidental loops. */
				*tail = e;
				tail = &e->next;
			}
		}
	}
	return 0;
}


//#ifdef CONFIG_SYS_JFFS2_SORT_FRAGMENTS
#if 0
void sort_list2(struct b_list *list)
{
	struct b_node * *array_list;
	struct b_node *item;
	int i = 0;

	array_list = malloc(list->listCount * sizeof(struct b_node *));
	if(array_list == NULL)
	{
		return;
	}
	
	item = list->listHead;
	do
	{
		array_list[i] = item;
		i++;
		item = item->next;
	}while(item);
	
	//qsort(array_list, list->listCount, sizeof(struct b_node *), list->listCompare);
	timsort(array_list, list->listCount, sizeof(struct b_node *), list->listCompare);

	list->listHead = array_list[0];
	for(i=0; i<list->listCount-1; i++)
	{
		array_list[i]->next = array_list[i+1];
	}
	array_list[i]->next = NULL;
	list->listTail = array_list[i];

	free(array_list);
}
#endif

