/*
 *	Copied from Linux Monitor (LiMon) - Networking.
 *
 *	Copyright 1994 - 2000 <PERSON>.
 *	(See License)
 *	Copyright 2000 <PERSON>
 *	Copyright 2000 <PERSON>
 *	Copyright 2000-2002 <PERSON>, <EMAIL>
 */

#if defined(CONFIG_CMD_CDP)

#ifndef __CDP_H__
#define __CDP_H__

void CDPStart(void);
/* Process a received CDP packet */
void cdp_receive(const uchar *pkt, unsigned len);

#endif /* __CDP_H__ */
#endif
