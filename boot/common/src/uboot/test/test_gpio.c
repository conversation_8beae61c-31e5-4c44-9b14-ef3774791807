#include <common.h>
#include <command.h>
#include <net.h>
#include <jffs2/load_kernel.h>
#include <common.h>
#include <asm/arch/hardware.h>
#include <asm/arch/uart.h>
#include <asm/arch/lsp_crpm.h>
#include "errno.h"

//#if CONFIG_HARDWARE_TEST
#if 0
void gpio_led_on(gpio_pin_t pin, gpio_sel_t val)
{
	gpio_sel_t value = gpio_get_reuse(pin);     /*for restore*/
	gpio_set_reuse(pin,val);                    /* gpio function*/
	gpio_direction_output(pin,1);               /*out high*/
	udelay(1000000);
	gpio_direction_output(pin,0);               /*out low*/
	gpio_direction_input(pin);
	gpio_set_reuse(pin,value);                  /*restore*/
}

void gpio_leds_on(void)
{		
	gpio_led_on(GPIO9,1);   /*gpio9 default function is i2c sda1*/
	gpio_led_on(GPIO11,0);
	gpio_led_on(GPIO12,0);
//	gpio_led_on(GPIO13,0);
//    gpio_led_on(GPIO16,0);
	gpio_led_on(GPIO17,0);	
}


int do_test_gpio(void) 
{
    uint8_t i = 10;
    while( i-- )
    {
        gpio_leds_on();
    }
    return 0;
}

U_BOOT_CMD(
	test_gpio, CONFIG_SYS_MAXARGS, 0, do_test_gpio,
	"test_gpio: test_gpio ",
	""
);

#endif
