/*
 * (C) Copyright 2002
 * Stä<PERSON>li Faverges - <www.staubli.com>
 * Pierre AUBERT  <EMAIL>
 *
 * See file CREDITS for list of people who contributed to this
 * project.
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU General Public License as
 * published by the Free Software Foundation; either version 2 of
 * the License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston,
 * MA 02111-1307 USA
 */

#ifndef _FDC_H_
#define _FDC_H_

/* Functions prototype                                                       */
int fdc_fdos_init (int drive);
int fdc_fdos_seek (int where);
int fdc_fdos_read (void *buffer, int len);

int dos_open(char *name);
int dos_read (ulong addr);
int dos_dir (void);

#endif
