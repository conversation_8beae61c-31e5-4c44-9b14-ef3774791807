/* ==========================================================================
 * $File: //dwh/usb_iip/dev/software/otg/linux/drivers/dwc_otg_core_if.h $
 * $Revision: #13 $
 * $Date: 2012/08/10 $
 * $Change: 2047372 $
 *
 * Synopsys HS OTG Linux Software Driver and documentation (hereinafter,
 * "Software") is an Unsupported proprietary work of Synopsys, Inc. unless
 * otherwise expressly agreed to in writing between Synopsys and you.
 *
 * The Software IS NOT an item of Licensed Software or Licensed Product under
 * any End User Software License Agreement or Agreement for Licensed Product
 * with Synopsys or any supplement thereto. You are permitted to use and
 * redistribute this Software in source and binary forms, with or without
 * modification, provided that redistributions of source code must retain this
 * notice. You may not view, use, disclose, copy or distribute this file or
 * any information contained herein except pursuant to this license grant from
 * Synopsys. If you do not agree with this notice, including the disclaimer
 * below, then you are not authorized to use the Software.
 *
 * THIS SOFTWARE IS BEING DISTRIBUTED BY SYNOPSYS SOLELY ON AN "AS IS" BASIS
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
 * ARE HEREBY DISCLAIMED. IN NO EVENT SHALL SYNOPSYS BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
 * CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
 * LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
 * OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH
 * DAMAGE.
 * ========================================================================== */
#if !defined(__DWC_CORE_IF_H__)
#define __DWC_CORE_IF_H__
#include "common.h"
struct dwc_otg_core_if;
typedef struct dwc_otg_core_if dwc_otg_core_if_t;

#define DWC_E_NOT_SUPPORTED	1004
#define DWC_E_INVALID		    1001
#define DWC_E_NO_MEMORY		1002
#define DWC_E_NO_DEVICE		1003
#define DWC_E_SHUTDOWN		    1010

/** Maximum number of Periodic FIFOs */
#define MAX_TX_FIFOS 15

/** Maximum number of Endpoints/HostChannels */
#define MAX_EPS_CHANNELS 3

extern dwc_otg_core_if_t *dwc_otg_cil_init(const uint32_t * _reg_base_addr);
extern void dwc_otg_core_init(dwc_otg_core_if_t * _core_if);

#define DWC_OTG_CAP_PARAM_HNP_SRP_CAPABLE 0
#define DWC_OTG_CAP_PARAM_NO_HNP_SRP_CAPABLE 2
#define dwc_param_otg_cap_default DWC_OTG_CAP_PARAM_HNP_SRP_CAPABLE
#define dwc_param_opt_default 1
#define dwc_param_dma_enable_default 1
#define dwc_param_dma_desc_enable_default 1
#define dwc_param_dma_burst_size_default 32
#define dwc_param_speed_default 0
#define DWC_SPEED_PARAM_HIGH 0
#define DWC_SPEED_PARAM_FULL 1
#define dwc_param_host_support_fs_ls_low_power_default 0
#define dwc_param_host_ls_low_power_phy_clk_default 0
#define DWC_HOST_LS_LOW_POWER_PHY_CLK_PARAM_48MHZ 0
#define DWC_HOST_LS_LOW_POWER_PHY_CLK_PARAM_6MHZ 1
#define dwc_param_enable_dynamic_fifo_default 1
#define dwc_param_data_fifo_size_default 8192
#define dwc_param_dev_rx_fifo_size_default 1064
#define dwc_param_dev_nperio_tx_fifo_size_default 1024
#define dwc_param_dev_perio_tx_fifo_size_default 256
#define dwc_param_host_rx_fifo_size_default 1024
#define dwc_param_host_nperio_tx_fifo_size_default 1024
#define dwc_param_host_perio_tx_fifo_size_default 1024
#define dwc_param_max_transfer_size_default 65535
#define dwc_param_max_packet_count_default 511
#define dwc_param_host_channels_default 12
#define dwc_param_dev_endpoints_default 6
#define DWC_PHY_TYPE_PARAM_FS 0
#define DWC_PHY_TYPE_PARAM_UTMI 1
#define DWC_PHY_TYPE_PARAM_ULPI 2
#define dwc_param_phy_type_default DWC_PHY_TYPE_PARAM_UTMI
#define dwc_param_phy_utmi_width_default 16
#define dwc_param_phy_ulpi_ddr_default 0
#define DWC_PHY_ULPI_INTERNAL_VBUS 0
#define DWC_PHY_ULPI_EXTERNAL_VBUS 1
#define dwc_param_phy_ulpi_ext_vbus_default DWC_PHY_ULPI_INTERNAL_VBUS
#define dwc_param_i2c_enable_default 0
#define dwc_param_ulpi_fs_ls_default 0
#define dwc_param_ts_dline_default 0
#define dwc_param_en_multiple_tx_fifo_default 1
#define dwc_param_dev_tx_fifo_size_default 768
#define dwc_param_thr_ctl_default 0
#define dwc_param_tx_thr_length_default 64
#define dwc_param_rx_thr_length_default 64
#define dwc_param_lpm_enable_default 1
#define dwc_param_pti_enable_default 0
#define dwc_param_mpi_enable_default 0
#define dwc_param_adp_enable_default 0
#define dwc_param_ic_usb_cap_default 0
#define dwc_param_ahb_thr_ratio_default 0
#define dwc_param_power_down_default 0
#define dwc_param_reload_ctl_default 0
#define dwc_param_dev_out_nak_default 0
#define dwc_param_cont_on_bna_default 0
#define dwc_param_ahb_single_default 0

#endif /* __DWC_CORE_IF_H__ */
