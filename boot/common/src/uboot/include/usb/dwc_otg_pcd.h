/* ==========================================================================
 * $File: //dwh/usb_iip/dev/software/otg/linux/drivers/dwc_otg_pcd.h $
 * $Revision: #48 $
 * $Date: 2012/08/10 $
 * $Change: 2047372 $
 *
 * Synopsys HS OTG Linux Software Driver and documentation (hereinafter,
 * "Software") is an Unsupported proprietary work of Synopsys, Inc. unless
 * otherwise expressly agreed to in writing between Synopsys and you.
 *
 * The Software IS NOT an item of Licensed Software or Licensed Product under
 * any End User Software License Agreement or Agreement for Licensed Product
 * with Synopsys or any supplement thereto. You are permitted to use and
 * redistribute this Software in source and binary forms, with or without
 * modification, provided that redistributions of source code must retain this
 * notice. You may not view, use, disclose, copy or distribute this file or
 * any information contained herein except pursuant to this license grant from
 * Synopsys. If you do not agree with this notice, including the disclaimer
 * below, then you are not authorized to use the Software.
 *
 * THIS SOFTWARE IS BEING DISTRIBUTED BY SYNOPSYS SOLELY ON AN "AS IS" BASIS
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
 * ARE HEREBY DISCLAIMED. IN NO EVENT SHALL SYNOPSYS BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
 * CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
 * LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
 * OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH
 * DAMAGE.
 * ========================================================================== */
#ifndef DWC_HOST_ONLY
#if !defined(__DWC_PCD_H__)
#define __DWC_PCD_H__
#include "usb.h"
#include "dwc_otg_cil.h"
#include "dwc_otg_pcd_if.h"
#include "type.h"
/**
 * @file
 *
 * This file contains the structures, constants, and interfaces for
 * the Perpherial Contoller Driver (PCD).
 *
 * The Peripheral Controller Driver (PCD) for Linux will implement the
 * Gadget API, so that the existing Gadget drivers can be used. For
 * the Mass Storage Function driver the File-backed USB Storage Gadget
 * (FBS) driver will be used.  The FBS driver supports the
 * Control-Bulk (CB), Control-Bulk-Interrupt (CBI), and Bulk-Only
 * transports.
 *
 */

/**
 * Get the pointer to the core_if from the pcd pointer.
 */
#define GET_CORE_IF( _pcd ) (_pcd->core_if)

/**
 * States of EP0.
 */
typedef enum ep0_state
{
    EP0_DISCONNECT,		/* no host */
    EP0_IDLE,
    EP0_IN_DATA_PHASE,
    EP0_OUT_DATA_PHASE,
    EP0_IN_STATUS_PHASE,
    EP0_OUT_STATUS_PHASE,
    EP0_STALL,
} ep0state_e;

/** Fordward declaration.*/
struct dwc_otg_pcd;

/** DWC_otg request structure.
 * This structure is a list of requests.
 */
typedef struct dwc_otg_pcd_request
{
    void *priv;
    void *buf;
    uint32_t dma;
    uint32_t length;
    uint32_t actual;
    unsigned sent_zlp:1;
    /**
     * Used instead of original buffer if
     * it(physical address) is not dword-aligned.
     **/
    uint8_t *dw_align_buf;
    uint32_t  dw_align_buf_dma;
} dwc_otg_pcd_request_t;

/**	  PCD EP structure.
 * This structure describes an EP, there is an array of EPs in the PCD
 * structure.
 */
typedef struct dwc_otg_pcd_ep
{
    /** USB EP Descriptor */
    const usb_endpoint_descriptor_t *desc;

    /** queue of dwc_otg_pcd_requests. */
    unsigned stopped:1;
    unsigned disabling:1;
    unsigned dma:1;
    unsigned queue_sof:1;
    dwc_ep_t dwc_ep;

    /** Pointer to PCD */
    struct dwc_otg_pcd *pcd;

    void *priv;
} dwc_otg_pcd_ep_t;

/** DWC_otg PCD Structure.
 * This structure encapsulates the data for the dwc_otg PCD.
 */

typedef union
{
    usb_device_request_t req;
    uint32_t d32[2];
}u_setup_pkt;

struct dwc_otg_pcd
{
    /** The DWC otg device pointer */
    struct dwc_otg_device *otg_dev;
    /** Core Interface */
    dwc_otg_core_if_t *core_if;
    /** State of EP0 */
    ep0state_e ep0state;
    /** EP0 Request is pending */
    unsigned ep0_pending;
    /** Indicates when SET CONFIGURATION Request is in process */
    unsigned request_config;
    unsigned request_pending;

    /** SETUP packet for EP0
     * This structure is allocated as a DMA buffer on PCD initialization
     * with enough space for up to 3 setup packets.
     */
    u_setup_pkt *setup_pkt;

    uint32_t setup_pkt_dma_handle;
    /* Additional buffer and flag for CTRL_WR premature case */
    uint8_t *backup_buf;
    unsigned data_terminated;

    /** 2-byte dma buffer used to return status from GET_STATUS */
    uint16_t *status_buf;
    uint32_t  status_buf_dma_handle;
    /** EP0 */
    dwc_otg_pcd_ep_t ep0;

    /** Array of IN EPs. */
    dwc_otg_pcd_ep_t in_ep[MAX_EPS_CHANNELS - 1];
    /** Array of OUT EPs. */
    dwc_otg_pcd_ep_t out_ep[MAX_EPS_CHANNELS - 1];

#ifdef DWC_UTE_CFI
    cfiobject_t *cfi;
#endif

};

#define CLEAR_OUT_EP_INTR(__core_if,__epnum,__intr) \
do { \
		doepint_data_t doepint ; \
		doepint.d32 = 0;\
		doepint.b.__intr = 1; \
		DWC_WRITE_REG32(&__core_if->dev_if->out_ep_regs[__epnum]->doepint, \
		doepint.d32); \
} while (0)

#define CLEAR_IN_EP_INTR(__core_if,__epnum,__intr) \
do { \
		diepint_data_t diepint; \
		diepint.d32 = 0;\
		diepint.b.__intr = 1; \
		DWC_WRITE_REG32(&__core_if->dev_if->in_ep_regs[__epnum]->diepint, \
		diepint.d32); \
} while (0)

#endif
#endif /* DWC_HOST_ONLY */
