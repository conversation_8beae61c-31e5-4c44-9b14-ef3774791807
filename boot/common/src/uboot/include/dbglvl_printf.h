/*
 * (C) Copyright 2016, ZIXC Corporation.
 *
 */

#ifndef __DEBUG_LEVEL_PRINTF__
#define __DEBUG_LEVEL_PRINTF__

#define UBOOT_ERR     1
#define UBOOT_WARN    2
#define UBOOT_NOTICE  3
#define UBOOT_DBG     4
#define UBOOT_INFO    5

extern unsigned int g_uiDebugLevel;    

#define BOOT_PRINTF(Level, Fmt, Args...)  \
        do{                               \
            if (Level <= g_uiDebugLevel)  \
            {                             \
				printf("[%s]<%d>"Fmt, __FUNCTION__, __LINE__, ##Args); \
            }								    					   \
        }while(0)	

#endif	/* __DEBUG_LEVEL_PRINTF__ */

