

#ifndef _BOOT_IMAGE_H_
#define _BOOT_IMAGE_H_

typedef struct boot_img_hdr boot_img_hdr;

#define BOOT_MAGIC "ANDROID!"
#define BOOT_MAGIC_SIZE 8
#define BOOT_NAME_SIZE 16
#define BOOT_ARGS_SIZE 512

struct boot_img_hdr
{
    unsigned char magic[BOOT_MAGIC_SIZE];

    unsigned kernel_size;
    unsigned kernel_addr;

    unsigned ramdisk_size;
    unsigned ramdisk_addr;

    unsigned second_size;
    unsigned second_addr;

    unsigned tags_addr;
    unsigned page_size;
    unsigned unused[2];

    unsigned char name[BOOT_NAME_SIZE];
    
    unsigned char cmdline[BOOT_ARGS_SIZE];

    unsigned id[8];
};

#if 0
typedef struct ptentry ptentry;

struct ptentry {
    char name[16]; 
    unsigned start; 
    unsigned length;
    unsigned flags;
};


#endif

#endif
