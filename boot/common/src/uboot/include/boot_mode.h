
/*
 * (C) Copyright 2016, ZIXC Corporation.
 *
 */

#ifndef __BOOT_MODE__
#define __BOOT_MODE__

typedef enum
{
	TLOAD_MODE = 1,
	ZLOAD_MODE = 0,
	UNKNOWN_LOAD_MODE = 2
}load_mode_t;


typedef enum
{
	NORMAL_BOOT = 0,
	FOTA_UPDATE_BOOT = 1,
	UNKNOWN_BOOT_MODE
} boot_mode_t;


typedef enum
{
	RB_POWER_KEY = 0,
	RB_USB_INSERT = 1,
	RB_RTC = 2,
	RB_POWER_KEY_LONG = 3,
	RB_RESET_NOMAL = 4,
	RB_RESET_USB_OFF = 5,    /* */
	RB_RESET_EXCEPT = 6,
	RB_POWER_BOOST_IN = 7,
	RB_RESET_ALARM = 8,
	RB_AMT = 9,
	RB_PRODUCTION = 10,
	UNKNOWN_BOOT_REASON
} boot_reason_t;

typedef enum{
	POWER_ON_NORMAL = 0,
	POWER_ON_FOTA,
	POWER_ON_CHARGING,
	POWER_ON_RTC,
	POWER_ON_RESET,
	POWER_ON_HDT_TEST,
	POWER_ON_EXCEPTRESET,
	POWER_ON_LOCALUPDATE,
	POWER_ON_BOOST_IN,
	POWER_ON_AMT,
	POWER_ON_PRODUCTION,
	POWER_ON_INVALID,
}boot_type_t;

typedef enum
{
	FOTA_NORMAL = 0,
	FOTA_UPDATE = 1,
	FOTA_LOCALUPDATE = 2,
	FOTA_RECOVERY = 3,
} eFotaFlag_t;

typedef enum
{
	FOTA_PS_NORMAL = 0,
	FOTA_PS = 1
} eFotaPsFlag_t;

load_mode_t get_load_mode( void );
int get_boot_reason(void);
boot_reason_t read_boot_reason(void);
int read_boot_flashtype(void);
void set_boot_flashtype();
void set_flash_opt(void);
struct flash_ops *find_flash_type(void);
int get_battery_detect_flag(void);
int nvrw_flag_init(void);


#endif	/* __BOOT_MODE__ */
