/*
 * (C) Copyright 2001
 * <PERSON>, Custom IDEAS, <EMAIL>.
 *
 * See file CREDITS for list of people who contributed to this
 * project.
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU General Public License as
 * published by the Free Software Foundation; either version 2 of
 * the License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston,
 * MA 02111-1307 USA
 *
 * The original I2C interface was
 *   (C) 2000 by <PERSON> (<EMAIL>)
 *   AIRVENT SAM s.p.a - RIMINI(ITALY)
 * but has been changed substantially.
 */

#ifndef _I2C_H_
#define _I2C_H_

#define        DRV_I2C_ERROR_DEVICE             -101
#define        DRV_I2C_ERROR_DATA               -102
#define        DRV_I2C_ERROR_TIMEOUT            -103
#define        DRV_I2C_ERROR_TIMEOUTSELF        -104
#define        DRV_I2C_BUS_PERSIST_WAIT         -105
#define        DRV_I2C_SEND_NOT_COMPLETE        -106
#define        DRV_I2C_RECV_NOT_COMPLETE        -107
    
int i2c_init(void);

int i2c_read(uint i2c_bus, ushort slave_addr, ushort reg_addr, uint reg_len,
                                                    uchar *read_buf, uint read_len);

int i2c_write(uint i2c_bus, ushort slave_addr, ushort reg_addr, uint reg_len,
                                                uchar *write_buf, uint write_len);
#endif	/* _I2C_H_ */
