/*  
 * (C) Copyright 2016, ZIXC Corporation.
 *
 */

#ifndef _LCD_H_
#define _LCD_H_

#include <common.h>

//#include "types.h"
/* return code */
#define DRV_SUCCESS                     0         /* successed */
#define DRV_ERROR                       -1          /* failed */
#define DRV_ERR_NOT_OPENED    -9         /* the device to operate hasn't been opened yet */
#define DRV_ERR_OPEN_TIMES     -10       /* try to open a device which has been opened already */

typedef unsigned int UINT32;

typedef enum
{
    LCD_DCX_CMD = 0,    // �͵�ƽ����
    LCD_DCX_DATA,       // �ߵ�ƽ����������

    MAX_LCD_DCX_TYPE
} T_ZDrvLcd_DcxType; // transmission byte type

typedef enum
{
    LCD_XFER_POLL = 0, // ��ѯ��ʽ
    LCD_XFER_INT,       // �жϷ�ʽ
    LCD_XFER_DMA,   // DMA��ʽ
    LCD_XFER_CPU,

    MAX_LCD_XFER_TYPE
} T_ZDrvLcd_XferType;

typedef enum zx297520_lcd_vendor
{
    VENDOR_YS = 0,
    VENDOR_LD = 1,
    VENDOR_TM = 2,
    VENDOR_NUM = 3,
};

/* lcd mode */
typedef enum
{
    LCD_NORMAL,        /* normal mode */
    LCD_SLEEP,          /* sleep mode */

    MAX_LCD_MODE
} T_ZDrvLcd_Mode;

/* lcd instance */
typedef struct
{
    bool                        bOpen;  /* status: open, close */
    T_ZDrvLcd_Mode          eMode;    /* working mode: normal, sleep */
    T_ZDrvLcd_XferType      eXferType; /* poll, interrupt or DMA */
    T_ZDrvLcd_DcxType       eDcxType; /* command or parameter/data */
} T_ZDrvLcd_Instance;


typedef enum
{
    LCD_BACKLIGHT_OFF = 0,
    LCD_BACKLIGHT_ON,

} T_ZDrvLcd_BacklightStatus;

typedef unsigned int T_ZDrvLcd_Brightness; /* [0, 255]: 0 - darkest; 255 - brightest */

/* lcd module list */
typedef enum
{
	ZGD_ST7735_128x160=0,
	ZGD_GC910x_128x160,
	ZGD_GC930x_240x320, 
	ZGD_GC9306_240x320, 
	ST7789V_240x320,	
	ST7567_128x64,	
	LEAD_ST7735_128x128,
	LEAD_ST7735_128x128_F231E,
	S93521A_128x128,
	BOE7735S_128x128,	
	ST7789V_240x240,	
	ILI9342C_320x240,	
#ifdef CONFIG_QRZL_UE
    NV3023B_128x128,
#endif
	
	MAX_LCD_ITEM
} T_ZDrvLcd_Module_List;


struct T_ZDrvLcd_Lcm_Id
{
    unsigned short			dev_id;   
    unsigned char 			man_id;    
    T_ZDrvLcd_Module_List	lcd_item;    
    char *name;
};

struct T_ZDrvLcd_gpio_define
{
    UINT32	spi_cs_gpio;   
    UINT32	spi_scl_gpio; 	
    UINT32	spi_sda_gpio;   
    UINT32	spi_dcx_gpio; 
	
    UINT32	spi_cs_func_sel;   
    UINT32	spi_scl_func_sel; 	
    UINT32	spi_sda_func_sel;   
    UINT32	spi_dcx_func_sel;   
	
    UINT32	spi_cs_gpio_sel;   
    UINT32	spi_scl_gpio_sel; 	
    UINT32	spi_sda_gpio_sel;   
    UINT32	spi_dcx_gpio_sel;   
	UINT32	lcd_reset_gpio;	
	UINT32	lcd_reset_gpio_sel;	
	
	UINT32	lcd_blg_gpio;	
	UINT32	lcd_blg_gpio_sel;	
};

typedef struct
{
    unsigned short  width;
    unsigned short  height;
    unsigned short  bitsPerPixel;
    unsigned short  rMask;
    unsigned short  gMask;
    unsigned short  bMask;
    T_ZDrvLcd_Instance instance;
    T_ZDrvLcd_BacklightStatus backlight;
    T_ZDrvLcd_Brightness brightness;
	struct T_ZDrvLcd_Lcm_Id lcm_info;
	struct T_ZDrvLcd_gpio_define gpio_def;
} T_ZDrvLcd_Info;

struct lcd_opt{
    int (*get_info)(T_ZDrvLcd_Info* lcd_info);
    int (*lcm_init)(void); 
	int (*update_rect)(unsigned char *pBuf, unsigned short uiLeft, unsigned short uiTop, unsigned short uiWidth, unsigned short uiHeight);
};

int zDrvLcd_BootPicFromRes(void);
int zDrvLcd_PowerOnLogo(void);
int zDrvLcd_ShowCharging(void);
int zDrvLcd_ShowUpdateWait(void);
int zDrvLcd_ShowUpdateSucc(void);
int zDrvLcd_ShowUpdateFail(void);
int zDrvLcd_ShowLowBattery(void);
int zDrvLcd_ShowNoBattery(void);
int zDrvLcd_TurnBacklightOnOff(unsigned int enable);

int zDrvLcd_Initiate(void);
int lcd_init(void);

#endif

