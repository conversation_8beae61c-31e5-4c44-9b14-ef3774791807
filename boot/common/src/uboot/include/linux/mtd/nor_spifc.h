/*
 * Freescale QuadSPI driver.
 *
 * Copyright (C) 2013 Freescale Semiconductor, Inc.
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation; either version 2 of the License, or
 * (at your option) any later version.
 */

#ifndef _SPIFC_H_
#define _SPIFC_H_

#include <linux/mtd/spi-nor.h>

 

#define SYS_SPI_NAND_BASE           0x01407000


struct spifc_nor_reg_t
{
    uint32_t     VER_REG;					//0x00
    uint32_t     SFC_START;                 //0x04
    uint32_t     SFC_EN;                    //0x08
	uint32_t	 SFC_CTRL0;					//0x0c 
	uint32_t	 SFC_CTRL1;					//0x10
	uint32_t	 SFC_CTRL2;					//0x14 
	uint32_t	 SFC_BYTE_NUM;				//0x18
	uint32_t	 SFC_ADDR;					//0x1c
	uint32_t	 SFC_INS;					//0x20
	uint32_t	 SFC_TIMING;				//0x24
	uint32_t	 SFC_INT_EN;				//0x28
    uint32_t     SFC_INT_RAW;               //0x2c
    uint32_t     SFC_INT_SW_CLR;            //0x30
    uint32_t     SFC_SW;                    //0x34
    uint32_t     SFC_DATA;                  //0x38
};

/*spifc start 0x4*/
#define     FC_START      (1<<0)
#define     FC_BUSY       (1<<0)

/*spifc enable 0x8*/
#define     FC_EN_BACK          (1)
#define     FC_EN               (0)

/*spifc main ctr0 0xc*/
#define     FC_SCLK_PAUSE_CLR_ALLOW     (17)
#define     FC_SCLK_PAUSE_EN            (16)
#define     FC_TXFIFO_CLR               (15)
#define     FC_RXFIFO_CLR               (14)
#define     FC_TXFIFO_THRES             (10)
#define     FC_RXFIFO_THRES             (6)
#define     FC_TX_DMA_EN                (5)
#define     FC_RX_DMA_EN                (4)
#define     FC_WDOG_EN                  (3)
#define     FC_SPI_MODE                 (1)
#define     FC_WR_PROTECT               (0)

/*spifc ctrl1 0x10  in the condition : SFC_EN = 1 SFC_BUSY = 0*/
#define     FC_ADDR_TX_EN           (4)
#define     FC_DUMMY_TX_EN          (2)
#define     FC_READ_DAT_EN          (1)
#define     FC_WRITE_DAT_EN         (0)

/*spifc ctrl2 0x14*/
#define     FC_DUMMY_BYTE_NUM           (12)  /* [12:15} */
#define     FC_DUMMY_BIT_NUM            (8)   /* [8:10] */
#define     FC_ADDR_BYTE_NUM            (5)   /* [5:6] */ 
#define     FC_ADDR_MULTI_LINE_EN       (4)
#define     FC_DAT_MULTI_LINE_EN        (2)
#define     FC_TRANS_MOD                (0)

#define     FC_ADDR_BYTE_NUM_8             (0) 
#define     FC_ADDR_BYTE_NUM_16            (1) 
#define     FC_ADDR_BYTE_NUM_24            (2)  
#define     FC_ADDR_BYTE_NUM_32            (3)  


/*spifc timing 0x24*/
#define     FC_READ_DELAY           (1<<16)   /* [17:16} */
#define     FC_T_CS_SETUP           (1<<11)   /* [11:13} */
#define     FC_T_CS_HOLD            (1<<6)    /* [8:6} */
#define     FC_T_CS_DESEL           (1<<0)    /* [0:3} */


/*spifc int enable 0x28*/
#define     FC_INT_EN_TX_BYD_THES           (1<<7)
#define     FC_INT_EN_RX_BYD_THES           (1<<6)
#define     FC_INT_EN_TX_UNDERRUN           (1<<5)
#define     FC_INT_EN_RX_OVERRUN            (1<<4)
#define     FC_INT_EN_WDOG_OVERRUN          (1<<2)
#define     FC_INT_EN_FMT_ERR               (1<<1)
#define     FC_INT_EN_CMD_END               (1<<0)

/*spifc raw interrupt 0x2c*/
#define     FC_INT_RAW_TX_BYD_THES           (1<<7)
#define     FC_INT_RAW_RX_BYD_THES           (1<<6)
#define     FC_INT_RAW_TX_UNDERRUN           (1<<5)
#define     FC_INT_RAW_RX_OVERRUN            (1<<4)
#define     FC_INT_RAW_WDOG_OVERRUN          (1<<2)
#define     FC_INT_RAW_FMT_ERR               (1<<1)
#define     FC_INT_RAW_CMD_END               (1<<0)
#define     FC_INT_RAW_ERR_MASK              (FC_INT_RAW_TX_UNDERRUN|\
                                              FC_INT_RAW_RX_OVERRUN|\
                                              FC_INT_RAW_WDOG_OVERRUN|\
                                              FC_INT_RAW_FMT_ERR)

/*spifc int startus and clr 0x30*/
#define     FC_INT_CLR_TX_BYD_THES           (1<<7)
#define     FC_INT_CLR_RX_BYD_THES           (1<<6)
#define     FC_INT_CLR_TX_UNDERRUN           (1<<5)
#define     FC_INT_CLR_RX_OVERRUN            (1<<4)
#define     FC_INT_CLR_WDOG_OVERRUN          (1<<2)
#define     FC_INT_CLR_FMT_ERR               (1<<1)
#define     FC_INT_CLR_CMD_END               (1<<0)

/*spifc sw 0x34*/
#define     FC_TX_FIFO_CNT              (16)         /* [16:20} */
#define     FC_TX_FIFO_CNT_MASK         (0x1F)      /* [8:12} */
#define     FC_RX_FIFO_CNT              (8)             /* [8:12} */
#define     FC_RX_FIFO_CNT_MASK         (0x1F)      /* [8:12} */
#define     FC_TX_BYD_THRES             (1<<5)  
#define     FC_RX_BYD_THRES             (1<<4)  
#define     FC_SCLK_PAUSE_FLAG          (1<<3)  
#define     FC_WAIT_FLAG                (1<<2) 
#define     FC_FORMAT_ERR               (1<<1)  


#define     FC_DMA_NONE           		0
#define     FC_DMA_TX             		1
#define     FC_DMA_RX             		2


#define TX_DMA_EN		1 
#define TX_DMA_DIS      0 
#define RX_DMA_EN		1 
#define RX_DMA_DIS      0
#define ADDR_TX_EN      1   
#define ADDR_TX_DIS     0
#define DATA_TX_EN      1
#define DATA_TX_DIS     0
#define DATA_RX_EN      1
#define DATA_RX_DIS     0
#define DUMY_TX_EN      1
#define DUMY_TX_DIS     0
#define ADDR_MULTI_LINE_EN		1 
#define ADDR_MULTI_LINE_DIS      0
#define DATA_MULTI_LINE_EN		1 
#define DATA_MULTI_LINE_DIS      0
#define TRANS_MOD_QUAD		1 
#define TRANS_MOD_DUAL      0
#define TRANS_MOD_SINGLE	2


#define ADDR_WIDTH_8    0
#define ADDR_WIDTH_16   1
#define ADDR_WIDTH_24   2
#define ADDR_WIDTH_32   3



typedef struct spinor_cmd
{
    u8 cmd;
	u8 tx_dma_en;
	u8 rx_dma_en;
    u8 addr_tx_en;
    u8 addr_byte_num;
    u8 data_tx_en;
    u8 data_rx_en;
    u8 dumy_tx_en;
    u8 dumy_byte_num;
	u8 dumy_bit_num;
	u8 addr_multi_line_en;
	u8 data_multi_line_en;
	u8 trans_mod;
	u8 reserved[3];
    u8 *info;
}spinor_cmd_t;


#define CMD_WREN                        0x06
#define CMD_WRDI                        0x04
#define CMD_WRENVSR                     0x50
#define CMD_RDSR0                       0x05
#define CMD_RDSR1                       0x35
#define CMD_WRSR                        0x01
#define CMD_RDB			            	0x03
#define CMD_RDFT                   		0x0B
#define CMD_RDDFT                       0x3B
#define CMD_RDQFT                       0x6B
#define CMD_PP                          0x02 
#define CMD_QPP                         0x32
#define CMD_SE                          0x20
#define CMD_32KBE                       0x52
#define CMD_64KBE                       0xD8
#define CMD_CE                          0x60
#define CMD_DP                          0xB9
#define CMD_RDPRDI                      0xAB
#define CMD_REMS                     	0x90
#define CMD_RDID                    	0x9F
#define CMD_PES                    		0x75
#define CMD_PER                    		0x7A
#define CMD_ESR                    		0x44
#define CMD_PSR                    		0x42
#define CMD_RSR                    		0x48
#define CMD_ENRESET                    	0x66
#define CMD_RESET                    	0x99


enum fsl_qspi_devtype {
	FSL_QUADSPI_VYBRID,
	FSL_QUADSPI_IMX6SX,
	FSL_QUADSPI_IMX7D,
	FSL_QUADSPI_IMX6UL,
};

struct fsl_qspi_devtype_data {
	enum fsl_qspi_devtype devtype;
	int rxfifo;
	int txfifo;
	int ahb_buf_size;
	int driver_data;
};


#define NOR_MAX_PAGE_SIZE	2048
#define SPI_NOR_BUF_SIZE	NOR_MAX_PAGE_SIZE

struct spi_nor_buf
{
    uint32_t head;
    uint32_t tail;
	uint8_t _buf[SPI_NOR_BUF_SIZE];
	uint8_t *buf;	
    dma_addr_t dma_buf;
};




#define FSL_QSPI_MAX_CHIP	1
struct fsl_qspi {
	struct spi_nor nor[FSL_QSPI_MAX_CHIP];
	struct spi_nor_buf buf;
	void __iomem *iobase;
	void __iomem *ahb_addr;
	u32 memmap_phy;
	u32 memmap_offs;
	u32 memmap_len;
	struct clk *clk, *clk_en;
	struct device *dev;
	//struct completion c;
	struct fsl_qspi_devtype_data *devtype_data;
	u32 nor_size;
	u32 nor_num;
	u32 clk_rate;
	unsigned int chip_base_addr; /* We may support two chips. */
	bool has_second_chip;
	//struct mutex lock;
	//struct pm_qos_request pm_qos_req;
};

#endif /* _SPIFC_H_ */

