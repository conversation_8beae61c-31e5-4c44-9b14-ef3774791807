/*
 * MTD device concatenation layer definitions
 *
 * (C) 2002 <PERSON> <<EMAIL>>
 *
 * This code is GPL
 */

#ifndef MTD_CONCAT_H
#define MTD_CONCAT_H

struct mtd_info *mtd_concat_create(
    struct mtd_info *subdev[],  /* subdevices to concatenate */
    int num_devs,               /* number of subdevices      */
    const char *name);          /* name for the new device   */

void mtd_concat_destroy(struct mtd_info *mtd);

#endif
