/*
 *  linux/include/linux/mtd/bbm.h
 *
 *  NAND family Bad Block Management (BBM) header file
 *    - Bad Block Table (BBT) implementation
 *
 *  Copyright (c) 2005-2007 Samsung Electronics
 *  Kyungmin Park <<EMAIL>>
 *
 *  Copyright (c) 2000-2005
 *  <PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License version 2 as
 * published by the Free Software Foundation.
 */
#ifndef __LINUX_MTD_BBM_H
#define __LINUX_MTD_BBM_H

/* The maximum number of NAND chips in an array */
#ifndef CONFIG_SYS_NAND_MAX_CHIPS
#define CONFIG_SYS_NAND_MAX_CHIPS	9
#endif

/**
 * struct nand_bbt_descr - bad block table descriptor
 * @param options	options for this descriptor
 * @param pages		the page(s) where we find the bbt, used with
 *			option BBT_ABSPAGE when bbt is searched,
 *			then we store the found bbts pages here.
 *			Its an array and supports up to 8 chips now
 * @param offs		offset of the pattern in the oob area of the page
 * @param veroffs	offset of the bbt version counter in the oob are of the page
 * @param version	version read from the bbt page during scan
 * @param len		length of the pattern, if 0 no pattern check is performed
 * @param maxblocks	maximum number of blocks to search for a bbt. This number of
 *			blocks is reserved at the end of the device
 *			where the tables are written.
 * @param reserved_block_code	if non-0, this pattern denotes a reserved
 *			(rather than bad) block in the stored bbt
 * @param pattern	pattern to identify bad block table or factory marked
 *			good / bad blocks, can be NULL, if len = 0
 *
 * Descriptor for the bad block table marker and the descriptor for the
 * pattern which identifies good and bad blocks. The assumption is made
 * that the pattern and the version count are always located in the oob area
 * of the first block.
 */
struct nand_bbt_descr {
	int options;
	int pages[CONFIG_SYS_NAND_MAX_CHIPS];
	int offs;
	int veroffs;
	uint8_t version[CONFIG_SYS_NAND_MAX_CHIPS];
	int len;
	int maxblocks;
	int reserved_block_code;
	uint8_t *pattern;
};

/* Options for the bad block table descriptors */

/* The number of bits used per block in the bbt on the device */
#define NAND_BBT_NRBITS_MSK	0x0000000F
#define NAND_BBT_1BIT		0x00000001
#define NAND_BBT_2BIT		0x00000002
#define NAND_BBT_4BIT		0x00000004
#define NAND_BBT_8BIT		0x00000008
/* The bad block table is in the last good block of the device */
#define NAND_BBT_LASTBLOCK	0x00000010
/* The bbt is at the given page, else we must scan for the bbt */
#define NAND_BBT_ABSPAGE	0x00000020
/* The bbt is at the given page, else we must scan for the bbt */
#define NAND_BBT_SEARCH		0x00000040
/* bbt is stored per chip on multichip devices */
#define NAND_BBT_PERCHIP	0x00000080
/* bbt has a version counter at offset veroffs */
#define NAND_BBT_VERSION	0x00000100
/* Create a bbt if none axists */
#define NAND_BBT_CREATE		0x00000200
/* Search good / bad pattern through all pages of a block */
#define NAND_BBT_SCANALLPAGES	0x00000400
/* Scan block empty during good / bad block scan */
#define NAND_BBT_SCANEMPTY	0x00000800
/* Write bbt if neccecary */
#define NAND_BBT_WRITE		0x00001000
/* Read and write back block contents when writing bbt */
#define NAND_BBT_SAVECONTENT	0x00002000
/* Search good / bad pattern on the first and the second page */
#define NAND_BBT_SCAN2NDPAGE	0x00004000

/* The maximum number of blocks to scan for a bbt */
#define NAND_BBT_SCAN_MAXBLOCKS	4

/*
 * Constants for oob configuration
 */
#define ONENAND_BADBLOCK_POS	0

/*
 * Bad block scanning errors
 */
#define ONENAND_BBT_READ_ERROR          1
#define ONENAND_BBT_READ_ECC_ERROR      2
#define ONENAND_BBT_READ_FATAL_ERROR    4

/**
 * struct bbt_info - [GENERIC] Bad Block Table data structure
 * @param bbt_erase_shift	[INTERN] number of address bits in a bbt entry
 * @param badblockpos		[INTERN] position of the bad block marker in the oob area
 * @param bbt			[INTERN] bad block table pointer
 * @param badblock_pattern	[REPLACEABLE] bad block scan pattern used for initial bad block scan
 * @param priv			[OPTIONAL] pointer to private bbm date
 */
struct bbm_info {
	int bbt_erase_shift;
	int badblockpos;
	int options;

	uint8_t *bbt;

	int (*isbad_bbt) (struct mtd_info * mtd, loff_t ofs, int allowbbt);

	/* TODO Add more NAND specific fileds */
	struct nand_bbt_descr *badblock_pattern;

	void *priv;
};

/* OneNAND BBT interface */
extern int onenand_scan_bbt (struct mtd_info *mtd, struct nand_bbt_descr *bd);
extern int onenand_default_bbt (struct mtd_info *mtd);

#endif				/* __LINUX_MTD_BBM_H */
