/*
 *  linux/include/linux/mtd/nand.h
 */
#ifndef __LINUX_MTD_NAND_LEGACY_H
#define __LINUX_MTD_NAND_LEGACY_H


#define NAND_CMD_READ0		0
#define NAND_CMD_READ1		1
#define NAND_CMD_PAGEPROG	0x10
#define NAND_CMD_READOOB	0x50
#define NAND_CMD_ERASE1		0x60
#define NAND_CMD_STATUS		0x70
#define NAND_CMD_SEQIN		0x80
#define NAND_CMD_READID		0x90
#define NAND_CMD_ERASE2		0xd0
#define NAND_CMD_RESET		0xff


typedef enum {
	FL_READY,
	FL_READING,
	FL_WRITING,
	FL_ERASING,
	FL_SYNCING
} nand_state_t;



struct Nand {
	char floor, chip;
	unsigned long curadr;
	unsigned char curmode;
};

struct nand_chip {
	int 	    page_shift;
	u_char 	    *data_buf;
	u_char 		*data_cache;
	int		    cache_page;
	u_char 		ecc_code_buf[6];
	u_char 		reserved[2];
	char        ChipID; 
	struct Nand *chips;
	int         chipshift;
	char        *chips_name;
    ulong       pagesize;
	ulong       erasesize;
	ulong       mfr; 
	ulong       id;
	char        * name;
	int         numchips;
	char        page256;
    char        columnadrlen;
	char        pageadrlen;
	ulong       IO_ADDR;
	ulong       totlen;
	uint        oobblock;
	uint        oobsize;
	uint        eccsize;
	int         bus16;
};

struct nand_flash_dev {
	char * name;
	int manufacture_id;
	int model_id;
	int chipshift;
	char page256;
	char pageadrlen;
	unsigned long erasesize;
	int bus16;
};

#endif
