
#ifndef __ZFTL_ECC_H__
#define __ZFTL_ECC_H__

struct zftl_ecc_other {
	unsigned line_parity;
	unsigned line_parity_prime;
	unsigned char col_parity;
}__attribute__((packed));

enum zftl_ecc_result {
	ZFTL_ECC_RESULT_UNKNOWN,
	ZFTL_ECC_RESULT_NO_ERROR,
	ZFTL_ECC_RESULT_FIXED,
	ZFTL_ECC_RESULT_UNFIXED
};




void zftl_ecc_calc_other(const unsigned char *data, unsigned n_bytes,
			  struct zftl_ecc_other *ecc);
int zftl_ecc_correct_other(unsigned char *data, unsigned n_bytes,
			    struct zftl_ecc_other *read_ecc,
			    const struct zftl_ecc_other *test_ecc);
#endif
