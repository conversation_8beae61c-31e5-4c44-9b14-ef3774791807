/*  
 * (C) Copyright 2016, ZIXC Corporation.
 *
 */

#ifndef __ZX234290_H__
#define __ZX234290_H__
#include <drvs_gpio.h>
//#define PS_HOLD_PIN     GPIO51

#define  ZX234290_I2C_SLAVE_ADDR	0x12
#define  ZX234296_REG_PWRKEY_CTRL2	0x43
#define  ZX234290_REG_ADDR_STSA		0x04

#define  INTR_A						0x0
#define  INTR_B						0x1

#define  INTR_MASK_A				0x2
#define  INTR_EOADC              	(0x1 << 2)
#define  INTR_PWR_ON_SHORT_PRESS  	(0x1 << 3)
#define  INTR_PWR_ON_LONG_PRESS   	(0x1 << 4)
#define  INTR_PWR_ON              	(0x1 << 5)

#define  INTR_MASK_B				0x3

#define  STATUS_A					0x4
#define  STATUS_EOADC            	(0x1 << 2)
#define  STATUS_PWR_ON           	(0x1 << 5)
#define  STATUS_B                   0x5
#define  STATUS_RTC_ALARM			(0x1 << 0)

#define  START_UP_STATUS			0x6
#define  PWR_ON_START_UP			(0x1 << 0)
#define  RTC_ALARM_START_UP      	(0x1 << 1)
#define  PS_HOLD_START_UP        	(0x1 << 2)
#define  LLP_RESTART_UP          	(0x1 << 3)

#define  SYS_CONTROL				0x7
//#define     SINK1_ENABLE            (0x1 << 0)
//#define     SINK2_ENABLE            (0x1 << 1)
#define  ADC1_ENABLE             	(0x1 << 3)
#define  ADC2_ENABLE            	(0x1 << 4)

#define  BUCK_MODE_CONTROL0			0xe
#define  BUCK_MODE_CONTROL1			0xf
#define  MMC_CTRL					0x15
#define ZX234290_REG_ADDR_LDO78_VOL		0x15
#define ZX234290_REG_ADDR_LDO78_SLPVOL	0x1B

#define ZX234290_LDO8_VSEL_LSH		(4)
#define ZX234290_LDO8_SLP_VSEL_LSH	(4)

#define ZX234290_REG_LDO_EN1		0x21	/* LDO8-1 */
#define ZX234290_REG_ADC_ADC2MSB	0x27	/* CHANNEL 2	*/
#define ZX234290_REG_ADC_ADC2LSB	0x28
#define ZX234290_REG_ADC_ADC1MSB	0x25	/* CHANNEL 2	*/
#define ZX234290_REG_ADC_ADC1LSB	0x26
#define ZX234290_REG_ADC_VBATMSB	0x23	/* CHANNEL 2	*/
#define ZX234290_REG_ADC_VBATLSB	0x24

#define ZX234290_REG_RTC_CONTROL2	0x31
#define	RTC_CONTROL2_TIE			(1 << 0)
#define	RTC_CONTROL2_AIE			(1 << 1)
#define	RTC_CONTROL2_TF				(1 << 2)
#define	RTC_CONTROL2_AF				(1 << 3)

#define ZX234290_REG_USER         	0x50

/*define reset flag*/
#define ZX234290_USER_RST_FLAG		(0x0<<4)/*0x00*/
#define ZX234290_WDT_RST_FLAG		(0x1<<4)/*0x10*/
#define ZX234290_USER_RST_UNDEFINE	(0x0<<0)/*0x0*/
#define ZX234290_USER_RST_TO_NORMAL	(0x1<<0)/*0x1*/
#define ZX234290_USER_RST_TO_CHARGER (0x2<<0)/*0x2*/
#define ZX234290_USER_RST_TO_ALARM	(0x3<<0)/*0x3*/
#define ZX234290_USER_RST_TO_EXCEPT	(0x4<<0)/*0x4*/


#define ZX234290_REG_INTA         	0x00    /* INTERRUPT */
#define ZX234290_REG_INTB          	0x01


#define ZX234290_REG_SYS_CTRL		0x07
/* sink control */
#define ZX234297_REG_ADDR_SINK_CONTROL	0x29
#define ZX234290_REG_ADDR_LDO_EN2		0x22	/* [xx xx]BUCK4-1, [0xx0]LDO10-9*/

/* 0x22 */
#define ZX234297_SINK1_ON_LSH			(2)
#define ZX234297_SINK2_ON_LSH			(3)
#define ZX234297_SINK_ON_WID			(1)
/* 0x29 */
#define ZX234297_SINK1_CURRENT_LSH		(0)
#define ZX234297_SINK2_CURRENT_LSH		(4)
#define ZX234297_SINK_CURRENT_WID		(4)

typedef enum _T_ZX234290_SINK
{
	SINK_1 = 0,
	SINK_2 = 1,
	SINK_MAX
}T_ZX234290_SINK;

typedef enum _T_ZX234290_SINK_CURRENT
{
	SINK_CURRENT_5MA,
	SINK_CURRENT_10MA,
	SINK_CURRENT_15MA,
	SINK_CURRENT_20MA,
	SINK_CURRENT_30MA,
	SINK_CURRENT_40MA,
	SINK_CURRENT_50MA,
	SINK_CURRENT_60MA,
	SINK_CURRENT_70MA,
	SINK_CURRENT_80MA,
	SINK_CURRENT_90MA,
	SINK_CURRENT_100MA,
	SINK_CURRENT_110MA,
	SINK_CURRENT_120MA,

    SINK_CURRENT_MAX
}T_ZX234297_SINK_CURRENT;

typedef enum _T_ZDrvZx234290_VldoD
{
    VLDOD_1_400 = 0,
	VLDOD_1_500 = 1,
	VLDOD_1_600 = 2,
	VLDOD_1_800 = 3,
	VLDOD_1_850 = 4,
	VLDOD_2_000 = 5,
	VLDOD_2_050 = 6,
    VLDOD_2_500 = 7,
    VLDOD_2_550 = 8,
    VLDOD_2_700 = 9,
    VLDOD_2_750 = 10,
    VLDOD_2_800 = 11,
    VLDOD_2_850 = 12,
    VLDOD_2_900 = 13,
    VLDOD_2_950 = 14,
    VLDOD_3_000 = 15,

    VLDOD_MAX

}T_ZDrvZx234290_VldoD;


/* pwr_on long pressed time	*/
#define ZX234290_REG_ADDR_PWRON				0x42

int zx234290_reset_flag(void);
int zx234290_write_flag(UINT8 val);
int pmu_pull_off_ps_hold(void);
int pmu_pull_on_ps_hold(void);

/*get the poweron key state 1: poweron press 0:poweron up*/
int zx234290_get_poweron_state(void);
int zx234290_get_rtc_state(void);
int zx234290_get_adc1_voltage(void);
int zx234290_get_adc2_voltage(void);
int zx234290_get_vbat_voltage(void);
int zx234290_set_softon(int on);
int zx234290_ldo8_enable(int enable);
int zx234290_set_sink(T_ZX234290_SINK sink_num, int is_on, T_ZX234297_SINK_CURRENT sink_current);


#endif	/* __ZX234290_H__ */
