/*
 *	PCI Class, Vendor and Device IDs
 *
 *	Please keep sorted.
 */

/* Device classes and subclasses */

#define PCI_CLASS_NOT_DEFINED		0x0000
#define PCI_CLASS_NOT_DEFINED_VGA	0x0001

#define PCI_BASE_CLASS_STORAGE		0x01
#define PCI_CLASS_STORAGE_SCSI		0x0100
#define PCI_CLASS_STORAGE_IDE		0x0101
#define PCI_CLASS_STORAGE_FLOPPY	0x0102
#define PCI_CLASS_STORAGE_IPI		0x0103
#define PCI_CLASS_STORAGE_RAID		0x0104
#define PCI_CLASS_STORAGE_OTHER		0x0180

#define PCI_BASE_CLASS_NETWORK		0x02
#define PCI_CLASS_NETWORK_ETHERNET	0x0200
#define PCI_CLASS_NETWORK_TOKEN_RING	0x0201
#define PCI_CLASS_NETWORK_FDDI		0x0202
#define PCI_CLASS_NETWORK_ATM		0x0203
#define PCI_CLASS_NETWORK_OTHER		0x0280

#define PCI_BASE_CLASS_DISPLAY		0x03
#define PCI_CLASS_DISPLAY_VGA		0x0300
#define PCI_CLASS_DISPLAY_XGA		0x0301
#define PCI_CLASS_DISPLAY_3D		0x0302
#define PCI_CLASS_DISPLAY_OTHER		0x0380

#define PCI_BASE_CLASS_MULTIMEDIA	0x04
#define PCI_CLASS_MULTIMEDIA_VIDEO	0x0400
#define PCI_CLASS_MULTIMEDIA_AUDIO	0x0401
#define PCI_CLASS_MULTIMEDIA_PHONE	0x0402
#define PCI_CLASS_MULTIMEDIA_OTHER	0x0480

#define PCI_BASE_CLASS_MEMORY		0x05
#define PCI_CLASS_MEMORY_RAM		0x0500
#define PCI_CLASS_MEMORY_FLASH		0x0501
#define PCI_CLASS_MEMORY_OTHER		0x0580

#define PCI_BASE_CLASS_BRIDGE		0x06
#define PCI_CLASS_BRIDGE_HOST		0x0600
#define PCI_CLASS_BRIDGE_ISA		0x0601
#define PCI_CLASS_BRIDGE_EISA		0x0602
#define PCI_CLASS_BRIDGE_MC		0x0603
#define PCI_CLASS_BRIDGE_PCI		0x0604
#define PCI_CLASS_BRIDGE_PCMCIA		0x0605
#define PCI_CLASS_BRIDGE_NUBUS		0x0606
#define PCI_CLASS_BRIDGE_CARDBUS	0x0607
#define PCI_CLASS_BRIDGE_RACEWAY	0x0608
#define PCI_CLASS_BRIDGE_OTHER		0x0680

#define PCI_BASE_CLASS_COMMUNICATION	0x07
#define PCI_CLASS_COMMUNICATION_SERIAL	0x0700
#define PCI_CLASS_COMMUNICATION_PARALLEL 0x0701
#define PCI_CLASS_COMMUNICATION_MULTISERIAL 0x0702
#define PCI_CLASS_COMMUNICATION_MODEM	0x0703
#define PCI_CLASS_COMMUNICATION_OTHER	0x0780

#define PCI_BASE_CLASS_SYSTEM		0x08
#define PCI_CLASS_SYSTEM_PIC		0x0800
#define PCI_CLASS_SYSTEM_DMA		0x0801
#define PCI_CLASS_SYSTEM_TIMER		0x0802
#define PCI_CLASS_SYSTEM_RTC		0x0803
#define PCI_CLASS_SYSTEM_PCI_HOTPLUG	0x0804
#define PCI_CLASS_SYSTEM_OTHER		0x0880

#define PCI_BASE_CLASS_INPUT		0x09
#define PCI_CLASS_INPUT_KEYBOARD	0x0900
#define PCI_CLASS_INPUT_PEN		0x0901
#define PCI_CLASS_INPUT_MOUSE		0x0902
#define PCI_CLASS_INPUT_SCANNER		0x0903
#define PCI_CLASS_INPUT_GAMEPORT	0x0904
#define PCI_CLASS_INPUT_OTHER		0x0980

#define PCI_BASE_CLASS_DOCKING		0x0a
#define PCI_CLASS_DOCKING_GENERIC	0x0a00
#define PCI_CLASS_DOCKING_OTHER		0x0a80

#define PCI_BASE_CLASS_PROCESSOR	0x0b
#define PCI_CLASS_PROCESSOR_386		0x0b00
#define PCI_CLASS_PROCESSOR_486		0x0b01
#define PCI_CLASS_PROCESSOR_PENTIUM	0x0b02
#define PCI_CLASS_PROCESSOR_ALPHA	0x0b10
#define PCI_CLASS_PROCESSOR_POWERPC	0x0b20
#define PCI_CLASS_PROCESSOR_MIPS	0x0b30
#define PCI_CLASS_PROCESSOR_CO		0x0b40

#define PCI_BASE_CLASS_SERIAL		0x0c
#define PCI_CLASS_SERIAL_FIREWIRE	0x0c00
#define PCI_CLASS_SERIAL_ACCESS		0x0c01
#define PCI_CLASS_SERIAL_SSA		0x0c02
#define PCI_CLASS_SERIAL_USB		0x0c03
#define PCI_CLASS_SERIAL_FIBER		0x0c04
#define PCI_CLASS_SERIAL_SMBUS		0x0c05

#define PCI_BASE_CLASS_INTELLIGENT	0x0e
#define PCI_CLASS_INTELLIGENT_I2O	0x0e00

#define PCI_BASE_CLASS_SATELLITE	0x0f
#define PCI_CLASS_SATELLITE_TV		0x0f00
#define PCI_CLASS_SATELLITE_AUDIO	0x0f01
#define PCI_CLASS_SATELLITE_VOICE	0x0f03
#define PCI_CLASS_SATELLITE_DATA	0x0f04

#define PCI_BASE_CLASS_CRYPT		0x10
#define PCI_CLASS_CRYPT_NETWORK		0x1000
#define PCI_CLASS_CRYPT_ENTERTAINMENT	0x1001
#define PCI_CLASS_CRYPT_OTHER		0x1080

#define PCI_BASE_CLASS_SIGNAL_PROCESSING 0x11
#define PCI_CLASS_SP_DPIO		0x1100
#define PCI_CLASS_SP_OTHER		0x1180

#define PCI_CLASS_OTHERS		0xff

/* Vendors and devices.	 Sort key: vendor first, device next. */

#define PCI_VENDOR_ID_DYNALINK		0x0675
#define PCI_DEVICE_ID_DYNALINK_IS64PH	0x1702

#define PCI_VENDOR_ID_BERKOM			0x0871
#define PCI_DEVICE_ID_BERKOM_A1T		0xffa1
#define PCI_DEVICE_ID_BERKOM_T_CONCEPT		0xffa2
#define PCI_DEVICE_ID_BERKOM_A4T		0xffa4
#define PCI_DEVICE_ID_BERKOM_SCITEL_QUADRO	0xffa8

#define PCI_VENDOR_ID_COMPAQ		0x0e11
#define PCI_DEVICE_ID_COMPAQ_TOKENRING	0x0508
#define PCI_DEVICE_ID_COMPAQ_1280	0x3033
#define PCI_DEVICE_ID_COMPAQ_TRIFLEX	0x4000
#define PCI_DEVICE_ID_COMPAQ_6010	0x6010
#define PCI_DEVICE_ID_COMPAQ_TACHYON	0xa0fc
#define PCI_DEVICE_ID_COMPAQ_SMART2P	0xae10
#define PCI_DEVICE_ID_COMPAQ_NETEL100	0xae32
#define PCI_DEVICE_ID_COMPAQ_TRIFLEX_IDE 0xae33
#define PCI_DEVICE_ID_COMPAQ_NETEL10	0xae34
#define PCI_DEVICE_ID_COMPAQ_NETFLEX3I	0xae35
#define PCI_DEVICE_ID_COMPAQ_NETEL100D	0xae40
#define PCI_DEVICE_ID_COMPAQ_NETEL100PI 0xae43
#define PCI_DEVICE_ID_COMPAQ_NETEL100I	0xb011
#define PCI_DEVICE_ID_COMPAQ_CISS	0xb060
#define PCI_DEVICE_ID_COMPAQ_CISSB	0xb178
#define PCI_DEVICE_ID_COMPAQ_CISSC	0x0046
#define PCI_DEVICE_ID_COMPAQ_THUNDER	0xf130
#define PCI_DEVICE_ID_COMPAQ_NETFLEX3B	0xf150

#define PCI_VENDOR_ID_NCR		0x1000
#define PCI_VENDOR_ID_LSI_LOGIC		0x1000
#define PCI_DEVICE_ID_NCR_53C810	0x0001
#define PCI_DEVICE_ID_NCR_53C820	0x0002
#define PCI_DEVICE_ID_NCR_53C825	0x0003
#define PCI_DEVICE_ID_NCR_53C815	0x0004
#define PCI_DEVICE_ID_LSI_53C810AP	0x0005
#define PCI_DEVICE_ID_NCR_53C860	0x0006
#define PCI_DEVICE_ID_LSI_53C1510	0x000a
#define PCI_DEVICE_ID_NCR_53C896	0x000b
#define PCI_DEVICE_ID_NCR_53C895	0x000c
#define PCI_DEVICE_ID_NCR_53C885	0x000d
#define PCI_DEVICE_ID_NCR_53C875	0x000f
#define PCI_DEVICE_ID_NCR_53C1510	0x0010
#define PCI_DEVICE_ID_LSI_53C895A	0x0012
#define PCI_DEVICE_ID_LSI_53C875A	0x0013
#define PCI_DEVICE_ID_LSI_53C1010_33	0x0020
#define PCI_DEVICE_ID_LSI_53C1010_66	0x0021
#define PCI_DEVICE_ID_LSI_53C1030	0x0030
#define PCI_DEVICE_ID_LSI_53C1035	0x0040
#define PCI_DEVICE_ID_NCR_53C875J	0x008f
#define PCI_DEVICE_ID_LSI_FC909		0x0621
#define PCI_DEVICE_ID_LSI_FC929		0x0622
#define PCI_DEVICE_ID_LSI_FC929_LAN	0x0623
#define PCI_DEVICE_ID_LSI_FC919		0x0624
#define PCI_DEVICE_ID_LSI_FC919_LAN	0x0625
#define PCI_DEVICE_ID_LSI_FC929X	0x0626
#define PCI_DEVICE_ID_LSI_FC919X	0x0628
#define PCI_DEVICE_ID_NCR_YELLOWFIN	0x0701
#define PCI_DEVICE_ID_LSI_61C102	0x0901
#define PCI_DEVICE_ID_LSI_63C815	0x1000

#define PCI_VENDOR_ID_ATI		0x1002
/* Mach64 */
#define PCI_DEVICE_ID_ATI_68800		0x4158
#define PCI_DEVICE_ID_ATI_215CT222	0x4354
#define PCI_DEVICE_ID_ATI_210888CX	0x4358
#define PCI_DEVICE_ID_ATI_215ET222	0x4554
/* Mach64 / Rage */
#define PCI_DEVICE_ID_ATI_215GB		0x4742
#define PCI_DEVICE_ID_ATI_215GD		0x4744
#define PCI_DEVICE_ID_ATI_215GI		0x4749
#define PCI_DEVICE_ID_ATI_215GP		0x4750
#define PCI_DEVICE_ID_ATI_215GQ		0x4751
#define PCI_DEVICE_ID_ATI_215XL		0x4752
#define PCI_DEVICE_ID_ATI_215GT		0x4754
#define PCI_DEVICE_ID_ATI_215GTB	0x4755
#define PCI_DEVICE_ID_ATI_215_IV	0x4756
#define PCI_DEVICE_ID_ATI_215_IW	0x4757
#define PCI_DEVICE_ID_ATI_215_IZ	0x475A
#define PCI_DEVICE_ID_ATI_210888GX	0x4758
#define PCI_DEVICE_ID_ATI_215_LB	0x4c42
#define PCI_DEVICE_ID_ATI_215_LD	0x4c44
#define PCI_DEVICE_ID_ATI_215_LG	0x4c47
#define PCI_DEVICE_ID_ATI_215_LI	0x4c49
#define PCI_DEVICE_ID_ATI_215_LM	0x4c4D
#define PCI_DEVICE_ID_ATI_215_LN	0x4c4E
#define PCI_DEVICE_ID_ATI_215_LR	0x4c52
#define PCI_DEVICE_ID_ATI_215_LS	0x4c53
#define PCI_DEVICE_ID_ATI_264_LT	0x4c54
/* Mach64 VT */
#define PCI_DEVICE_ID_ATI_264VT		0x5654
#define PCI_DEVICE_ID_ATI_264VU		0x5655
#define PCI_DEVICE_ID_ATI_264VV		0x5656
/* Rage128 Pro GL */
#define PCI_DEVICE_ID_ATI_Rage128_PA	0x5041
#define PCI_DEVICE_ID_ATI_Rage128_PB	0x5042
#define PCI_DEVICE_ID_ATI_Rage128_PC	0x5043
#define PCI_DEVICE_ID_ATI_Rage128_PD	0x5044
#define PCI_DEVICE_ID_ATI_Rage128_PE	0x5045
#define PCI_DEVICE_ID_ATI_RAGE128_PF	0x5046
/* Rage128 Pro VR */
#define PCI_DEVICE_ID_ATI_RAGE128_PG	0x5047
#define PCI_DEVICE_ID_ATI_RAGE128_PH	0x5048
#define PCI_DEVICE_ID_ATI_RAGE128_PI	0x5049
#define PCI_DEVICE_ID_ATI_RAGE128_PJ	0x504A
#define PCI_DEVICE_ID_ATI_RAGE128_PK	0x504B
#define PCI_DEVICE_ID_ATI_RAGE128_PL	0x504C
#define PCI_DEVICE_ID_ATI_RAGE128_PM	0x504D
#define PCI_DEVICE_ID_ATI_RAGE128_PN	0x504E
#define PCI_DEVICE_ID_ATI_RAGE128_PO	0x504F
#define PCI_DEVICE_ID_ATI_RAGE128_PP	0x5050
#define PCI_DEVICE_ID_ATI_RAGE128_PQ	0x5051
#define PCI_DEVICE_ID_ATI_RAGE128_PR	0x5052
#define PCI_DEVICE_ID_ATI_RAGE128_TR	0x5452
#define PCI_DEVICE_ID_ATI_RAGE128_PS	0x5053
#define PCI_DEVICE_ID_ATI_RAGE128_PT	0x5054
#define PCI_DEVICE_ID_ATI_RAGE128_PU	0x5055
#define PCI_DEVICE_ID_ATI_RAGE128_PV	0x5056
#define PCI_DEVICE_ID_ATI_RAGE128_PW	0x5057
#define PCI_DEVICE_ID_ATI_RAGE128_PX	0x5058
/* Rage128 GL */
#define PCI_DEVICE_ID_ATI_RAGE128_RE	0x5245
#define PCI_DEVICE_ID_ATI_RAGE128_RF	0x5246
#define PCI_DEVICE_ID_ATI_RAGE128_RG	0x534b
#define PCI_DEVICE_ID_ATI_RAGE128_RH	0x534c
#define PCI_DEVICE_ID_ATI_RAGE128_RI	0x534d
/* Rage128 VR */
#define PCI_DEVICE_ID_ATI_RAGE128_RK	0x524b
#define PCI_DEVICE_ID_ATI_RAGE128_RL	0x524c
#define PCI_DEVICE_ID_ATI_RAGE128_RM	0x5345
#define PCI_DEVICE_ID_ATI_RAGE128_RN	0x5346
#define PCI_DEVICE_ID_ATI_RAGE128_RO	0x5347
/* Rage128 M3 */
#define PCI_DEVICE_ID_ATI_RAGE128_LE	0x4c45
#define PCI_DEVICE_ID_ATI_RAGE128_LF	0x4c46
/* Rage128 Pro Ultra */
#define PCI_DEVICE_ID_ATI_RAGE128_U1	0x5446
#define PCI_DEVICE_ID_ATI_RAGE128_U2	0x544C
#define PCI_DEVICE_ID_ATI_RAGE128_U3	0x5452
/* Rage M4 */
#define PCI_DEVICE_ID_ATI_RADEON_LE	0x4d45
#define PCI_DEVICE_ID_ATI_RADEON_LF	0x4d46
/* Radeon R100 */
#define PCI_DEVICE_ID_ATI_RADEON_QD	0x5144
#define PCI_DEVICE_ID_ATI_RADEON_QE	0x5145
#define PCI_DEVICE_ID_ATI_RADEON_QF	0x5146
#define PCI_DEVICE_ID_ATI_RADEON_QG	0x5147
/* Radeon RV100 (VE) */
#define PCI_DEVICE_ID_ATI_RADEON_QY	0x5159
#define PCI_DEVICE_ID_ATI_RADEON_QZ	0x515a
/* Radeon R200 (8500) */
#define PCI_DEVICE_ID_ATI_RADEON_QL	0x514c
#define PCI_DEVICE_ID_ATI_RADEON_QN	0x514e
#define PCI_DEVICE_ID_ATI_RADEON_QO	0x514f
#define PCI_DEVICE_ID_ATI_RADEON_Ql	0x516c
#define PCI_DEVICE_ID_ATI_RADEON_BB	0x4242
/* Radeon R200 (9100) */
#define PCI_DEVICE_ID_ATI_RADEON_QM	0x514d
/* Radeon RV200 (7500) */
#define PCI_DEVICE_ID_ATI_RADEON_QW	0x5157
#define PCI_DEVICE_ID_ATI_RADEON_QX	0x5158
/* Radeon RV250 (9000) */
#define PCI_DEVICE_ID_ATI_RADEON_Id	0x4964
#define PCI_DEVICE_ID_ATI_RADEON_Ie	0x4965
#define PCI_DEVICE_ID_ATI_RADEON_If	0x4966
#define PCI_DEVICE_ID_ATI_RADEON_Ig	0x4967
/* Radeon RV280 (9200) */
#define PCI_DEVICE_ID_ATI_RADEON_Y_	0x5960
#define PCI_DEVICE_ID_ATI_RADEON_Ya	0x5961
#define PCI_DEVICE_ID_ATI_RADEON_Yd	0x5964
/* Radeon R300 (9700) */
#define PCI_DEVICE_ID_ATI_RADEON_ND	0x4e44
#define PCI_DEVICE_ID_ATI_RADEON_NE	0x4e45
#define PCI_DEVICE_ID_ATI_RADEON_NF	0x4e46
#define PCI_DEVICE_ID_ATI_RADEON_NG	0x4e47
#define PCI_DEVICE_ID_ATI_RADEON_AE	0x4145
#define PCI_DEVICE_ID_ATI_RADEON_AF	0x4146
/* Radeon R300 (9500) */
#define PCI_DEVICE_ID_ATI_RADEON_AD	0x4144
/* Radeon R350 (9800) */
#define PCI_DEVICE_ID_ATI_RADEON_NH	0x4e48
#define PCI_DEVICE_ID_ATI_RADEON_NI	0x4e49
/* Radeon RV350 (9600) */
#define PCI_DEVICE_ID_ATI_RADEON_AP	0x4150
#define PCI_DEVICE_ID_ATI_RADEON_AR	0x4152
/* Radeon M6 */
#define PCI_DEVICE_ID_ATI_RADEON_LY	0x4c59
#define PCI_DEVICE_ID_ATI_RADEON_LZ	0x4c5a
/* Radeon M7 */
#define PCI_DEVICE_ID_ATI_RADEON_LW	0x4c57
#define PCI_DEVICE_ID_ATI_RADEON_LX	0x4c58
/* Radeon M9 */
#define PCI_DEVICE_ID_ATI_RADEON_Ld	0x4c64
#define PCI_DEVICE_ID_ATI_RADEON_Le	0x4c65
#define PCI_DEVICE_ID_ATI_RADEON_Lf	0x4c66
#define PCI_DEVICE_ID_ATI_RADEON_Lg	0x4c67
/* RadeonIGP */
#define PCI_DEVICE_ID_ATI_RADEON_IGP	0xCAB0
/* ATI IXP Chipset */
#define PCI_DEVICE_ID_ATI_IXP_IDE	0x4349

#define PCI_VENDOR_ID_VLSI		0x1004
#define PCI_DEVICE_ID_VLSI_82C592	0x0005
#define PCI_DEVICE_ID_VLSI_82C593	0x0006
#define PCI_DEVICE_ID_VLSI_82C594	0x0007
#define PCI_DEVICE_ID_VLSI_82C597	0x0009
#define PCI_DEVICE_ID_VLSI_82C541	0x000c
#define PCI_DEVICE_ID_VLSI_82C543	0x000d
#define PCI_DEVICE_ID_VLSI_82C532	0x0101
#define PCI_DEVICE_ID_VLSI_82C534	0x0102
#define PCI_DEVICE_ID_VLSI_82C535	0x0104
#define PCI_DEVICE_ID_VLSI_82C147	0x0105
#define PCI_DEVICE_ID_VLSI_VAS96011	0x0702

#define PCI_VENDOR_ID_ADL		0x1005
#define PCI_DEVICE_ID_ADL_2301		0x2301

#define PCI_VENDOR_ID_NS		0x100b
#define PCI_DEVICE_ID_NS_87415		0x0002
#define PCI_DEVICE_ID_NS_87560_LIO	0x000e
#define PCI_DEVICE_ID_NS_87560_USB	0x0012
#define PCI_DEVICE_ID_NS_83815		0x0020
#define PCI_DEVICE_ID_NS_83820		0x0022
#define PCI_DEVICE_ID_NS_SCx200_BRIDGE	0x0500
#define PCI_DEVICE_ID_NS_SCx200_SMI	0x0501
#define PCI_DEVICE_ID_NS_SCx200_IDE	0x0502
#define PCI_DEVICE_ID_NS_SCx200_AUDIO	0x0503
#define PCI_DEVICE_ID_NS_SCx200_VIDEO	0x0504
#define PCI_DEVICE_ID_NS_SCx200_XBUS	0x0505
#define PCI_DEVICE_ID_NS_87410		0xd001

#define PCI_VENDOR_ID_TSENG		0x100c
#define PCI_DEVICE_ID_TSENG_W32P_2	0x3202
#define PCI_DEVICE_ID_TSENG_W32P_b	0x3205
#define PCI_DEVICE_ID_TSENG_W32P_c	0x3206
#define PCI_DEVICE_ID_TSENG_W32P_d	0x3207
#define PCI_DEVICE_ID_TSENG_ET6000	0x3208

#define PCI_VENDOR_ID_WEITEK		0x100e
#define PCI_DEVICE_ID_WEITEK_P9000	0x9001
#define PCI_DEVICE_ID_WEITEK_P9100	0x9100

#define PCI_VENDOR_ID_DEC		0x1011
#define PCI_DEVICE_ID_DEC_BRD		0x0001
#define PCI_DEVICE_ID_DEC_TULIP		0x0002
#define PCI_DEVICE_ID_DEC_TGA		0x0004
#define PCI_DEVICE_ID_DEC_TULIP_FAST	0x0009
#define PCI_DEVICE_ID_DEC_TGA2		0x000D
#define PCI_DEVICE_ID_DEC_FDDI		0x000F
#define PCI_DEVICE_ID_DEC_TULIP_PLUS	0x0014
#define PCI_DEVICE_ID_DEC_21142		0x0019
#define PCI_DEVICE_ID_DEC_21052		0x0021
#define PCI_DEVICE_ID_DEC_21150		0x0022
#define PCI_DEVICE_ID_DEC_21152		0x0024
#define PCI_DEVICE_ID_DEC_21153		0x0025
#define PCI_DEVICE_ID_DEC_21154		0x0026
#define PCI_DEVICE_ID_DEC_21285		0x1065
#define PCI_DEVICE_ID_COMPAQ_42XX	0x0046

#define PCI_VENDOR_ID_CIRRUS		0x1013
#define PCI_DEVICE_ID_CIRRUS_7548	0x0038
#define PCI_DEVICE_ID_CIRRUS_5430	0x00a0
#define PCI_DEVICE_ID_CIRRUS_5434_4	0x00a4
#define PCI_DEVICE_ID_CIRRUS_5434_8	0x00a8
#define PCI_DEVICE_ID_CIRRUS_5436	0x00ac
#define PCI_DEVICE_ID_CIRRUS_5446	0x00b8
#define PCI_DEVICE_ID_CIRRUS_5480	0x00bc
#define PCI_DEVICE_ID_CIRRUS_5462	0x00d0
#define PCI_DEVICE_ID_CIRRUS_5464	0x00d4
#define PCI_DEVICE_ID_CIRRUS_5465	0x00d6
#define PCI_DEVICE_ID_CIRRUS_6729	0x1100
#define PCI_DEVICE_ID_CIRRUS_6832	0x1110
#define PCI_DEVICE_ID_CIRRUS_7542	0x1200
#define PCI_DEVICE_ID_CIRRUS_7543	0x1202
#define PCI_DEVICE_ID_CIRRUS_7541	0x1204

#define PCI_VENDOR_ID_IBM		0x1014
#define PCI_DEVICE_ID_IBM_FIRE_CORAL	0x000a
#define PCI_DEVICE_ID_IBM_TR		0x0018
#define PCI_DEVICE_ID_IBM_82G2675	0x001d
#define PCI_DEVICE_ID_IBM_MCA		0x0020
#define PCI_DEVICE_ID_IBM_82351		0x0022
#define PCI_DEVICE_ID_IBM_PYTHON	0x002d
#define PCI_DEVICE_ID_IBM_SERVERAID	0x002e
#define PCI_DEVICE_ID_IBM_TR_WAKE	0x003e
#define PCI_DEVICE_ID_IBM_MPIC		0x0046
#define PCI_DEVICE_ID_IBM_3780IDSP	0x007d
#define PCI_DEVICE_ID_IBM_CHUKAR	0x0096
#define PCI_DEVICE_ID_IBM_CPC700	0x00f9
#define PCI_DEVICE_ID_IBM_CPC710_PCI64	0x00fc
#define PCI_DEVICE_ID_IBM_CPC710_PCI32	0x0105
#define PCI_DEVICE_ID_IBM_405GP		0x0156
#define PCI_DEVICE_ID_IBM_SERVERAIDI960 0x01bd
#define PCI_DEVICE_ID_IBM_MPIC_2	0xffff

#define PCI_VENDOR_ID_COMPEX2		0x101a /* pci.ids says "AT&T GIS (NCR)" */
#define PCI_DEVICE_ID_COMPEX2_100VG	0x0005

#define PCI_VENDOR_ID_WD		0x101c
#define PCI_DEVICE_ID_WD_7197		0x3296
#define PCI_DEVICE_ID_WD_90C		0xc24a

#define PCI_VENDOR_ID_AMI		0x101e
#define PCI_DEVICE_ID_AMI_MEGARAID3	0x1960
#define PCI_DEVICE_ID_AMI_MEGARAID	0x9010
#define PCI_DEVICE_ID_AMI_MEGARAID2	0x9060

#define PCI_VENDOR_ID_AMD		0x1022
#define PCI_DEVICE_ID_AMD_LANCE		0x2000
#define PCI_DEVICE_ID_AMD_LANCE_HOME	0x2001
#define PCI_DEVICE_ID_AMD_SCSI		0x2020
#define PCI_DEVICE_ID_AMD_SERENADE	0x36c0
#define PCI_DEVICE_ID_AMD_FE_GATE_7006	0x7006
#define PCI_DEVICE_ID_AMD_FE_GATE_7007	0x7007
#define PCI_DEVICE_ID_AMD_FE_GATE_700C	0x700C
#define PCI_DEVICE_ID_AMD_FE_GATE_700D	0x700D
#define PCI_DEVICE_ID_AMD_FE_GATE_700E	0x700E
#define PCI_DEVICE_ID_AMD_FE_GATE_700F	0x700F
#define PCI_DEVICE_ID_AMD_COBRA_7400	0x7400
#define PCI_DEVICE_ID_AMD_COBRA_7401	0x7401
#define PCI_DEVICE_ID_AMD_COBRA_7403	0x7403
#define PCI_DEVICE_ID_AMD_COBRA_7404	0x7404
#define PCI_DEVICE_ID_AMD_VIPER_7408	0x7408
#define PCI_DEVICE_ID_AMD_VIPER_7409	0x7409
#define PCI_DEVICE_ID_AMD_VIPER_740B	0x740B
#define PCI_DEVICE_ID_AMD_VIPER_740C	0x740C
#define PCI_DEVICE_ID_AMD_VIPER_7410	0x7410
#define PCI_DEVICE_ID_AMD_VIPER_7411	0x7411
#define PCI_DEVICE_ID_AMD_VIPER_7413	0x7413
#define PCI_DEVICE_ID_AMD_VIPER_7414	0x7414
#define PCI_DEVICE_ID_AMD_OPUS_7440	0x7440
#	define PCI_DEVICE_ID_AMD_VIPER_7440	PCI_DEVICE_ID_AMD_OPUS_7440
#define PCI_DEVICE_ID_AMD_OPUS_7441	0x7441
#	define PCI_DEVICE_ID_AMD_VIPER_7441	PCI_DEVICE_ID_AMD_OPUS_7441
#define PCI_DEVICE_ID_AMD_OPUS_7443	0x7443
#	define PCI_DEVICE_ID_AMD_VIPER_7443	PCI_DEVICE_ID_AMD_OPUS_7443
#define PCI_DEVICE_ID_AMD_OPUS_7448	0x7448
# define	PCI_DEVICE_ID_AMD_VIPER_7448	PCI_DEVICE_ID_AMD_OPUS_7448
#define PCI_DEVICE_ID_AMD_OPUS_7449	0x7449
#	define PCI_DEVICE_ID_AMD_VIPER_7449	PCI_DEVICE_ID_AMD_OPUS_7449
#define PCI_DEVICE_ID_AMD_8111_LAN	0x7462
#define PCI_DEVICE_ID_AMD_8111_IDE	0x7469
#define PCI_DEVICE_ID_AMD_8111_AC97	0x746d
#define PCI_DEVICE_ID_AMD_8131_APIC	0x7450

#define PCI_VENDOR_ID_TRIDENT		0x1023
#define PCI_DEVICE_ID_TRIDENT_4DWAVE_DX 0x2000
#define PCI_DEVICE_ID_TRIDENT_4DWAVE_NX 0x2001
#define PCI_DEVICE_ID_TRIDENT_9320	0x9320
#define PCI_DEVICE_ID_TRIDENT_9388	0x9388
#define PCI_DEVICE_ID_TRIDENT_9397	0x9397
#define PCI_DEVICE_ID_TRIDENT_939A	0x939A
#define PCI_DEVICE_ID_TRIDENT_9520	0x9520
#define PCI_DEVICE_ID_TRIDENT_9525	0x9525
#define PCI_DEVICE_ID_TRIDENT_9420	0x9420
#define PCI_DEVICE_ID_TRIDENT_9440	0x9440
#define PCI_DEVICE_ID_TRIDENT_9660	0x9660
#define PCI_DEVICE_ID_TRIDENT_9750	0x9750
#define PCI_DEVICE_ID_TRIDENT_9850	0x9850
#define PCI_DEVICE_ID_TRIDENT_9880	0x9880
#define PCI_DEVICE_ID_TRIDENT_8400	0x8400
#define PCI_DEVICE_ID_TRIDENT_8420	0x8420
#define PCI_DEVICE_ID_TRIDENT_8500	0x8500

#define PCI_VENDOR_ID_AI		0x1025
#define PCI_DEVICE_ID_AI_M1435		0x1435

#define PCI_VENDOR_ID_DELL		0x1028

#define PCI_VENDOR_ID_MATROX		0x102B
#define PCI_DEVICE_ID_MATROX_MGA_2	0x0518
#define PCI_DEVICE_ID_MATROX_MIL	0x0519
#define PCI_DEVICE_ID_MATROX_MYS	0x051A
#define PCI_DEVICE_ID_MATROX_MIL_2	0x051b
#define PCI_DEVICE_ID_MATROX_MIL_2_AGP	0x051f
#define PCI_DEVICE_ID_MATROX_MGA_IMP	0x0d10
#define PCI_DEVICE_ID_MATROX_G100_MM	0x1000
#define PCI_DEVICE_ID_MATROX_G100_AGP	0x1001
#define PCI_DEVICE_ID_MATROX_G200_PCI	0x0520
#define PCI_DEVICE_ID_MATROX_G200_AGP	0x0521
#define PCI_DEVICE_ID_MATROX_G400	0x0525
#define PCI_DEVICE_ID_MATROX_G550	0x2527
#define PCI_DEVICE_ID_MATROX_VIA	0x4536

#define PCI_VENDOR_ID_CT		0x102c
#define PCI_DEVICE_ID_CT_65545		0x00d8
#define PCI_DEVICE_ID_CT_65548		0x00dc
#define PCI_DEVICE_ID_CT_65550		0x00e0
#define PCI_DEVICE_ID_CT_65554		0x00e4
#define PCI_DEVICE_ID_CT_65555		0x00e5
#define PCI_DEVICE_ID_CT_69000		0x00c0
#define PCI_DEVICE_ID_CT_69030		0x0c30

#define PCI_VENDOR_ID_MIRO		0x1031
#define PCI_DEVICE_ID_MIRO_36050	0x5601

#define PCI_VENDOR_ID_NEC		0x1033
#define PCI_DEVICE_ID_NEC_NAPCCARD	0x003e
#define PCI_DEVICE_ID_NEC_PCX2		0x0046
#define PCI_DEVICE_ID_NEC_NILE4		0x005a
#define PCI_DEVICE_ID_NEC_VRC5476	0x009b
#define PCI_DEVICE_ID_NEC_VRC4173	0x00a5
#define PCI_DEVICE_ID_NEC_VRC5477_AC97	0x00a6

#define PCI_VENDOR_ID_FD		0x1036
#define PCI_DEVICE_ID_FD_36C70		0x0000

#define PCI_VENDOR_ID_SI		0x1039
#define PCI_DEVICE_ID_SI_5591_AGP	0x0001
#define PCI_DEVICE_ID_SI_6202		0x0002
#define PCI_DEVICE_ID_SI_503		0x0008
#define PCI_DEVICE_ID_SI_ACPI		0x0009
#define PCI_DEVICE_ID_SI_180		0x0180
#define PCI_DEVICE_ID_SI_5597_VGA	0x0200
#define PCI_DEVICE_ID_SI_6205		0x0205
#define PCI_DEVICE_ID_SI_501		0x0406
#define PCI_DEVICE_ID_SI_496		0x0496
#define PCI_DEVICE_ID_SI_300		0x0300
#define PCI_DEVICE_ID_SI_315H		0x0310
#define PCI_DEVICE_ID_SI_315		0x0315
#define PCI_DEVICE_ID_SI_315PRO		0x0325
#define PCI_DEVICE_ID_SI_530		0x0530
#define PCI_DEVICE_ID_SI_540		0x0540
#define PCI_DEVICE_ID_SI_550		0x0550
#define PCI_DEVICE_ID_SI_540_VGA	0x5300
#define PCI_DEVICE_ID_SI_550_VGA	0x5315
#define PCI_DEVICE_ID_SI_601		0x0601
#define PCI_DEVICE_ID_SI_620		0x0620
#define PCI_DEVICE_ID_SI_630		0x0630
#define PCI_DEVICE_ID_SI_633		0x0633
#define PCI_DEVICE_ID_SI_635		0x0635
#define PCI_DEVICE_ID_SI_640		0x0640
#define PCI_DEVICE_ID_SI_645		0x0645
#define PCI_DEVICE_ID_SI_646		0x0646
#define PCI_DEVICE_ID_SI_648		0x0648
#define PCI_DEVICE_ID_SI_650		0x0650
#define PCI_DEVICE_ID_SI_651		0x0651
#define PCI_DEVICE_ID_SI_652		0x0652
#define PCI_DEVICE_ID_SI_655		0x0655
#define PCI_DEVICE_ID_SI_730		0x0730
#define PCI_DEVICE_ID_SI_733		0x0733
#define PCI_DEVICE_ID_SI_630_VGA	0x6300
#define PCI_DEVICE_ID_SI_730_VGA	0x7300
#define PCI_DEVICE_ID_SI_735		0x0735
#define PCI_DEVICE_ID_SI_740		0x0740
#define PCI_DEVICE_ID_SI_745		0x0745
#define PCI_DEVICE_ID_SI_746		0x0746
#define PCI_DEVICE_ID_SI_748		0x0748
#define PCI_DEVICE_ID_SI_750		0x0750
#define PCI_DEVICE_ID_SI_751		0x0751
#define PCI_DEVICE_ID_SI_752		0x0752
#define PCI_DEVICE_ID_SI_755		0x0755
#define PCI_DEVICE_ID_SI_900		0x0900
#define PCI_DEVICE_ID_SI_5107		0x5107
#define PCI_DEVICE_ID_SI_5300		0x5300
#define PCI_DEVICE_ID_SI_5511		0x5511
#define PCI_DEVICE_ID_SI_5513		0x5513
#define PCI_DEVICE_ID_SI_5518		0x5518
#define PCI_DEVICE_ID_SI_5571		0x5571
#define PCI_DEVICE_ID_SI_5581		0x5581
#define PCI_DEVICE_ID_SI_5582		0x5582
#define PCI_DEVICE_ID_SI_5591		0x5591
#define PCI_DEVICE_ID_SI_5596		0x5596
#define PCI_DEVICE_ID_SI_5597		0x5597
#define PCI_DEVICE_ID_SI_5598		0x5598
#define PCI_DEVICE_ID_SI_5600		0x5600
#define PCI_DEVICE_ID_SI_6300		0x6300
#define PCI_DEVICE_ID_SI_6306		0x6306
#define PCI_DEVICE_ID_SI_6326		0x6326
#define PCI_DEVICE_ID_SI_7001		0x7001
#define PCI_DEVICE_ID_SI_7016		0x7016

#define PCI_VENDOR_ID_HP		0x103c
#define PCI_DEVICE_ID_HP_DONNER_GFX	0x1008
#define PCI_DEVICE_ID_HP_TACHYON	0x1028
#define PCI_DEVICE_ID_HP_TACHLITE	0x1029
#define PCI_DEVICE_ID_HP_J2585A		0x1030
#define PCI_DEVICE_ID_HP_J2585B		0x1031
#define PCI_DEVICE_ID_HP_SAS		0x1048
#define PCI_DEVICE_ID_HP_DIVA1		0x1049
#define PCI_DEVICE_ID_HP_DIVA2		0x104A
#define PCI_DEVICE_ID_HP_SP2_0		0x104B
#define PCI_DEVICE_ID_HP_PCI_LBA	0x1054
#define PCI_DEVICE_ID_HP_REO_SBA	0x10f0
#define PCI_DEVICE_ID_HP_REO_IOC	0x10f1
#define PCI_DEVICE_ID_HP_ZX1_SBA	0x1229
#define PCI_DEVICE_ID_HP_ZX1_IOC	0x122a
#define PCI_DEVICE_ID_HP_PCIX_LBA	0x122e
#define PCI_DEVICE_ID_HP_SX1000_IOC	0x127c

#define PCI_VENDOR_ID_PCTECH		0x1042
#define PCI_DEVICE_ID_PCTECH_RZ1000	0x1000
#define PCI_DEVICE_ID_PCTECH_RZ1001	0x1001
#define PCI_DEVICE_ID_PCTECH_SAMURAI_0	0x3000
#define PCI_DEVICE_ID_PCTECH_SAMURAI_1	0x3010
#define PCI_DEVICE_ID_PCTECH_SAMURAI_IDE 0x3020

#define PCI_VENDOR_ID_ASUSTEK		0x1043
#define PCI_DEVICE_ID_ASUSTEK_0675	0x0675

#define PCI_VENDOR_ID_DPT		0x1044
#define PCI_DEVICE_ID_DPT		0xa400

#define PCI_VENDOR_ID_OPTI		0x1045
#define PCI_DEVICE_ID_OPTI_92C178	0xc178
#define PCI_DEVICE_ID_OPTI_82C557	0xc557
#define PCI_DEVICE_ID_OPTI_82C558	0xc558
#define PCI_DEVICE_ID_OPTI_82C621	0xc621
#define PCI_DEVICE_ID_OPTI_82C700	0xc700
#define PCI_DEVICE_ID_OPTI_82C701	0xc701
#define PCI_DEVICE_ID_OPTI_82C814	0xc814
#define PCI_DEVICE_ID_OPTI_82C822	0xc822
#define PCI_DEVICE_ID_OPTI_82C861	0xc861
#define PCI_DEVICE_ID_OPTI_82C825	0xd568

#define PCI_VENDOR_ID_ELSA		0x1048
#define PCI_DEVICE_ID_ELSA_MICROLINK	0x1000
#define PCI_DEVICE_ID_ELSA_QS3000	0x3000

#define PCI_VENDOR_ID_SGS		0x104a
#define PCI_DEVICE_ID_SGS_2000		0x0008
#define PCI_DEVICE_ID_SGS_1764		0x0009

#define PCI_VENDOR_ID_BUSLOGIC			0x104B
#define PCI_DEVICE_ID_BUSLOGIC_MULTIMASTER_NC	0x0140
#define PCI_DEVICE_ID_BUSLOGIC_MULTIMASTER	0x1040
#define PCI_DEVICE_ID_BUSLOGIC_FLASHPOINT	0x8130

#define PCI_VENDOR_ID_TI		0x104c
#define PCI_DEVICE_ID_TI_TVP4010	0x3d04
#define PCI_DEVICE_ID_TI_TVP4020	0x3d07
#define PCI_DEVICE_ID_TI_1130		0xac12
#define PCI_DEVICE_ID_TI_1031		0xac13
#define PCI_DEVICE_ID_TI_1131		0xac15
#define PCI_DEVICE_ID_TI_1250		0xac16
#define PCI_DEVICE_ID_TI_1220		0xac17
#define PCI_DEVICE_ID_TI_1221		0xac19
#define PCI_DEVICE_ID_TI_1210		0xac1a
#define PCI_DEVICE_ID_TI_1410		0xac50
#define PCI_DEVICE_ID_TI_1450		0xac1b
#define PCI_DEVICE_ID_TI_1225		0xac1c
#define PCI_DEVICE_ID_TI_1251A		0xac1d
#define PCI_DEVICE_ID_TI_1211		0xac1e
#define PCI_DEVICE_ID_TI_1251B		0xac1f
#define PCI_DEVICE_ID_TI_4410		0xac41
#define PCI_DEVICE_ID_TI_4451		0xac42
#define PCI_DEVICE_ID_TI_1420		0xac51
#define PCI_DEVICE_ID_TI_1520		0xac55
#define PCI_DEVICE_ID_TI_1510		0xac56

#define PCI_VENDOR_ID_SONY		0x104d
#define PCI_DEVICE_ID_SONY_CXD3222	0x8039

#define PCI_VENDOR_ID_OAK		0x104e
#define PCI_DEVICE_ID_OAK_OTI107	0x0107

/* Winbond have two vendor IDs! See 0x10ad as well */
#define PCI_VENDOR_ID_WINBOND2		0x1050
#define PCI_DEVICE_ID_WINBOND2_89C940	0x0940
#define PCI_DEVICE_ID_WINBOND2_89C940F	0x5a5a
#define PCI_DEVICE_ID_WINBOND2_6692	0x6692

#define PCI_VENDOR_ID_ANIGMA		0x1051
#define PCI_DEVICE_ID_ANIGMA_MC145575	0x0100

#define PCI_VENDOR_ID_EFAR		0x1055
#define PCI_DEVICE_ID_EFAR_SLC90E66_1	0x9130
#define PCI_DEVICE_ID_EFAR_SLC90E66_0	0x9460
#define PCI_DEVICE_ID_EFAR_SLC90E66_2	0x9462
#define PCI_DEVICE_ID_EFAR_SLC90E66_3	0x9463

#define PCI_VENDOR_ID_MOTOROLA		0x1057
#define PCI_VENDOR_ID_MOTOROLA_OOPS	0x1507
#define PCI_DEVICE_ID_MOTOROLA_MPC105	0x0001
#define PCI_DEVICE_ID_MOTOROLA_MPC106	0x0002
#define PCI_DEVICE_ID_MOTOROLA_MPC107	0x0004
#define PCI_DEVICE_ID_MOTOROLA_MPC8540	0x0008
#define PCI_DEVICE_ID_MOTOROLA_MPC8560	0x0009
#define PCI_DEVICE_ID_MOTOROLA_MPC8265A 0x18c0
#define PCI_DEVICE_ID_MOTOROLA_RAVEN	0x4801
#define PCI_DEVICE_ID_MOTOROLA_FALCON	0x4802
#define PCI_DEVICE_ID_MOTOROLA_HAWK	0x4803
#define PCI_DEVICE_ID_MOTOROLA_CPX8216	0x4806
#define PCI_DEVICE_ID_MOTOROLA_MPC190	0x6400

#define PCI_VENDOR_ID_PROMISE		0x105a
#define PCI_DEVICE_ID_PROMISE_20265	0x0d30
#define PCI_DEVICE_ID_PROMISE_20267	0x4d30
#define PCI_DEVICE_ID_PROMISE_20246	0x4d33
#define PCI_DEVICE_ID_PROMISE_20262	0x4d38
#define PCI_DEVICE_ID_PROMISE_20263	0x0D38
#define PCI_DEVICE_ID_PROMISE_20268	0x4d68
#define PCI_DEVICE_ID_PROMISE_20270	0x6268
#define PCI_DEVICE_ID_PROMISE_20269	0x4d69
#define PCI_DEVICE_ID_PROMISE_20271	0x6269
#define PCI_DEVICE_ID_PROMISE_20275	0x1275
#define PCI_DEVICE_ID_PROMISE_20276	0x5275
#define PCI_DEVICE_ID_PROMISE_20277	0x7275
#define PCI_DEVICE_ID_PROMISE_5300	0x5300

#define PCI_VENDOR_ID_N9		0x105d
#define PCI_DEVICE_ID_N9_I128		0x2309
#define PCI_DEVICE_ID_N9_I128_2		0x2339
#define PCI_DEVICE_ID_N9_I128_T2R	0x493d

#define PCI_VENDOR_ID_UMC		0x1060
#define PCI_DEVICE_ID_UMC_UM8673F	0x0101
#define PCI_DEVICE_ID_UMC_UM8891A	0x0891
#define PCI_DEVICE_ID_UMC_UM8886BF	0x673a
#define PCI_DEVICE_ID_UMC_UM8886A	0x886a
#define PCI_DEVICE_ID_UMC_UM8881F	0x8881
#define PCI_DEVICE_ID_UMC_UM8886F	0x8886
#define PCI_DEVICE_ID_UMC_UM9017F	0x9017
#define PCI_DEVICE_ID_UMC_UM8886N	0xe886
#define PCI_DEVICE_ID_UMC_UM8891N	0xe891

#define PCI_VENDOR_ID_X			0x1061
#define PCI_DEVICE_ID_X_AGX016		0x0001

#define PCI_VENDOR_ID_MYLEX		0x1069
#define PCI_DEVICE_ID_MYLEX_DAC960_P	0x0001
#define PCI_DEVICE_ID_MYLEX_DAC960_PD	0x0002
#define PCI_DEVICE_ID_MYLEX_DAC960_PG	0x0010
#define PCI_DEVICE_ID_MYLEX_DAC960_LA	0x0020
#define PCI_DEVICE_ID_MYLEX_DAC960_LP	0x0050
#define PCI_DEVICE_ID_MYLEX_DAC960_BA	0xBA56

#define PCI_VENDOR_ID_PICOP		0x1066
#define PCI_DEVICE_ID_PICOP_PT86C52X	0x0001
#define PCI_DEVICE_ID_PICOP_PT80C524	0x8002

#define PCI_VENDOR_ID_APPLE		0x106b
#define PCI_DEVICE_ID_APPLE_BANDIT	0x0001
#define PCI_DEVICE_ID_APPLE_GC		0x0002
#define PCI_DEVICE_ID_APPLE_HYDRA	0x000e
#define PCI_DEVICE_ID_APPLE_UNI_N_FW	0x0018
#define PCI_DEVICE_ID_APPLE_KL_USB	0x0019
#define PCI_DEVICE_ID_APPLE_UNI_N_AGP	0x0020
#define PCI_DEVICE_ID_APPLE_UNI_N_GMAC	0x0021
#define PCI_DEVICE_ID_APPLE_KEYLARGO	0x0022
#define PCI_DEVICE_ID_APPLE_UNI_N_GMACP 0x0024
#define PCI_DEVICE_ID_APPLE_KEYLARGO_P	0x0025
#define PCI_DEVICE_ID_APPLE_KL_USB_P	0x0026
#define PCI_DEVICE_ID_APPLE_UNI_N_AGP_P 0x0027
#define PCI_DEVICE_ID_APPLE_UNI_N_AGP15 0x002d
#define PCI_DEVICE_ID_APPLE_UNI_N_FW2	0x0030
#define PCI_DEVICE_ID_APPLE_TIGON3	0x1645

#define PCI_VENDOR_ID_YAMAHA		0x1073
#define PCI_DEVICE_ID_YAMAHA_724	0x0004
#define PCI_DEVICE_ID_YAMAHA_724F	0x000d
#define PCI_DEVICE_ID_YAMAHA_740	0x000a
#define PCI_DEVICE_ID_YAMAHA_740C	0x000c
#define PCI_DEVICE_ID_YAMAHA_744	0x0010
#define PCI_DEVICE_ID_YAMAHA_754	0x0012

#define PCI_VENDOR_ID_NEXGEN		0x1074
#define PCI_DEVICE_ID_NEXGEN_82C501	0x4e78

#define PCI_VENDOR_ID_QLOGIC		0x1077
#define PCI_DEVICE_ID_QLOGIC_ISP1020	0x1020
#define PCI_DEVICE_ID_QLOGIC_ISP1022	0x1022
#define PCI_DEVICE_ID_QLOGIC_ISP2100	0x2100
#define PCI_DEVICE_ID_QLOGIC_ISP2200	0x2200

#define PCI_VENDOR_ID_CYRIX		0x1078
#define PCI_DEVICE_ID_CYRIX_5510	0x0000
#define PCI_DEVICE_ID_CYRIX_PCI_MASTER	0x0001
#define PCI_DEVICE_ID_CYRIX_5520	0x0002
#define PCI_DEVICE_ID_CYRIX_5530_LEGACY 0x0100
#define PCI_DEVICE_ID_CYRIX_5530_SMI	0x0101
#define PCI_DEVICE_ID_CYRIX_5530_IDE	0x0102
#define PCI_DEVICE_ID_CYRIX_5530_AUDIO	0x0103
#define PCI_DEVICE_ID_CYRIX_5530_VIDEO	0x0104

#define PCI_VENDOR_ID_LEADTEK		0x107d
#define PCI_DEVICE_ID_LEADTEK_805	0x0000

#define PCI_VENDOR_ID_INTERPHASE	0x107e
#define PCI_DEVICE_ID_INTERPHASE_5526	0x0004
#define PCI_DEVICE_ID_INTERPHASE_55x6	0x0005
#define PCI_DEVICE_ID_INTERPHASE_5575	0x0008

#define PCI_VENDOR_ID_CONTAQ		0x1080
#define PCI_DEVICE_ID_CONTAQ_82C599	0x0600
#define PCI_DEVICE_ID_CONTAQ_82C693	0xc693

#define PCI_VENDOR_ID_FOREX		0x1083

#define PCI_VENDOR_ID_OLICOM		0x108d
#define PCI_DEVICE_ID_OLICOM_OC3136	0x0001
#define PCI_DEVICE_ID_OLICOM_OC2315	0x0011
#define PCI_DEVICE_ID_OLICOM_OC2325	0x0012
#define PCI_DEVICE_ID_OLICOM_OC2183	0x0013
#define PCI_DEVICE_ID_OLICOM_OC2326	0x0014
#define PCI_DEVICE_ID_OLICOM_OC6151	0x0021

#define PCI_VENDOR_ID_SUN		0x108e
#define PCI_DEVICE_ID_SUN_EBUS		0x1000
#define PCI_DEVICE_ID_SUN_HAPPYMEAL	0x1001
#define PCI_DEVICE_ID_SUN_RIO_EBUS	0x1100
#define PCI_DEVICE_ID_SUN_RIO_GEM	0x1101
#define PCI_DEVICE_ID_SUN_RIO_1394	0x1102
#define PCI_DEVICE_ID_SUN_RIO_USB	0x1103
#define PCI_DEVICE_ID_SUN_GEM		0x2bad
#define PCI_DEVICE_ID_SUN_SIMBA		0x5000
#define PCI_DEVICE_ID_SUN_PBM		0x8000
#define PCI_DEVICE_ID_SUN_SCHIZO	0x8001
#define PCI_DEVICE_ID_SUN_SABRE		0xa000
#define PCI_DEVICE_ID_SUN_HUMMINGBIRD	0xa001
#define PCI_DEVICE_ID_SUN_TOMATILLO	0xa801

#define PCI_VENDOR_ID_CMD		0x1095
#define PCI_DEVICE_ID_SII_1210SA	0x0240

#define PCI_DEVICE_ID_CMD_640		0x0640
#define PCI_DEVICE_ID_CMD_643		0x0643
#define PCI_DEVICE_ID_CMD_646		0x0646
#define PCI_DEVICE_ID_CMD_647		0x0647
#define PCI_DEVICE_ID_CMD_648		0x0648
#define PCI_DEVICE_ID_CMD_649		0x0649
#define PCI_DEVICE_ID_CMD_670		0x0670

#define PCI_DEVICE_ID_SII_680		0x0680
#define PCI_DEVICE_ID_SII_3112		0x3112

#define PCI_VENDOR_ID_VISION		0x1098
#define PCI_DEVICE_ID_VISION_QD8500	0x0001
#define PCI_DEVICE_ID_VISION_QD8580	0x0002

#define PCI_VENDOR_ID_BROOKTREE		0x109e
#define PCI_DEVICE_ID_BROOKTREE_848	0x0350
#define PCI_DEVICE_ID_BROOKTREE_849A	0x0351
#define PCI_DEVICE_ID_BROOKTREE_878_1	0x036e
#define PCI_DEVICE_ID_BROOKTREE_878	0x0878
#define PCI_DEVICE_ID_BROOKTREE_8474	0x8474

#define PCI_VENDOR_ID_SIERRA		0x10a8
#define PCI_DEVICE_ID_SIERRA_STB	0x0000

#define PCI_VENDOR_ID_SGI		0x10a9
#define PCI_DEVICE_ID_SGI_IOC3		0x0003
#define PCI_DEVICE_ID_SGI_IOC4		0x100a

#define PCI_VENDOR_ID_ACC		0x10aa
#define PCI_DEVICE_ID_ACC_2056		0x0000

#define PCI_VENDOR_ID_WINBOND		0x10ad
#define PCI_DEVICE_ID_WINBOND_83769	0x0001
#define PCI_DEVICE_ID_WINBOND_82C105	0x0105
#define PCI_DEVICE_ID_WINBOND_83C553	0x0565

#define PCI_VENDOR_ID_DATABOOK		0x10b3
#define PCI_DEVICE_ID_DATABOOK_87144	0xb106

#define PCI_VENDOR_ID_PLX		0x10b5
#define PCI_DEVICE_ID_PLX_R685		0x1030
#define PCI_DEVICE_ID_PLX_ROMULUS	0x106a
#define PCI_DEVICE_ID_PLX_SPCOM800	0x1076
#define PCI_DEVICE_ID_PLX_1077		0x1077
#define PCI_DEVICE_ID_PLX_SPCOM200	0x1103
#define PCI_DEVICE_ID_PLX_DJINN_ITOO	0x1151
#define PCI_DEVICE_ID_PLX_R753		0x1152
#define PCI_DEVICE_ID_PLX_9030		0x9030
#define PCI_DEVICE_ID_PLX_9050		0x9050
#define PCI_DEVICE_ID_PLX_9060		0x9060
#define PCI_DEVICE_ID_PLX_9060ES	0x906E
#define PCI_DEVICE_ID_PLX_9060SD	0x906D
#define PCI_DEVICE_ID_PLX_9080		0x9080
#define PCI_DEVICE_ID_PLX_GTEK_SERIAL2	0xa001

#define PCI_VENDOR_ID_MADGE		0x10b6
#define PCI_DEVICE_ID_MADGE_MK2		0x0002
#define PCI_DEVICE_ID_MADGE_C155S	0x1001

#define PCI_VENDOR_ID_3COM		0x10b7
#define PCI_DEVICE_ID_3COM_3C985	0x0001
#define PCI_DEVICE_ID_3COM_3C339	0x3390
#define PCI_DEVICE_ID_3COM_3C590	0x5900
#define PCI_DEVICE_ID_3COM_3C595TX	0x5950
#define PCI_DEVICE_ID_3COM_3C595T4	0x5951
#define PCI_DEVICE_ID_3COM_3C595MII	0x5952
#define PCI_DEVICE_ID_3COM_3C900TPO	0x9000
#define PCI_DEVICE_ID_3COM_3C900COMBO	0x9001
#define PCI_DEVICE_ID_3COM_3C905TX	0x9050
#define PCI_DEVICE_ID_3COM_3C905T4	0x9051
#define PCI_DEVICE_ID_3COM_3C905B_TX	0x9055
#define PCI_DEVICE_ID_3COM_3CR990	0x9900
#define PCI_DEVICE_ID_3COM_3CR990_TX_95 0x9902
#define PCI_DEVICE_ID_3COM_3CR990_TX_97 0x9903
#define PCI_DEVICE_ID_3COM_3CR990B	0x9904
#define PCI_DEVICE_ID_3COM_3CR990_FX	0x9905
#define PCI_DEVICE_ID_3COM_3CR990SVR95	0x9908
#define PCI_DEVICE_ID_3COM_3CR990SVR97	0x9909
#define PCI_DEVICE_ID_3COM_3CR990SVR	0x990a

#define PCI_VENDOR_ID_SMC		0x10b8
#define PCI_DEVICE_ID_SMC_EPIC100	0x0005

#define PCI_VENDOR_ID_AL		0x10b9
#define PCI_DEVICE_ID_AL_M1445		0x1445
#define PCI_DEVICE_ID_AL_M1449		0x1449
#define PCI_DEVICE_ID_AL_M1451		0x1451
#define PCI_DEVICE_ID_AL_M1461		0x1461
#define PCI_DEVICE_ID_AL_M1489		0x1489
#define PCI_DEVICE_ID_AL_M1511		0x1511
#define PCI_DEVICE_ID_AL_M1513		0x1513
#define PCI_DEVICE_ID_AL_M1521		0x1521
#define PCI_DEVICE_ID_AL_M1523		0x1523
#define PCI_DEVICE_ID_AL_M1531		0x1531
#define PCI_DEVICE_ID_AL_M1533		0x1533
#define PCI_DEVICE_ID_AL_M1535		0x1535
#define PCI_DEVICE_ID_AL_M1541		0x1541
#define PCI_DEVICE_ID_AL_M1621		0x1621
#define PCI_DEVICE_ID_AL_M1631		0x1631
#define PCI_DEVICE_ID_AL_M1641		0x1641
#define PCI_DEVICE_ID_AL_M1644		0x1644
#define PCI_DEVICE_ID_AL_M1647		0x1647
#define PCI_DEVICE_ID_AL_M1651		0x1651
#define PCI_DEVICE_ID_AL_M1543		0x1543
#define PCI_DEVICE_ID_AL_M3307		0x3307
#define PCI_DEVICE_ID_AL_M4803		0x5215
#define PCI_DEVICE_ID_AL_M5219		0x5219
#define PCI_DEVICE_ID_AL_M5229		0x5229
#define PCI_DEVICE_ID_AL_M5237		0x5237
#define PCI_DEVICE_ID_AL_M5243		0x5243
#define PCI_DEVICE_ID_AL_M5451		0x5451
#define PCI_DEVICE_ID_AL_M7101		0x7101

#define PCI_VENDOR_ID_MITSUBISHI	0x10ba

#define PCI_VENDOR_ID_SURECOM		0x10bd
#define PCI_DEVICE_ID_SURECOM_NE34	0x0e34

#define PCI_VENDOR_ID_NEOMAGIC		0x10c8
#define PCI_DEVICE_ID_NEOMAGIC_MAGICGRAPH_NM2070	0x0001
#define PCI_DEVICE_ID_NEOMAGIC_MAGICGRAPH_128V		0x0002
#define PCI_DEVICE_ID_NEOMAGIC_MAGICGRAPH_128ZV		0x0003
#define PCI_DEVICE_ID_NEOMAGIC_MAGICGRAPH_NM2160	0x0004
#define PCI_DEVICE_ID_NEOMAGIC_MAGICMEDIA_256AV		0x0005
#define PCI_DEVICE_ID_NEOMAGIC_MAGICGRAPH_128ZVPLUS	0x0083

#define PCI_VENDOR_ID_ASP		0x10cd
#define PCI_DEVICE_ID_ASP_ABP940	0x1200
#define PCI_DEVICE_ID_ASP_ABP940U	0x1300
#define PCI_DEVICE_ID_ASP_ABP940UW	0x2300

#define PCI_VENDOR_ID_MACRONIX		0x10d9
#define PCI_DEVICE_ID_MACRONIX_MX98713	0x0512
#define PCI_DEVICE_ID_MACRONIX_MX987x5	0x0531

#define PCI_VENDOR_ID_TCONRAD		0x10da
#define PCI_DEVICE_ID_TCONRAD_TOKENRING 0x0508

#define PCI_VENDOR_ID_CERN		0x10dc
#define PCI_DEVICE_ID_CERN_SPSB_PMC	0x0001
#define PCI_DEVICE_ID_CERN_SPSB_PCI	0x0002
#define PCI_DEVICE_ID_CERN_HIPPI_DST	0x0021
#define PCI_DEVICE_ID_CERN_HIPPI_SRC	0x0022

#define PCI_VENDOR_ID_NVIDIA			0x10de
#define PCI_DEVICE_ID_NVIDIA_TNT		0x0020
#define PCI_DEVICE_ID_NVIDIA_TNT2		0x0028
#define PCI_DEVICE_ID_NVIDIA_UTNT2		0x0029
#define PCI_DEVICE_ID_NVIDIA_VTNT2		0x002C
#define PCI_DEVICE_ID_NVIDIA_UVTNT2		0x002D
#define PCI_DEVICE_ID_NVIDIA_NFORCE2_IDE	0x0065
#define PCI_DEVICE_ID_NVIDIA_NFORCE2S_IDE	0x0085
#define PCI_DEVICE_ID_NVIDIA_NFORCE2S_SATA	0x008e
#define PCI_DEVICE_ID_NVIDIA_ITNT2		0x00A0
#define PCI_DEVICE_ID_NVIDIA_NFORCE3		0x00d1
#define PCI_DEVICE_ID_NVIDIA_NFORCE3_IDE	0x00d5
#define PCI_DEVICE_ID_NVIDIA_NFORCE3S		0x00e1
#define PCI_DEVICE_ID_NVIDIA_NFORCE3S_SATA	0x00e3
#define PCI_DEVICE_ID_NVIDIA_NFORCE3S_IDE	0x00e5
#define PCI_DEVICE_ID_NVIDIA_NFORCE3S_SATA2	0x00ee
#define PCI_DEVICE_ID_NVIDIA_GEFORCE_SDR	0x0100
#define PCI_DEVICE_ID_NVIDIA_GEFORCE_DDR	0x0101
#define PCI_DEVICE_ID_NVIDIA_QUADRO		0x0103
#define PCI_DEVICE_ID_NVIDIA_GEFORCE2_MX	0x0110
#define PCI_DEVICE_ID_NVIDIA_GEFORCE2_MX2	0x0111
#define PCI_DEVICE_ID_NVIDIA_GEFORCE2_GO	0x0112
#define PCI_DEVICE_ID_NVIDIA_QUADRO2_MXR	0x0113
#define PCI_DEVICE_ID_NVIDIA_GEFORCE2_GTS	0x0150
#define PCI_DEVICE_ID_NVIDIA_GEFORCE2_GTS2	0x0151
#define PCI_DEVICE_ID_NVIDIA_GEFORCE2_ULTRA	0x0152
#define PCI_DEVICE_ID_NVIDIA_QUADRO2_PRO	0x0153
#define PCI_DEVICE_ID_NVIDIA_IGEFORCE2		0x01a0
#define PCI_DEVICE_ID_NVIDIA_NFORCE		0x01a4
#define PCI_DEVICE_ID_NVIDIA_NFORCE_IDE		0x01bc
#define PCI_DEVICE_ID_NVIDIA_NFORCE2		0x01e0
#define PCI_DEVICE_ID_NVIDIA_GEFORCE3		0x0200
#define PCI_DEVICE_ID_NVIDIA_GEFORCE3_1		0x0201
#define PCI_DEVICE_ID_NVIDIA_GEFORCE3_2		0x0202
#define PCI_DEVICE_ID_NVIDIA_QUADRO_DDC		0x0203

#define PCI_VENDOR_ID_IMS		0x10e0
#define PCI_DEVICE_ID_IMS_8849		0x8849
#define PCI_DEVICE_ID_IMS_TT128		0x9128
#define PCI_DEVICE_ID_IMS_TT3D		0x9135

#define PCI_VENDOR_ID_TEKRAM2		0x10e1
#define PCI_DEVICE_ID_TEKRAM2_690c	0x690c

#define PCI_VENDOR_ID_TUNDRA		0x10e3
#define PCI_DEVICE_ID_TUNDRA_CA91C042	0x0000

#define PCI_VENDOR_ID_AMCC		0x10e8
#define PCI_DEVICE_ID_AMCC_MYRINET	0x8043
#define PCI_DEVICE_ID_AMCC_PARASTATION	0x8062
#define PCI_DEVICE_ID_AMCC_S5933	0x807d
#define PCI_DEVICE_ID_AMCC_S5933_HEPC3	0x809c

#define PCI_VENDOR_ID_INTERG		0x10ea
#define PCI_DEVICE_ID_INTERG_1680	0x1680
#define PCI_DEVICE_ID_INTERG_1682	0x1682
#define PCI_DEVICE_ID_INTERG_2000	0x2000
#define PCI_DEVICE_ID_INTERG_2010	0x2010
#define PCI_DEVICE_ID_INTERG_5000	0x5000
#define PCI_DEVICE_ID_INTERG_5050	0x5050

#define PCI_VENDOR_ID_REALTEK		0x10ec
#define PCI_DEVICE_ID_REALTEK_8029	0x8029
#define PCI_DEVICE_ID_REALTEK_8129	0x8129
#define PCI_DEVICE_ID_REALTEK_8139	0x8139
#define PCI_DEVICE_ID_REALTEK_8169	0x8169

#define PCI_VENDOR_ID_DLINK		0x1186
#define PCI_DEVICE_ID_DLINK_8139	0x1300

#define PCI_VENDOR_ID_XILINX		0x10ee
#define PCI_DEVICE_ID_TURBOPAM		0x4020

#define PCI_VENDOR_ID_TRUEVISION	0x10fa
#define PCI_DEVICE_ID_TRUEVISION_T1000	0x000c

#define PCI_VENDOR_ID_INIT		0x1101
#define PCI_DEVICE_ID_INIT_320P		0x9100
#define PCI_DEVICE_ID_INIT_360P		0x9500

#define PCI_VENDOR_ID_CREATIVE		0x1102 /* duplicate: ECTIVA */
#define PCI_DEVICE_ID_CREATIVE_EMU10K1	0x0002

#define PCI_VENDOR_ID_ECTIVA		0x1102 /* duplicate: CREATIVE */
#define PCI_DEVICE_ID_ECTIVA_EV1938	0x8938

#define PCI_VENDOR_ID_TTI		0x1103
#define PCI_DEVICE_ID_TTI_HPT343	0x0003
#define PCI_DEVICE_ID_TTI_HPT366	0x0004
#define PCI_DEVICE_ID_TTI_HPT372	0x0005
#define PCI_DEVICE_ID_TTI_HPT302	0x0006
#define PCI_DEVICE_ID_TTI_HPT371	0x0007
#define PCI_DEVICE_ID_TTI_HPT374	0x0008
#define PCI_DEVICE_ID_TTI_HPT372N	0x0009	/* appoarently a 372N variant? */

#define PCI_VENDOR_ID_VIA		0x1106
#define PCI_DEVICE_ID_VIA_8363_0	0x0305
#define PCI_DEVICE_ID_VIA_8371_0	0x0391
#define PCI_DEVICE_ID_VIA_8501_0	0x0501
#define PCI_DEVICE_ID_VIA_82C505	0x0505
#define PCI_DEVICE_ID_VIA_82C561	0x0561
#define PCI_DEVICE_ID_VIA_82C586_1	0x0571
#define PCI_DEVICE_ID_VIA_82C576	0x0576
#define PCI_DEVICE_ID_VIA_82C585	0x0585
#define PCI_DEVICE_ID_VIA_82C586_0	0x0586
#define PCI_DEVICE_ID_VIA_82C595	0x0595
#define PCI_DEVICE_ID_VIA_82C596	0x0596
#define PCI_DEVICE_ID_VIA_82C597_0	0x0597
#define PCI_DEVICE_ID_VIA_82C598_0	0x0598
#define PCI_DEVICE_ID_VIA_8601_0	0x0601
#define PCI_DEVICE_ID_VIA_8605_0	0x0605
#define PCI_DEVICE_ID_VIA_82C680	0x0680
#define PCI_DEVICE_ID_VIA_82C686	0x0686
#define PCI_DEVICE_ID_VIA_82C691	0x0691
#define PCI_DEVICE_ID_VIA_82C693	0x0693
#define PCI_DEVICE_ID_VIA_82C693_1	0x0698
#define PCI_DEVICE_ID_VIA_82C926	0x0926
#define PCI_DEVICE_ID_VIA_82C576_1	0x1571
#define PCI_DEVICE_ID_VIA_82C595_97	0x1595
#define PCI_DEVICE_ID_VIA_82C586_2	0x3038
#define PCI_DEVICE_ID_VIA_82C586_3	0x3040
#define PCI_DEVICE_ID_VIA_6305		0x3044
#define PCI_DEVICE_ID_VIA_82C596_3	0x3050
#define PCI_DEVICE_ID_VIA_82C596B_3	0x3051
#define PCI_DEVICE_ID_VIA_82C686_4	0x3057
#define PCI_DEVICE_ID_VIA_82C686_5	0x3058
#define PCI_DEVICE_ID_VIA_8233_5	0x3059
#define PCI_DEVICE_ID_VIA_8233_7	0x3065
#define PCI_DEVICE_ID_VIA_82C686_6	0x3068
#define PCI_DEVICE_ID_VIA_8233_0	0x3074
#define PCI_DEVICE_ID_VIA_8633_0	0x3091
#define PCI_DEVICE_ID_VIA_8367_0	0x3099
#define PCI_DEVICE_ID_VIA_8622		0x3102
#define PCI_DEVICE_ID_VIA_8233C_0	0x3109
#define PCI_DEVICE_ID_VIA_8361		0x3112
#define PCI_DEVICE_ID_VIA_8375		0x3116
#define PCI_DEVICE_ID_VIA_CLE266	0x3123
#define PCI_DEVICE_ID_VIA_8233A		0x3147
#define PCI_DEVICE_ID_VIA_P4M266	0x3148
#define PCI_DEVICE_ID_VIA_8237_SATA	0x3149
#define PCI_DEVICE_ID_VIA_P4X333	0x3168
#define PCI_DEVICE_ID_VIA_8235		0x3177
#define PCI_DEVICE_ID_VIA_8377_0	0x3189
#define PCI_DEVICE_ID_VIA_K8T400M_0	0x3188
#define PCI_DEVICE_ID_VIA_8237		0x3227
#define PCI_DEVICE_ID_VIA_86C100A	0x6100
#define PCI_DEVICE_ID_VIA_8231		0x8231
#define PCI_DEVICE_ID_VIA_8231_4	0x8235
#define PCI_DEVICE_ID_VIA_8365_1	0x8305
#define PCI_DEVICE_ID_VIA_8371_1	0x8391
#define PCI_DEVICE_ID_VIA_8501_1	0x8501
#define PCI_DEVICE_ID_VIA_82C597_1	0x8597
#define PCI_DEVICE_ID_VIA_82C598_1	0x8598
#define PCI_DEVICE_ID_VIA_8601_1	0x8601
#define PCI_DEVICE_ID_VIA_8505_1	0x8605
#define PCI_DEVICE_ID_VIA_8633_1	0xB091
#define PCI_DEVICE_ID_VIA_8367_1	0xB099

#define PCI_VENDOR_ID_SIEMENS		0x110A
#define PCI_DEVICE_ID_SIEMENS_DSCC4	0x2102

#define PCI_VENDOR_ID_SMC2		0x1113
#define PCI_DEVICE_ID_SMC2_1211TX	0x1211

#define PCI_VENDOR_ID_VORTEX		0x1119
#define PCI_DEVICE_ID_VORTEX_GDT60x0	0x0000
#define PCI_DEVICE_ID_VORTEX_GDT6000B	0x0001
#define PCI_DEVICE_ID_VORTEX_GDT6x10	0x0002
#define PCI_DEVICE_ID_VORTEX_GDT6x20	0x0003
#define PCI_DEVICE_ID_VORTEX_GDT6530	0x0004
#define PCI_DEVICE_ID_VORTEX_GDT6550	0x0005
#define PCI_DEVICE_ID_VORTEX_GDT6x17	0x0006
#define PCI_DEVICE_ID_VORTEX_GDT6x27	0x0007
#define PCI_DEVICE_ID_VORTEX_GDT6537	0x0008
#define PCI_DEVICE_ID_VORTEX_GDT6557	0x0009
#define PCI_DEVICE_ID_VORTEX_GDT6x15	0x000a
#define PCI_DEVICE_ID_VORTEX_GDT6x25	0x000b
#define PCI_DEVICE_ID_VORTEX_GDT6535	0x000c
#define PCI_DEVICE_ID_VORTEX_GDT6555	0x000d
#define PCI_DEVICE_ID_VORTEX_GDT6x17RP	0x0100
#define PCI_DEVICE_ID_VORTEX_GDT6x27RP	0x0101
#define PCI_DEVICE_ID_VORTEX_GDT6537RP	0x0102
#define PCI_DEVICE_ID_VORTEX_GDT6557RP	0x0103
#define PCI_DEVICE_ID_VORTEX_GDT6x11RP	0x0104
#define PCI_DEVICE_ID_VORTEX_GDT6x21RP	0x0105
#define PCI_DEVICE_ID_VORTEX_GDT6x17RP1 0x0110
#define PCI_DEVICE_ID_VORTEX_GDT6x27RP1 0x0111
#define PCI_DEVICE_ID_VORTEX_GDT6537RP1 0x0112
#define PCI_DEVICE_ID_VORTEX_GDT6557RP1 0x0113
#define PCI_DEVICE_ID_VORTEX_GDT6x11RP1 0x0114
#define PCI_DEVICE_ID_VORTEX_GDT6x21RP1 0x0115
#define PCI_DEVICE_ID_VORTEX_GDT6x17RP2 0x0120
#define PCI_DEVICE_ID_VORTEX_GDT6x27RP2 0x0121
#define PCI_DEVICE_ID_VORTEX_GDT6537RP2 0x0122
#define PCI_DEVICE_ID_VORTEX_GDT6557RP2 0x0123
#define PCI_DEVICE_ID_VORTEX_GDT6x11RP2 0x0124
#define PCI_DEVICE_ID_VORTEX_GDT6x21RP2 0x0125

#define PCI_VENDOR_ID_EF		0x111a
#define PCI_DEVICE_ID_EF_ATM_FPGA	0x0000
#define PCI_DEVICE_ID_EF_ATM_ASIC	0x0002

#define PCI_VENDOR_ID_IDT		0x111d
#define PCI_DEVICE_ID_IDT_IDT77201	0x0001

#define PCI_VENDOR_ID_FORE		0x1127
#define PCI_DEVICE_ID_FORE_PCA200PC	0x0210
#define PCI_DEVICE_ID_FORE_PCA200E	0x0300

#define PCI_VENDOR_ID_IMAGINGTECH	0x112f
#define PCI_DEVICE_ID_IMAGINGTECH_ICPCI 0x0000

#define PCI_VENDOR_ID_PHILIPS		0x1131
#define PCI_DEVICE_ID_PHILIPS_SAA7145	0x7145
#define PCI_DEVICE_ID_PHILIPS_SAA7146	0x7146
#define PCI_DEVICE_ID_PHILIPS_SAA9730	0x9730

#define PCI_VENDOR_ID_EICON		0x1133
#define PCI_DEVICE_ID_EICON_DIVA20PRO	0xe001
#define PCI_DEVICE_ID_EICON_DIVA20	0xe002
#define PCI_DEVICE_ID_EICON_DIVA20PRO_U 0xe003
#define PCI_DEVICE_ID_EICON_DIVA20_U	0xe004
#define PCI_DEVICE_ID_EICON_DIVA201	0xe005
#define PCI_DEVICE_ID_EICON_DIVA202	0xe00b
#define PCI_DEVICE_ID_EICON_MAESTRA	0xe010
#define PCI_DEVICE_ID_EICON_MAESTRAQ	0xe012
#define PCI_DEVICE_ID_EICON_MAESTRAQ_U	0xe013
#define PCI_DEVICE_ID_EICON_MAESTRAP	0xe014

#define PCI_VENDOR_ID_CYCLONE		0x113c
#define PCI_DEVICE_ID_CYCLONE_SDK	0x0001

#define PCI_VENDOR_ID_ALLIANCE		0x1142
#define PCI_DEVICE_ID_ALLIANCE_PROMOTIO 0x3210
#define PCI_DEVICE_ID_ALLIANCE_PROVIDEO 0x6422
#define PCI_DEVICE_ID_ALLIANCE_AT24	0x6424
#define PCI_DEVICE_ID_ALLIANCE_AT3D	0x643d

#define PCI_VENDOR_ID_SYSKONNECT	0x1148
#define PCI_DEVICE_ID_SYSKONNECT_FP	0x4000
#define PCI_DEVICE_ID_SYSKONNECT_TR	0x4200
#define PCI_DEVICE_ID_SYSKONNECT_GE	0x4300
#define PCI_DEVICE_ID_SYSKONNECT_YU	0x4320
#define PCI_DEVICE_ID_SYSKONNECT_9DXX	0x4400
#define PCI_DEVICE_ID_SYSKONNECT_9MXX	0x4500

#define PCI_VENDOR_ID_VMIC		0x114a
#define PCI_DEVICE_ID_VMIC_VME		0x7587

#define PCI_VENDOR_ID_DIGI		0x114f
#define PCI_DEVICE_ID_DIGI_EPC		0x0002
#define PCI_DEVICE_ID_DIGI_RIGHTSWITCH	0x0003
#define PCI_DEVICE_ID_DIGI_XEM		0x0004
#define PCI_DEVICE_ID_DIGI_XR		0x0005
#define PCI_DEVICE_ID_DIGI_CX		0x0006
#define PCI_DEVICE_ID_DIGI_XRJ		0x0009
#define PCI_DEVICE_ID_DIGI_EPCJ		0x000a
#define PCI_DEVICE_ID_DIGI_XR_920	0x0027
#define PCI_DEVICE_ID_DIGI_DF_M_IOM2_E	0x0070
#define PCI_DEVICE_ID_DIGI_DF_M_E	0x0071
#define PCI_DEVICE_ID_DIGI_DF_M_IOM2_A	0x0072
#define PCI_DEVICE_ID_DIGI_DF_M_A	0x0073

#define PCI_VENDOR_ID_MUTECH		0x1159
#define PCI_DEVICE_ID_MUTECH_MV1000	0x0001

#define PCI_VENDOR_ID_XIRCOM		0x115d
#define PCI_DEVICE_ID_XIRCOM_X3201_ETH	0x0003
#define PCI_DEVICE_ID_XIRCOM_X3201_MDM	0x0103

#define PCI_VENDOR_ID_RENDITION		0x1163
#define PCI_DEVICE_ID_RENDITION_VERITE	0x0001
#define PCI_DEVICE_ID_RENDITION_VERITE2100 0x2000

#define PCI_VENDOR_ID_SERVERWORKS		0x1166
#define PCI_DEVICE_ID_SERVERWORKS_HE		0x0008
#define PCI_DEVICE_ID_SERVERWORKS_LE		0x0009
#define PCI_DEVICE_ID_SERVERWORKS_CIOB30	0x0010
#define PCI_DEVICE_ID_SERVERWORKS_CMIC_HE	0x0011
#define PCI_DEVICE_ID_SERVERWORKS_GCNB_LE	0x0017
#define PCI_DEVICE_ID_SERVERWORKS_OSB4		0x0200
#define PCI_DEVICE_ID_SERVERWORKS_CSB5		0x0201
#define PCI_DEVICE_ID_SERVERWORKS_CSB6		0x0203
#define PCI_DEVICE_ID_SERVERWORKS_OSB4IDE	0x0211
#define PCI_DEVICE_ID_SERVERWORKS_CSB5IDE	0x0212
#define PCI_DEVICE_ID_SERVERWORKS_CSB6IDE	0x0213
#define PCI_DEVICE_ID_SERVERWORKS_CSB6IDE2	0x0217
#define PCI_DEVICE_ID_SERVERWORKS_OSB4USB	0x0220
#define PCI_DEVICE_ID_SERVERWORKS_CSB5USB	PCI_DEVICE_ID_SERVERWORKS_OSB4USB
#define PCI_DEVICE_ID_SERVERWORKS_CSB6USB	0x0221
#define PCI_DEVICE_ID_SERVERWORKS_GCLE		0x0225
#define PCI_DEVICE_ID_SERVERWORKS_GCLE2		0x0227
#define PCI_DEVICE_ID_SERVERWORKS_CSB5ISA	0x0230

#define PCI_VENDOR_ID_SBE		0x1176
#define PCI_DEVICE_ID_SBE_WANXL100	0x0301
#define PCI_DEVICE_ID_SBE_WANXL200	0x0302
#define PCI_DEVICE_ID_SBE_WANXL400	0x0104

#define PCI_VENDOR_ID_TOSHIBA		0x1179
#define PCI_DEVICE_ID_TOSHIBA_PICCOLO	0x0102
#define PCI_DEVICE_ID_TOSHIBA_601	0x0601
#define PCI_DEVICE_ID_TOSHIBA_TOPIC95	0x060a
#define PCI_DEVICE_ID_TOSHIBA_TOPIC97	0x060f

#define PCI_VENDOR_ID_TOSHIBA_2		0x102f
#define PCI_DEVICE_ID_TOSHIBA_TX3927	0x000a
#define PCI_DEVICE_ID_TOSHIBA_TC35815CF 0x0030
#define PCI_DEVICE_ID_TOSHIBA_TX4927	0x0180

#define PCI_VENDOR_ID_RICOH		0x1180
#define PCI_DEVICE_ID_RICOH_RL5C465	0x0465
#define PCI_DEVICE_ID_RICOH_RL5C466	0x0466
#define PCI_DEVICE_ID_RICOH_RL5C475	0x0475
#define PCI_DEVICE_ID_RICOH_RL5C476	0x0476
#define PCI_DEVICE_ID_RICOH_RL5C478	0x0478

#define PCI_VENDOR_ID_ARTOP		0x1191
#define PCI_DEVICE_ID_ARTOP_ATP8400	0x0004
#define PCI_DEVICE_ID_ARTOP_ATP850UF	0x0005
#define PCI_DEVICE_ID_ARTOP_ATP860	0x0006
#define PCI_DEVICE_ID_ARTOP_ATP860R	0x0007
#define PCI_DEVICE_ID_ARTOP_ATP865	0x0008
#define PCI_DEVICE_ID_ARTOP_ATP865R	0x0009
#define PCI_DEVICE_ID_ARTOP_AEC7610	0x8002
#define PCI_DEVICE_ID_ARTOP_AEC7612UW	0x8010
#define PCI_DEVICE_ID_ARTOP_AEC7612U	0x8020
#define PCI_DEVICE_ID_ARTOP_AEC7612S	0x8030
#define PCI_DEVICE_ID_ARTOP_AEC7612D	0x8040
#define PCI_DEVICE_ID_ARTOP_AEC7612SUW	0x8050
#define PCI_DEVICE_ID_ARTOP_8060	0x8060

#define PCI_VENDOR_ID_ZEITNET		0x1193
#define PCI_DEVICE_ID_ZEITNET_1221	0x0001
#define PCI_DEVICE_ID_ZEITNET_1225	0x0002

#define PCI_VENDOR_ID_OMEGA		0x119b
#define PCI_DEVICE_ID_OMEGA_82C092G	0x1221

#define PCI_VENDOR_ID_FUJITSU_ME	0x119e
#define PCI_DEVICE_ID_FUJITSU_FS155	0x0001
#define PCI_DEVICE_ID_FUJITSU_FS50	0x0003

#define PCI_SUBVENDOR_ID_KEYSPAN	0x11a9
#define PCI_SUBDEVICE_ID_KEYSPAN_SX2	0x5334

#define PCI_VENDOR_ID_GALILEO		0x11ab
#define PCI_DEVICE_ID_GALILEO_GT64011	0x4146
#define PCI_DEVICE_ID_GALILEO_GT64111	0x4146
#define PCI_DEVICE_ID_GALILEO_GT96100	0x9652
#define PCI_DEVICE_ID_GALILEO_GT96100A	0x9653

#define PCI_VENDOR_ID_LITEON		0x11ad
#define PCI_DEVICE_ID_LITEON_LNE100TX	0x0002

#define PCI_VENDOR_ID_V3		0x11b0
#define PCI_DEVICE_ID_V3_V960		0x0001
#define PCI_DEVICE_ID_V3_V350		0x0001
#define PCI_DEVICE_ID_V3_V961		0x0002
#define PCI_DEVICE_ID_V3_V351		0x0002

#define PCI_VENDOR_ID_NP		0x11bc
#define PCI_DEVICE_ID_NP_PCI_FDDI	0x0001

#define PCI_VENDOR_ID_ATT		0x11c1
#define PCI_DEVICE_ID_ATT_L56XMF	0x0440
#define PCI_DEVICE_ID_ATT_VENUS_MODEM	0x480

#define PCI_VENDOR_ID_SPECIALIX		0x11cb
#define PCI_DEVICE_ID_SPECIALIX_IO8	0x2000
#define PCI_DEVICE_ID_SPECIALIX_XIO	0x4000
#define PCI_DEVICE_ID_SPECIALIX_RIO	0x8000
#define PCI_SUBDEVICE_ID_SPECIALIX_SPEED4 0xa004

#define PCI_VENDOR_ID_AURAVISION	0x11d1
#define PCI_DEVICE_ID_AURAVISION_VXP524 0x01f7

#define PCI_VENDOR_ID_ANALOG_DEVICES	0x11d4
#define PCI_DEVICE_ID_AD1889JS		0x1889

#define PCI_VENDOR_ID_IKON		0x11d5
#define PCI_DEVICE_ID_IKON_10115	0x0115
#define PCI_DEVICE_ID_IKON_10117	0x0117

#define PCI_VENDOR_ID_ZORAN		0x11de
#define PCI_DEVICE_ID_ZORAN_36057	0x6057
#define PCI_DEVICE_ID_ZORAN_36120	0x6120

#define PCI_VENDOR_ID_KINETIC		0x11f4
#define PCI_DEVICE_ID_KINETIC_2915	0x2915

#define PCI_VENDOR_ID_COMPEX		0x11f6
#define PCI_DEVICE_ID_COMPEX_ENET100VG4 0x0112
#define PCI_DEVICE_ID_COMPEX_RL2000	0x1401

#define PCI_VENDOR_ID_RP		0x11fe
#define PCI_DEVICE_ID_RP32INTF		0x0001
#define PCI_DEVICE_ID_RP8INTF		0x0002
#define PCI_DEVICE_ID_RP16INTF		0x0003
#define PCI_DEVICE_ID_RP4QUAD		0x0004
#define PCI_DEVICE_ID_RP8OCTA		0x0005
#define PCI_DEVICE_ID_RP8J		0x0006
#define PCI_DEVICE_ID_RPP4		0x000A
#define PCI_DEVICE_ID_RPP8		0x000B
#define PCI_DEVICE_ID_RP8M		0x000C

#define PCI_VENDOR_ID_CYCLADES		0x120e
#define PCI_DEVICE_ID_CYCLOM_Y_Lo	0x0100
#define PCI_DEVICE_ID_CYCLOM_Y_Hi	0x0101
#define PCI_DEVICE_ID_CYCLOM_4Y_Lo	0x0102
#define PCI_DEVICE_ID_CYCLOM_4Y_Hi	0x0103
#define PCI_DEVICE_ID_CYCLOM_8Y_Lo	0x0104
#define PCI_DEVICE_ID_CYCLOM_8Y_Hi	0x0105
#define PCI_DEVICE_ID_CYCLOM_Z_Lo	0x0200
#define PCI_DEVICE_ID_CYCLOM_Z_Hi	0x0201
#define PCI_DEVICE_ID_PC300_RX_2	0x0300
#define PCI_DEVICE_ID_PC300_RX_1	0x0301
#define PCI_DEVICE_ID_PC300_TE_2	0x0310
#define PCI_DEVICE_ID_PC300_TE_1	0x0311

#define PCI_VENDOR_ID_ESSENTIAL		0x120f
#define PCI_DEVICE_ID_ESSENTIAL_ROADRUNNER	0x0001

#define PCI_VENDOR_ID_O2		0x1217
#define PCI_DEVICE_ID_O2_6729		0x6729
#define PCI_DEVICE_ID_O2_6730		0x673a
#define PCI_DEVICE_ID_O2_6832		0x6832
#define PCI_DEVICE_ID_O2_6836		0x6836

#define PCI_VENDOR_ID_3DFX		0x121a
#define PCI_DEVICE_ID_3DFX_VOODOO	0x0001
#define PCI_DEVICE_ID_3DFX_VOODOO2	0x0002
#define PCI_DEVICE_ID_3DFX_BANSHEE	0x0003
#define PCI_DEVICE_ID_3DFX_VOODOO3	0x0005

#define PCI_VENDOR_ID_SIGMADES		0x1236
#define PCI_DEVICE_ID_SIGMADES_6425	0x6401

#define PCI_VENDOR_ID_CCUBE		0x123f

#define PCI_VENDOR_ID_AVM		0x1244
#define PCI_DEVICE_ID_AVM_B1		0x0700
#define PCI_DEVICE_ID_AVM_C4		0x0800
#define PCI_DEVICE_ID_AVM_A1		0x0a00
#define PCI_DEVICE_ID_AVM_A1_V2		0x0e00
#define PCI_DEVICE_ID_AVM_C2		0x1100
#define PCI_DEVICE_ID_AVM_T1		0x1200

#define PCI_VENDOR_ID_DIPIX		0x1246

#define PCI_VENDOR_ID_STALLION		0x124d
#define PCI_DEVICE_ID_STALLION_ECHPCI832 0x0000
#define PCI_DEVICE_ID_STALLION_ECHPCI864 0x0002
#define PCI_DEVICE_ID_STALLION_EIOPCI	0x0003

#define PCI_VENDOR_ID_OPTIBASE		0x1255
#define PCI_DEVICE_ID_OPTIBASE_FORGE	0x1110
#define PCI_DEVICE_ID_OPTIBASE_FUSION	0x1210
#define PCI_DEVICE_ID_OPTIBASE_VPLEX	0x2110
#define PCI_DEVICE_ID_OPTIBASE_VPLEXCC	0x2120
#define PCI_DEVICE_ID_OPTIBASE_VQUEST	0x2130

#define PCI_VENDOR_ID_ESS		0x125d
#define PCI_DEVICE_ID_ESS_ESS1968	0x1968
#define PCI_DEVICE_ID_ESS_AUDIOPCI	0x1969
#define PCI_DEVICE_ID_ESS_ESS1978	0x1978

#define PCI_VENDOR_ID_SATSAGEM		0x1267
#define PCI_DEVICE_ID_SATSAGEM_NICCY	0x1016
#define PCI_DEVICE_ID_SATSAGEM_PCR2101	0x5352
#define PCI_DEVICE_ID_SATSAGEM_TELSATTURBO 0x5a4b

#define PCI_VENDOR_ID_SMI		0x126f
#define PCI_DEVICE_ID_SMI_710		0x0710
#define PCI_DEVICE_ID_SMI_712		0x0712
#define PCI_DEVICE_ID_SMI_810		0x0810
#define PCI_DEVICE_ID_SMI_501		0x0501

#define PCI_VENDOR_ID_HUGHES		0x1273
#define PCI_DEVICE_ID_HUGHES_DIRECPC	0x0002

#define PCI_VENDOR_ID_ENSONIQ		0x1274
#define PCI_DEVICE_ID_ENSONIQ_CT5880	0x5880
#define PCI_DEVICE_ID_ENSONIQ_ES1370	0x5000
#define PCI_DEVICE_ID_ENSONIQ_ES1371	0x1371

#define PCI_VENDOR_ID_ROCKWELL		0x127A

#define PCI_VENDOR_ID_DAVICOM		0x1282
#define PCI_DEVICE_ID_DAVICOM_DM9102A	0x9102

#define PCI_VENDOR_ID_ITE		0x1283
#define PCI_DEVICE_ID_ITE_IT8172G	0x8172
#define PCI_DEVICE_ID_ITE_IT8172G_AUDIO 0x0801
#define PCI_DEVICE_ID_ITE_IT8181	0x8181
#define PCI_DEVICE_ID_ITE_8211		0x8211
#define PCI_DEVICE_ID_ITE_8212		0x8212
#define PCI_DEVICE_ID_ITE_8872		0x8872

#define PCI_DEVICE_ID_ITE_IT8330G_0	0xe886

/* formerly Platform Tech */
#define PCI_VENDOR_ID_ESS_OLD		0x1285
#define PCI_DEVICE_ID_ESS_ESS0100	0x0100

#define PCI_VENDOR_ID_ALTEON		0x12ae
#define PCI_DEVICE_ID_ALTEON_ACENIC	0x0001

#define PCI_VENDOR_ID_USR		0x12B9

#define PCI_SUBVENDOR_ID_CONNECT_TECH			0x12c4
#define PCI_SUBDEVICE_ID_CONNECT_TECH_BH8_232		0x0001
#define PCI_SUBDEVICE_ID_CONNECT_TECH_BH4_232		0x0002
#define PCI_SUBDEVICE_ID_CONNECT_TECH_BH2_232		0x0003
#define PCI_SUBDEVICE_ID_CONNECT_TECH_BH8_485		0x0004
#define PCI_SUBDEVICE_ID_CONNECT_TECH_BH8_485_4_4	0x0005
#define PCI_SUBDEVICE_ID_CONNECT_TECH_BH4_485		0x0006
#define PCI_SUBDEVICE_ID_CONNECT_TECH_BH4_485_2_2	0x0007
#define PCI_SUBDEVICE_ID_CONNECT_TECH_BH2_485		0x0008
#define PCI_SUBDEVICE_ID_CONNECT_TECH_BH8_485_2_6	0x0009
#define PCI_SUBDEVICE_ID_CONNECT_TECH_BH081101V1	0x000A
#define PCI_SUBDEVICE_ID_CONNECT_TECH_BH041101V1	0x000B

#define PCI_VENDOR_ID_PICTUREL		0x12c5
#define PCI_DEVICE_ID_PICTUREL_PCIVST	0x0081

#define PCI_VENDOR_ID_NVIDIA_SGS	0x12d2
#define PCI_DEVICE_ID_NVIDIA_SGS_RIVA128 0x0018

#define PCI_SUBVENDOR_ID_CHASE_PCIFAST		0x12E0
#define PCI_SUBDEVICE_ID_CHASE_PCIFAST4		0x0031
#define PCI_SUBDEVICE_ID_CHASE_PCIFAST8		0x0021
#define PCI_SUBDEVICE_ID_CHASE_PCIFAST16	0x0011
#define PCI_SUBDEVICE_ID_CHASE_PCIFAST16FMC	0x0041
#define PCI_SUBVENDOR_ID_CHASE_PCIRAS		0x124D
#define PCI_SUBDEVICE_ID_CHASE_PCIRAS4		0xF001
#define PCI_SUBDEVICE_ID_CHASE_PCIRAS8		0xF010

#define PCI_VENDOR_ID_AUREAL		0x12eb
#define PCI_DEVICE_ID_AUREAL_VORTEX_1	0x0001
#define PCI_DEVICE_ID_AUREAL_VORTEX_2	0x0002

#define PCI_VENDOR_ID_ESDGMBH		0x12fe

#define PCI_VENDOR_ID_CBOARDS		0x1307
#define PCI_DEVICE_ID_CBOARDS_DAS1602_16 0x0001

#define PCI_VENDOR_ID_SIIG		0x131f
#define PCI_DEVICE_ID_SIIG_1S_10x_550	0x1000
#define PCI_DEVICE_ID_SIIG_1S_10x_650	0x1001
#define PCI_DEVICE_ID_SIIG_1S_10x_850	0x1002
#define PCI_DEVICE_ID_SIIG_1S1P_10x_550 0x1010
#define PCI_DEVICE_ID_SIIG_1S1P_10x_650 0x1011
#define PCI_DEVICE_ID_SIIG_1S1P_10x_850 0x1012
#define PCI_DEVICE_ID_SIIG_1P_10x	0x1020
#define PCI_DEVICE_ID_SIIG_2P_10x	0x1021
#define PCI_DEVICE_ID_SIIG_2S_10x_550	0x1030
#define PCI_DEVICE_ID_SIIG_2S_10x_650	0x1031
#define PCI_DEVICE_ID_SIIG_2S_10x_850	0x1032
#define PCI_DEVICE_ID_SIIG_2S1P_10x_550 0x1034
#define PCI_DEVICE_ID_SIIG_2S1P_10x_650 0x1035
#define PCI_DEVICE_ID_SIIG_2S1P_10x_850 0x1036
#define PCI_DEVICE_ID_SIIG_4S_10x_550	0x1050
#define PCI_DEVICE_ID_SIIG_4S_10x_650	0x1051
#define PCI_DEVICE_ID_SIIG_4S_10x_850	0x1052
#define PCI_DEVICE_ID_SIIG_1S_20x_550	0x2000
#define PCI_DEVICE_ID_SIIG_1S_20x_650	0x2001
#define PCI_DEVICE_ID_SIIG_1S_20x_850	0x2002
#define PCI_DEVICE_ID_SIIG_1P_20x	0x2020
#define PCI_DEVICE_ID_SIIG_2P_20x	0x2021
#define PCI_DEVICE_ID_SIIG_2S_20x_550	0x2030
#define PCI_DEVICE_ID_SIIG_2S_20x_650	0x2031
#define PCI_DEVICE_ID_SIIG_2S_20x_850	0x2032
#define PCI_DEVICE_ID_SIIG_2P1S_20x_550 0x2040
#define PCI_DEVICE_ID_SIIG_2P1S_20x_650 0x2041
#define PCI_DEVICE_ID_SIIG_2P1S_20x_850 0x2042
#define PCI_DEVICE_ID_SIIG_1S1P_20x_550 0x2010
#define PCI_DEVICE_ID_SIIG_1S1P_20x_650 0x2011
#define PCI_DEVICE_ID_SIIG_1S1P_20x_850 0x2012
#define PCI_DEVICE_ID_SIIG_4S_20x_550	0x2050
#define PCI_DEVICE_ID_SIIG_4S_20x_650	0x2051
#define PCI_DEVICE_ID_SIIG_4S_20x_850	0x2052
#define PCI_DEVICE_ID_SIIG_2S1P_20x_550 0x2060
#define PCI_DEVICE_ID_SIIG_2S1P_20x_650 0x2061
#define PCI_DEVICE_ID_SIIG_2S1P_20x_850 0x2062

#define PCI_VENDOR_ID_DOMEX		0x134a
#define PCI_DEVICE_ID_DOMEX_DMX3191D	0x0001

#define PCI_VENDOR_ID_QUATECH		0x135C
#define PCI_DEVICE_ID_QUATECH_QSC100	0x0010
#define PCI_DEVICE_ID_QUATECH_DSC100	0x0020
#define PCI_DEVICE_ID_QUATECH_DSC200	0x0030
#define PCI_DEVICE_ID_QUATECH_QSC200	0x0040
#define PCI_DEVICE_ID_QUATECH_ESC100D	0x0050
#define PCI_DEVICE_ID_QUATECH_ESC100M	0x0060

#define PCI_VENDOR_ID_SEALEVEL		0x135e
#define PCI_DEVICE_ID_SEALEVEL_U530	0x7101
#define PCI_DEVICE_ID_SEALEVEL_UCOMM2	0x7201
#define PCI_DEVICE_ID_SEALEVEL_UCOMM422 0x7402
#define PCI_DEVICE_ID_SEALEVEL_UCOMM232 0x7202
#define PCI_DEVICE_ID_SEALEVEL_COMM4	0x7401
#define PCI_DEVICE_ID_SEALEVEL_COMM8	0x7801

#define PCI_VENDOR_ID_HYPERCOPE		0x1365
#define PCI_DEVICE_ID_HYPERCOPE_PLX	0x9050
#define PCI_SUBDEVICE_ID_HYPERCOPE_OLD_ERGO	0x0104
#define PCI_SUBDEVICE_ID_HYPERCOPE_ERGO		0x0106
#define PCI_SUBDEVICE_ID_HYPERCOPE_METRO	0x0107
#define PCI_SUBDEVICE_ID_HYPERCOPE_CHAMP2	0x0108
#define PCI_SUBDEVICE_ID_HYPERCOPE_PLEXUS	0x0109

#define PCI_VENDOR_ID_KAWASAKI		0x136b
#define PCI_DEVICE_ID_MCHIP_KL5A72002	0xff01

#define PCI_VENDOR_ID_LMC		0x1376
#define PCI_DEVICE_ID_LMC_HSSI		0x0003
#define PCI_DEVICE_ID_LMC_DS3		0x0004
#define PCI_DEVICE_ID_LMC_SSI		0x0005
#define PCI_DEVICE_ID_LMC_T1		0x0006

#define PCI_VENDOR_ID_NETGEAR		0x1385
#define PCI_DEVICE_ID_NETGEAR_GA620	0x620a
#define PCI_DEVICE_ID_NETGEAR_GA622	0x622a

#define PCI_VENDOR_ID_APPLICOM		0x1389
#define PCI_DEVICE_ID_APPLICOM_PCIGENERIC 0x0001
#define PCI_DEVICE_ID_APPLICOM_PCI2000IBS_CAN 0x0002
#define PCI_DEVICE_ID_APPLICOM_PCI2000PFB 0x0003

#define PCI_VENDOR_ID_MOXA		0x1393
#define PCI_DEVICE_ID_MOXA_C104		0x1040
#define PCI_DEVICE_ID_MOXA_C168		0x1680
#define PCI_DEVICE_ID_MOXA_CP204J	0x2040
#define PCI_DEVICE_ID_MOXA_C218		0x2180
#define PCI_DEVICE_ID_MOXA_C320		0x3200

#define PCI_VENDOR_ID_CCD		0x1397
#define PCI_DEVICE_ID_CCD_2BD0		0x2bd0
#define PCI_DEVICE_ID_CCD_B000		0xb000
#define PCI_DEVICE_ID_CCD_B006		0xb006
#define PCI_DEVICE_ID_CCD_B007		0xb007
#define PCI_DEVICE_ID_CCD_B008		0xb008
#define PCI_DEVICE_ID_CCD_B009		0xb009
#define PCI_DEVICE_ID_CCD_B00A		0xb00a
#define PCI_DEVICE_ID_CCD_B00B		0xb00b
#define PCI_DEVICE_ID_CCD_B00C		0xb00c
#define PCI_DEVICE_ID_CCD_B100		0xb100

#define PCI_VENDOR_ID_3WARE		0x13C1
#define PCI_DEVICE_ID_3WARE_1000	0x1000

#define PCI_VENDOR_ID_ABOCOM		0x13D1
#define PCI_DEVICE_ID_ABOCOM_2BD1	0x2BD1

#define PCI_VENDOR_ID_CMEDIA		0x13f6
#define PCI_DEVICE_ID_CMEDIA_CM8338A	0x0100
#define PCI_DEVICE_ID_CMEDIA_CM8338B	0x0101
#define PCI_DEVICE_ID_CMEDIA_CM8738	0x0111
#define PCI_DEVICE_ID_CMEDIA_CM8738B	0x0112

#define PCI_VENDOR_ID_LAVA		0x1407
#define PCI_DEVICE_ID_LAVA_DSERIAL	0x0100 /* 2x 16550 */
#define PCI_DEVICE_ID_LAVA_QUATRO_A	0x0101 /* 2x 16550, half of 4 port */
#define PCI_DEVICE_ID_LAVA_QUATRO_B	0x0102 /* 2x 16550, half of 4 port */
#define PCI_DEVICE_ID_LAVA_OCTO_A	0x0180 /* 4x 16550A, half of 8 port */
#define PCI_DEVICE_ID_LAVA_OCTO_B	0x0181 /* 4x 16550A, half of 8 port */
#define PCI_DEVICE_ID_LAVA_PORT_PLUS	0x0200 /* 2x 16650 */
#define PCI_DEVICE_ID_LAVA_QUAD_A	0x0201 /* 2x 16650, half of 4 port */
#define PCI_DEVICE_ID_LAVA_QUAD_B	0x0202 /* 2x 16650, half of 4 port */
#define PCI_DEVICE_ID_LAVA_SSERIAL	0x0500 /* 1x 16550 */
#define PCI_DEVICE_ID_LAVA_PORT_650	0x0600 /* 1x 16650 */
#define PCI_DEVICE_ID_LAVA_PARALLEL	0x8000
#define PCI_DEVICE_ID_LAVA_DUAL_PAR_A	0x8002 /* The Lava Dual Parallel is */
#define PCI_DEVICE_ID_LAVA_DUAL_PAR_B	0x8003 /* two PCI devices on a card */
#define PCI_DEVICE_ID_LAVA_BOCA_IOPPAR	0x8800

#define PCI_VENDOR_ID_TIMEDIA		0x1409
#define PCI_DEVICE_ID_TIMEDIA_1889	0x7168

#define PCI_VENDOR_ID_OXSEMI		0x1415
#define PCI_DEVICE_ID_OXSEMI_12PCI840	0x8403
#define PCI_DEVICE_ID_OXSEMI_16PCI954	0x9501
#define PCI_DEVICE_ID_OXSEMI_16PCI95N	0x9511
#define PCI_DEVICE_ID_OXSEMI_16PCI954PP 0x9513
#define PCI_DEVICE_ID_OXSEMI_16PCI952	0x9521

#define PCI_VENDOR_ID_AIRONET		0x14b9
#define PCI_DEVICE_ID_AIRONET_4800_1	0x0001
#define PCI_DEVICE_ID_AIRONET_4800	0x4500 /* values switched?  see */
#define PCI_DEVICE_ID_AIRONET_4500	0x4800 /* drivers/net/aironet4500_card.c */

#define PCI_VENDOR_ID_TITAN		0x14D2
#define PCI_DEVICE_ID_TITAN_010L	0x8001
#define PCI_DEVICE_ID_TITAN_100L	0x8010
#define PCI_DEVICE_ID_TITAN_110L	0x8011
#define PCI_DEVICE_ID_TITAN_200L	0x8020
#define PCI_DEVICE_ID_TITAN_210L	0x8021
#define PCI_DEVICE_ID_TITAN_400L	0x8040
#define PCI_DEVICE_ID_TITAN_800L	0x8080
#define PCI_DEVICE_ID_TITAN_100		0xA001
#define PCI_DEVICE_ID_TITAN_200		0xA005
#define PCI_DEVICE_ID_TITAN_400		0xA003
#define PCI_DEVICE_ID_TITAN_800B	0xA004

#define PCI_VENDOR_ID_PANACOM		0x14d4
#define PCI_DEVICE_ID_PANACOM_QUADMODEM 0x0400
#define PCI_DEVICE_ID_PANACOM_DUALMODEM 0x0402

#define PCI_VENDOR_ID_AFAVLAB		0x14db
#define PCI_DEVICE_ID_AFAVLAB_P028	0x2180

#define PCI_VENDOR_ID_BROADCOM		0x14e4
#define PCI_DEVICE_ID_TIGON3_5700	0x1644
#define PCI_DEVICE_ID_TIGON3_5701	0x1645
#define PCI_DEVICE_ID_TIGON3_5702	0x1646
#define PCI_DEVICE_ID_TIGON3_5703	0x1647
#define PCI_DEVICE_ID_TIGON3_5704	0x1648
#define PCI_DEVICE_ID_TIGON3_5704S_2	0x1649
#define PCI_DEVICE_ID_TIGON3_5702FE	0x164d
#define PCI_DEVICE_ID_TIGON3_5705	0x1653
#define PCI_DEVICE_ID_TIGON3_5705_2	0x1654
#define PCI_DEVICE_ID_TIGON3_5705M	0x165d
#define PCI_DEVICE_ID_TIGON3_5705M_2	0x165e
#define PCI_DEVICE_ID_TIGON3_5705F	0x166e
#define PCI_DEVICE_ID_TIGON3_5782	0x1696
#define PCI_DEVICE_ID_TIGON3_5788	0x169c
#define PCI_DEVICE_ID_TIGON3_5702X	0x16a6
#define PCI_DEVICE_ID_TIGON3_5703X	0x16a7
#define PCI_DEVICE_ID_TIGON3_5704S	0x16a8
#define PCI_DEVICE_ID_TIGON3_5702A3	0x16c6
#define PCI_DEVICE_ID_TIGON3_5703A3	0x16c7
#define PCI_DEVICE_ID_TIGON3_5901	0x170d
#define PCI_DEVICE_ID_TIGON3_5901_2	0x170e
#define PCI_DEVICE_ID_BCM4401		0x4401

#define PCI_VENDOR_ID_ENE		0x1524
#define PCI_DEVICE_ID_ENE_1211		0x1211
#define PCI_DEVICE_ID_ENE_1225		0x1225
#define PCI_DEVICE_ID_ENE_1410		0x1410
#define PCI_DEVICE_ID_ENE_1420		0x1420

#define PCI_VENDOR_ID_SYBA		0x1592
#define PCI_DEVICE_ID_SYBA_2P_EPP	0x0782
#define PCI_DEVICE_ID_SYBA_1P_ECP	0x0783

#define PCI_VENDOR_ID_MORETON		0x15aa
#define PCI_DEVICE_ID_RASTEL_2PORT	0x2000

#define PCI_VENDOR_ID_ZOLTRIX		0x15b0
#define PCI_DEVICE_ID_ZOLTRIX_2BD0	0x2bd0

#define PCI_VENDOR_ID_PDC		0x15e9
#define PCI_DEVICE_ID_PDC_ADMA100	0x1841

#define PCI_VENDOR_ID_ALTIMA		0x173b
#define PCI_DEVICE_ID_ALTIMA_AC1000	0x03e8
#define PCI_DEVICE_ID_ALTIMA_AC1001	0x03e9
#define PCI_DEVICE_ID_ALTIMA_AC9100	0x03ea
#define PCI_DEVICE_ID_ALTIMA_AC1003	0x03eb

#define PCI_VENDOR_ID_SYMPHONY		0x1c1c
#define PCI_DEVICE_ID_SYMPHONY_101	0x0001

#define PCI_VENDOR_ID_TEKRAM		0x1de1
#define PCI_DEVICE_ID_TEKRAM_DC290	0xdc29

#define PCI_VENDOR_ID_HINT		0x3388
#define PCI_DEVICE_ID_HINT_VXPROII_IDE	0x8013

#define PCI_VENDOR_ID_3DLABS		0x3d3d
#define PCI_DEVICE_ID_3DLABS_300SX	0x0001
#define PCI_DEVICE_ID_3DLABS_500TX	0x0002
#define PCI_DEVICE_ID_3DLABS_DELTA	0x0003
#define PCI_DEVICE_ID_3DLABS_PERMEDIA	0x0004
#define PCI_DEVICE_ID_3DLABS_MX		0x0006
#define PCI_DEVICE_ID_3DLABS_PERMEDIA2	0x0007
#define PCI_DEVICE_ID_3DLABS_GAMMA	0x0008
#define PCI_DEVICE_ID_3DLABS_PERMEDIA2V 0x0009

#define PCI_VENDOR_ID_AVANCE		0x4005
#define PCI_DEVICE_ID_AVANCE_ALG2064	0x2064
#define PCI_DEVICE_ID_AVANCE_2302	0x2302

#define PCI_VENDOR_ID_AKS		0x416c
#define PCI_DEVICE_ID_AKS_ALADDINCARD	0x0100
#define PCI_DEVICE_ID_AKS_CPC		0x0200

#define PCI_VENDOR_ID_REDCREEK		0x4916
#define PCI_DEVICE_ID_RC45		0x1960

#define PCI_VENDOR_ID_NETVIN		0x4a14
#define PCI_DEVICE_ID_NETVIN_NV5000SC	0x5000

#define PCI_VENDOR_ID_S3		0x5333
#define PCI_DEVICE_ID_S3_PLATO_PXS	0x0551
#define PCI_DEVICE_ID_S3_ViRGE		0x5631
#define PCI_DEVICE_ID_S3_TRIO		0x8811
#define PCI_DEVICE_ID_S3_AURORA64VP	0x8812
#define PCI_DEVICE_ID_S3_TRIO64UVP	0x8814
#define PCI_DEVICE_ID_S3_ViRGE_VX	0x883d
#define PCI_DEVICE_ID_S3_868		0x8880
#define PCI_DEVICE_ID_S3_928		0x88b0
#define PCI_DEVICE_ID_S3_864_1		0x88c0
#define PCI_DEVICE_ID_S3_864_2		0x88c1
#define PCI_DEVICE_ID_S3_964_1		0x88d0
#define PCI_DEVICE_ID_S3_964_2		0x88d1
#define PCI_DEVICE_ID_S3_968		0x88f0
#define PCI_DEVICE_ID_S3_TRIO64V2	0x8901
#define PCI_DEVICE_ID_S3_PLATO_PXG	0x8902
#define PCI_DEVICE_ID_S3_ViRGE_DXGX	0x8a01
#define PCI_DEVICE_ID_S3_ViRGE_GX2	0x8a10
#define PCI_DEVICE_ID_S3_ViRGE_MX	0x8c01
#define PCI_DEVICE_ID_S3_ViRGE_MXP	0x8c02
#define PCI_DEVICE_ID_S3_ViRGE_MXPMV	0x8c03
#define PCI_DEVICE_ID_S3_SONICVIBES	0xca00

#define PCI_VENDOR_ID_DUNORD		0x5544
#define PCI_DEVICE_ID_DUNORD_I3000	0x0001
#define PCI_VENDOR_ID_GENROCO		0x5555
#define PCI_DEVICE_ID_GENROCO_HFP832	0x0003

#define PCI_VENDOR_ID_DCI		0x6666
#define PCI_DEVICE_ID_DCI_PCCOM4	0x0001
#define PCI_DEVICE_ID_DCI_PCCOM8	0x0002

#define PCI_VENDOR_ID_INTEL		0x8086
#define PCI_DEVICE_ID_INTEL_21145	0x0039
#define PCI_DEVICE_ID_INTEL_21152BB	0xb152
#define PCI_DEVICE_ID_INTEL_82375	0x0482
#define PCI_DEVICE_ID_INTEL_82424	0x0483
#define PCI_DEVICE_ID_INTEL_82378	0x0484
#define PCI_DEVICE_ID_INTEL_82430	0x0486
#define PCI_DEVICE_ID_INTEL_82434	0x04a3
#define PCI_DEVICE_ID_INTEL_I960	0x0960
#define PCI_DEVICE_ID_INTEL_I960RM	0x0962
#define PCI_DEVICE_ID_INTEL_82541ER	0x1078
#define PCI_DEVICE_ID_INTEL_82541GI_LF	0x107c
#define PCI_DEVICE_ID_INTEL_82542	0x1000
#define PCI_DEVICE_ID_INTEL_82543GC_FIBER	0x1001
#define PCI_DEVICE_ID_INTEL_82543GC_COPPER	0x1004
#define PCI_DEVICE_ID_INTEL_82544EI_COPPER	0x1008
#define PCI_DEVICE_ID_INTEL_82544EI_FIBER	0x1009
#define PCI_DEVICE_ID_INTEL_82544GC_COPPER	0x100C
#define PCI_DEVICE_ID_INTEL_82544GC_LOM		0x100D
#define PCI_DEVICE_ID_INTEL_82540EM		0x100E
#define PCI_DEVICE_ID_INTEL_82545EM_COPPER	0x100F
#define PCI_DEVICE_ID_INTEL_82546EB_COPPER	0x1010
#define PCI_DEVICE_ID_INTEL_82545EM_FIBER	0x1011
#define PCI_DEVICE_ID_INTEL_82546EB_FIBER	0x1012
#define PCI_DEVICE_ID_INTEL_82546GB_COPPER	0x1079
#define PCI_DEVICE_ID_INTEL_82540EM_LOM		0x1015
#define PCI_DEVICE_ID_INTEL_82545GM_COPPER	0x1026
#define PCI_DEVICE_ID_INTEL_82559		0x1030

#define PCI_DEVICE_ID_INTEL_82562ET	0x1031

#define PCI_DEVICE_ID_INTEL_82571EB_COPPER      0x105E
#define PCI_DEVICE_ID_INTEL_82571EB_FIBER       0x105F
#define PCI_DEVICE_ID_INTEL_82571EB_SERDES      0x1060
#define PCI_DEVICE_ID_INTEL_82571EB_QUAD_COPPER 0x10A4
#define PCI_DEVICE_ID_INTEL_82571PT_QUAD_COPPER 0x10D5
#define PCI_DEVICE_ID_INTEL_82571EB_QUAD_FIBER  0x10A5
#define PCI_DEVICE_ID_INTEL_82571EB_QUAD_COPPER_LOWPROFILE  0x10BC
#define PCI_DEVICE_ID_INTEL_82571EB_SERDES_DUAL 0x10D9
#define PCI_DEVICE_ID_INTEL_82571EB_SERDES_QUAD 0x10DA
#define PCI_DEVICE_ID_INTEL_82572EI_COPPER      0x107D
#define PCI_DEVICE_ID_INTEL_82572EI_FIBER       0x107E
#define PCI_DEVICE_ID_INTEL_82572EI_SERDES      0x107F
#define PCI_DEVICE_ID_INTEL_82572EI             0x10B9
#define PCI_DEVICE_ID_INTEL_82573E              0x108B
#define PCI_DEVICE_ID_INTEL_82573E_IAMT         0x108C
#define PCI_DEVICE_ID_INTEL_82573L              0x109A
#define PCI_DEVICE_ID_INTEL_82574L              0x10D3
#define PCI_DEVICE_ID_INTEL_82546GB_QUAD_COPPER_KSP3 0x10B5
#define PCI_DEVICE_ID_INTEL_80003ES2LAN_COPPER_DPT     0x1096
#define PCI_DEVICE_ID_INTEL_80003ES2LAN_SERDES_DPT     0x1098
#define PCI_DEVICE_ID_INTEL_80003ES2LAN_COPPER_SPT     0x10BA
#define PCI_DEVICE_ID_INTEL_80003ES2LAN_SERDES_SPT     0x10BB

#define PCI_DEVICE_ID_INTEL_82815_MC	0x1130

#define PCI_DEVICE_ID_INTEL_82559ER	0x1209
#define PCI_DEVICE_ID_INTEL_82092AA_0	0x1221
#define PCI_DEVICE_ID_INTEL_82092AA_1	0x1222
#define PCI_DEVICE_ID_INTEL_7116	0x1223
#define PCI_DEVICE_ID_INTEL_7205_0	0x255d
#define PCI_DEVICE_ID_INTEL_82596	0x1226
#define PCI_DEVICE_ID_INTEL_82865	0x1227
#define PCI_DEVICE_ID_INTEL_82557	0x1229
#define PCI_DEVICE_ID_INTEL_82437	0x122d
#define PCI_DEVICE_ID_INTEL_82371FB_0	0x122e
#define PCI_DEVICE_ID_INTEL_82371FB_1	0x1230
#define PCI_DEVICE_ID_INTEL_82371MX	0x1234
#define PCI_DEVICE_ID_INTEL_82437MX	0x1235
#define PCI_DEVICE_ID_INTEL_82441	0x1237
#define PCI_DEVICE_ID_INTEL_82380FB	0x124b
#define PCI_DEVICE_ID_INTEL_82439	0x1250
#define PCI_DEVICE_ID_INTEL_80960_RP	0x1960
#define PCI_DEVICE_ID_INTEL_82845_HB	0x1a30
#define PCI_DEVICE_ID_INTEL_82371SB_0	0x7000
#define PCI_DEVICE_ID_INTEL_82371SB_1	0x7010
#define PCI_DEVICE_ID_INTEL_82371SB_2	0x7020
#define PCI_DEVICE_ID_INTEL_82437VX	0x7030
#define PCI_DEVICE_ID_INTEL_82439TX	0x7100
#define PCI_DEVICE_ID_INTEL_82371AB_0	0x7110
#define PCI_DEVICE_ID_INTEL_82371AB	0x7111
#define PCI_DEVICE_ID_INTEL_82371AB_2	0x7112
#define PCI_DEVICE_ID_INTEL_82371AB_3	0x7113
#define PCI_DEVICE_ID_INTEL_82801AA_0	0x2410
#define PCI_DEVICE_ID_INTEL_82801AA_1	0x2411
#define PCI_DEVICE_ID_INTEL_82801AA_2	0x2412
#define PCI_DEVICE_ID_INTEL_82801AA_3	0x2413
#define PCI_DEVICE_ID_INTEL_82801AA_5	0x2415
#define PCI_DEVICE_ID_INTEL_82801AA_6	0x2416
#define PCI_DEVICE_ID_INTEL_82801AA_8	0x2418
#define PCI_DEVICE_ID_INTEL_82801AB_0	0x2420
#define PCI_DEVICE_ID_INTEL_82801AB_1	0x2421
#define PCI_DEVICE_ID_INTEL_82801AB_2	0x2422
#define PCI_DEVICE_ID_INTEL_82801AB_3	0x2423
#define PCI_DEVICE_ID_INTEL_82801AB_5	0x2425
#define PCI_DEVICE_ID_INTEL_82801AB_6	0x2426
#define PCI_DEVICE_ID_INTEL_82801AB_8	0x2428
#define PCI_DEVICE_ID_INTEL_82801BA_0	0x2440
#define PCI_DEVICE_ID_INTEL_82801BA_1	0x2442
#define PCI_DEVICE_ID_INTEL_82801BA_2	0x2443
#define PCI_DEVICE_ID_INTEL_82801BA_3	0x2444
#define PCI_DEVICE_ID_INTEL_82801BA_4	0x2445
#define PCI_DEVICE_ID_INTEL_82801BA_5	0x2446
#define PCI_DEVICE_ID_INTEL_82801BA_6	0x2448
#define PCI_DEVICE_ID_INTEL_82801BA_7	0x2449
#define PCI_DEVICE_ID_INTEL_82801BA_8	0x244a
#define PCI_DEVICE_ID_INTEL_82801BA_9	0x244b
#define PCI_DEVICE_ID_INTEL_82801BA_10	0x244c
#define PCI_DEVICE_ID_INTEL_82801BA_11	0x244e
#define PCI_DEVICE_ID_INTEL_82801E_0	0x2450
#define PCI_DEVICE_ID_INTEL_82801E_2	0x2452
#define PCI_DEVICE_ID_INTEL_82801E_3	0x2453
#define PCI_DEVICE_ID_INTEL_82801E_9	0x2459
#define PCI_DEVICE_ID_INTEL_82801E_11	0x245B
#define PCI_DEVICE_ID_INTEL_82801E_14	0x245D
#define PCI_DEVICE_ID_INTEL_82801E_15	0x245E
#define PCI_DEVICE_ID_INTEL_82801CA_0	0x2480
#define PCI_DEVICE_ID_INTEL_82801CA_2	0x2482
#define PCI_DEVICE_ID_INTEL_82801CA_3	0x2483
#define PCI_DEVICE_ID_INTEL_82801CA_4	0x2484
#define PCI_DEVICE_ID_INTEL_82801CA_5	0x2485
#define PCI_DEVICE_ID_INTEL_82801CA_6	0x2486
#define PCI_DEVICE_ID_INTEL_82801CA_7	0x2487
#define PCI_DEVICE_ID_INTEL_82801CA_10	0x248a
#define PCI_DEVICE_ID_INTEL_82801CA_11	0x248b
#define PCI_DEVICE_ID_INTEL_82801CA_12	0x248c
#define PCI_DEVICE_ID_INTEL_82801DB_0	0x24c0
#define PCI_DEVICE_ID_INTEL_82801DB_2	0x24c2
#define PCI_DEVICE_ID_INTEL_82801DB_3	0x24c3
#define PCI_DEVICE_ID_INTEL_82801DB_4	0x24c4
#define PCI_DEVICE_ID_INTEL_82801DB_5	0x24c5
#define PCI_DEVICE_ID_INTEL_82801DB_6	0x24c6
#define PCI_DEVICE_ID_INTEL_82801DB_7	0x24c7
#define PCI_DEVICE_ID_INTEL_82801DB_10	0x24ca
#define PCI_DEVICE_ID_INTEL_82801DB_11	0x24cb
#define PCI_DEVICE_ID_INTEL_82801DB_12	0x24cc
#define PCI_DEVICE_ID_INTEL_82801DB_13	0x24cd
#define PCI_DEVICE_ID_INTEL_82801EB_0	0x24d0
#define PCI_DEVICE_ID_INTEL_82801EB_1	0x24d1
#define PCI_DEVICE_ID_INTEL_82801EB_2	0x24d2
#define PCI_DEVICE_ID_INTEL_82801EB_3	0x24d3
#define PCI_DEVICE_ID_INTEL_82801EB_4	0x24d4
#define PCI_DEVICE_ID_INTEL_82801EB_5	0x24d5
#define PCI_DEVICE_ID_INTEL_82801EB_6	0x24d6
#define PCI_DEVICE_ID_INTEL_82801EB_7	0x24d7
#define PCI_DEVICE_ID_INTEL_82801DB_10	0x24ca
#define PCI_DEVICE_ID_INTEL_82801EB_11	0x24db
#define PCI_DEVICE_ID_INTEL_82801EB_13	0x24dd
#define PCI_DEVICE_ID_INTEL_ESB_0	0x25a0
#define PCI_DEVICE_ID_INTEL_ESB_1	0x25a1
#define PCI_DEVICE_ID_INTEL_ESB_2	0x25a2
#define PCI_DEVICE_ID_INTEL_ESB_3	0x25a3
#define PCI_DEVICE_ID_INTEL_ESB_31	0x25b0
#define PCI_DEVICE_ID_INTEL_ESB_4	0x25a4
#define PCI_DEVICE_ID_INTEL_ESB_5	0x25a6
#define PCI_DEVICE_ID_INTEL_ESB_6	0x25a7
#define PCI_DEVICE_ID_INTEL_ESB_7	0x25a9
#define PCI_DEVICE_ID_INTEL_ESB_8	0x25aa
#define PCI_DEVICE_ID_INTEL_ESB_9	0x25ab
#define PCI_DEVICE_ID_INTEL_ESB_11	0x25ac
#define PCI_DEVICE_ID_INTEL_ESB_12	0x25ad
#define PCI_DEVICE_ID_INTEL_ESB_13	0x25ae
#define PCI_DEVICE_ID_INTEL_ICH6_0	0x2640
#define PCI_DEVICE_ID_INTEL_ICH6_1	0x2641
#define PCI_DEVICE_ID_INTEL_ICH6_2	0x266f
#define PCI_DEVICE_ID_INTEL_ICH6_3	0x266e
#define PCI_DEVICE_ID_INTEL_82850_HB	0x2530
#define PCI_DEVICE_ID_INTEL_82845G_HB	0x2560
#define PCI_DEVICE_ID_INTEL_80310	0x530d
#define PCI_DEVICE_ID_INTEL_82810_MC1	0x7120
#define PCI_DEVICE_ID_INTEL_82810_IG1	0x7121
#define PCI_DEVICE_ID_INTEL_82810_MC3	0x7122
#define PCI_DEVICE_ID_INTEL_82810_IG3	0x7123
#define PCI_DEVICE_ID_INTEL_82443LX_0	0x7180
#define PCI_DEVICE_ID_INTEL_82443LX_1	0x7181
#define PCI_DEVICE_ID_INTEL_82443BX_0	0x7190
#define PCI_DEVICE_ID_INTEL_82443BX_1	0x7191
#define PCI_DEVICE_ID_INTEL_82443BX_2	0x7192
#define PCI_DEVICE_ID_INTEL_82443MX_0	0x7198
#define PCI_DEVICE_ID_INTEL_82443MX_1	0x7199
#define PCI_DEVICE_ID_INTEL_82443MX_2	0x719a
#define PCI_DEVICE_ID_INTEL_82443MX_3	0x719b
#define PCI_DEVICE_ID_INTEL_82443GX_0	0x71a0
#define PCI_DEVICE_ID_INTEL_82443GX_1	0x71a1
#define PCI_DEVICE_ID_INTEL_82443GX_2	0x71a2
#define PCI_DEVICE_ID_INTEL_82372FB_0	0x7600
#define PCI_DEVICE_ID_INTEL_82372FB_1	0x7601
#define PCI_DEVICE_ID_INTEL_82372FB_2	0x7602
#define PCI_DEVICE_ID_INTEL_82372FB_3	0x7603
#define PCI_DEVICE_ID_INTEL_82454GX	0x84c4
#define PCI_DEVICE_ID_INTEL_82450GX	0x84c5
#define PCI_DEVICE_ID_INTEL_82451NX	0x84ca
#define PCI_DEVICE_ID_INTEL_82454NX	0x84cb

#define PCI_VENDOR_ID_COMPUTONE		0x8e0e
#define PCI_DEVICE_ID_COMPUTONE_IP2EX	0x0291
#define PCI_DEVICE_ID_COMPUTONE_PG	0x0302
#define PCI_SUBVENDOR_ID_COMPUTONE	0x8e0e
#define PCI_SUBDEVICE_ID_COMPUTONE_PG4	0x0001
#define PCI_SUBDEVICE_ID_COMPUTONE_PG8	0x0002
#define PCI_SUBDEVICE_ID_COMPUTONE_PG6	0x0003

#define PCI_VENDOR_ID_KTI		0x8e2e
#define PCI_DEVICE_ID_KTI_ET32P2	0x3000

#define PCI_VENDOR_ID_ADAPTEC		0x9004
#define PCI_DEVICE_ID_ADAPTEC_7810	0x1078
#define PCI_DEVICE_ID_ADAPTEC_7821	0x2178
#define PCI_DEVICE_ID_ADAPTEC_38602	0x3860
#define PCI_DEVICE_ID_ADAPTEC_7850	0x5078
#define PCI_DEVICE_ID_ADAPTEC_7855	0x5578
#define PCI_DEVICE_ID_ADAPTEC_5800	0x5800
#define PCI_DEVICE_ID_ADAPTEC_3860	0x6038
#define PCI_DEVICE_ID_ADAPTEC_1480A	0x6075
#define PCI_DEVICE_ID_ADAPTEC_7860	0x6078
#define PCI_DEVICE_ID_ADAPTEC_7861	0x6178
#define PCI_DEVICE_ID_ADAPTEC_7870	0x7078
#define PCI_DEVICE_ID_ADAPTEC_7871	0x7178
#define PCI_DEVICE_ID_ADAPTEC_7872	0x7278
#define PCI_DEVICE_ID_ADAPTEC_7873	0x7378
#define PCI_DEVICE_ID_ADAPTEC_7874	0x7478
#define PCI_DEVICE_ID_ADAPTEC_7895	0x7895
#define PCI_DEVICE_ID_ADAPTEC_7880	0x8078
#define PCI_DEVICE_ID_ADAPTEC_7881	0x8178
#define PCI_DEVICE_ID_ADAPTEC_7882	0x8278
#define PCI_DEVICE_ID_ADAPTEC_7883	0x8378
#define PCI_DEVICE_ID_ADAPTEC_7884	0x8478
#define PCI_DEVICE_ID_ADAPTEC_7885	0x8578
#define PCI_DEVICE_ID_ADAPTEC_7886	0x8678
#define PCI_DEVICE_ID_ADAPTEC_7887	0x8778
#define PCI_DEVICE_ID_ADAPTEC_7888	0x8878
#define PCI_DEVICE_ID_ADAPTEC_1030	0x8b78

#define PCI_VENDOR_ID_ADAPTEC2		0x9005
#define PCI_DEVICE_ID_ADAPTEC2_2940U2	0x0010
#define PCI_DEVICE_ID_ADAPTEC2_2930U2	0x0011
#define PCI_DEVICE_ID_ADAPTEC2_7890B	0x0013
#define PCI_DEVICE_ID_ADAPTEC2_7890	0x001f
#define PCI_DEVICE_ID_ADAPTEC2_3940U2	0x0050
#define PCI_DEVICE_ID_ADAPTEC2_3950U2D	0x0051
#define PCI_DEVICE_ID_ADAPTEC2_7896	0x005f
#define PCI_DEVICE_ID_ADAPTEC2_7892A	0x0080
#define PCI_DEVICE_ID_ADAPTEC2_7892B	0x0081
#define PCI_DEVICE_ID_ADAPTEC2_7892D	0x0083
#define PCI_DEVICE_ID_ADAPTEC2_7892P	0x008f
#define PCI_DEVICE_ID_ADAPTEC2_7899A	0x00c0
#define PCI_DEVICE_ID_ADAPTEC2_7899B	0x00c1
#define PCI_DEVICE_ID_ADAPTEC2_7899D	0x00c3
#define PCI_DEVICE_ID_ADAPTEC2_7899P	0x00cf

#define PCI_VENDOR_ID_ATRONICS		0x907f
#define PCI_DEVICE_ID_ATRONICS_2015	0x2015

#define PCI_VENDOR_ID_HOLTEK		0x9412
#define PCI_DEVICE_ID_HOLTEK_6565	0x6565

#define PCI_VENDOR_ID_NETMOS		0x9710
#define PCI_DEVICE_ID_NETMOS_9735	0x9735
#define PCI_DEVICE_ID_NETMOS_9835	0x9835

#define PCI_SUBVENDOR_ID_EXSYS		0xd84d
#define PCI_SUBDEVICE_ID_EXSYS_4014	0x4014
#define PCI_SUBDEVICE_ID_EXSYS_4055	0x4055

#define PCI_VENDOR_ID_TIGERJET		0xe159
#define PCI_DEVICE_ID_TIGERJET_300	0x0001
#define PCI_DEVICE_ID_TIGERJET_100	0x0002

#define PCI_VENDOR_ID_ARK		0xedd8
#define PCI_DEVICE_ID_ARK_STING		0xa091
#define PCI_DEVICE_ID_ARK_STINGARK	0xa099
#define PCI_DEVICE_ID_ARK_2000MT	0xa0a1

#define PCI_VENDOR_ID_MICROGATE		0x13c0
#define PCI_DEVICE_ID_MICROGATE_USC	0x0010
#define PCI_DEVICE_ID_MICROGATE_SCC	0x0020
#define PCI_DEVICE_ID_MICROGATE_SCA	0x0030

#define PCI_VENDOR_ID_FREESCALE		0x1957
#define PCI_DEVICE_ID_MPC8536E		0x0050
#define PCI_DEVICE_ID_MPC8536		0x0051
#define PCI_DEVICE_ID_MPC8548E		0x0012
#define PCI_DEVICE_ID_MPC8548		0x0013
#define PCI_DEVICE_ID_MPC8543E		0x0014
#define PCI_DEVICE_ID_MPC8543		0x0015
#define PCI_DEVICE_ID_MPC8547E		0x0018
#define PCI_DEVICE_ID_MPC8545E		0x0019
#define PCI_DEVICE_ID_MPC8545		0x001a
#define PCI_DEVICE_ID_MPC8568E		0x0020
#define PCI_DEVICE_ID_MPC8568		0x0021
#define PCI_DEVICE_ID_MPC8567E		0x0022
#define PCI_DEVICE_ID_MPC8567		0x0023
#define PCI_DEVICE_ID_MPC8533E		0x0030
#define PCI_DEVICE_ID_MPC8533		0x0031
#define PCI_DEVICE_ID_MPC8544E		0x0032
#define PCI_DEVICE_ID_MPC8544		0x0033
#define PCI_DEVICE_ID_MPC8572E		0x0040
#define PCI_DEVICE_ID_MPC8572		0x0041
#define PCI_DEVICE_ID_MPC8641		0x7010
#define PCI_DEVICE_ID_MPC8641D		0x7011
#define PCI_DEVICE_ID_MPC8610		0x7018

#define PCI_VENDOR_ID_ADMTEK		0x1317
#define PCI_DEVICE_ID_ADMTEK_AN983B	0x0985
