/*  
 * (C) Copyright 2016, ZIXC Corporation.
 *
 */

#ifndef __LOAD_IMAGE_H__
#define __LOAD_IMAGE_H__

int load_arm_image( uchar * part_name );
int load_arm_image_ps(void);
int load_arm_image_linux( uchar * part_name );
int load_arm_image_linux_ap(void);
int get_fota_update_flag( void );
int read_fota_update_flag( void );
void start_arm_ps( void );
void start_arm_phy( void );
void start_cpucap_cores( void );
uint32_t read_sys_ddr_kernel_start(void);
int get_gmac_init_flag( void );
int read_gmac_init_flag( void );
int read_gmac_init_overtime( void );


#endif	/* __LOAD_IMAGE_H__ */
