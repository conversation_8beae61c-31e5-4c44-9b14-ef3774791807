/*
 * (C) Copyright 2016, ZIXC Corporation.
 *
 */

#ifndef __PERIPHERAL_H__
#define __PERIPHERAL_H__

#include <common.h>
#include <zx234290.h>

int peripheral_init(void);
int Show_UpdateWait(void);
int Show_UpdateSucc(void);
int Show_UpdateFail(void);
int Show_LowPower(void);
int Show_No_Battery(void);
int Show_PowerOn_Normal(void);
int Show_Charging(void);
int Show_PowerOn_Rtc(void);
int Show_PowerOn_Fota(void);
int zx_get_vbat_voltage(void);
unsigned int zx_get_battery_Status(void);

#endif	/* __PERIPHERAL_H__ */
