/*
 * (C) Copyright 2000
 * <PERSON>, DENX Software Engineering, <EMAIL>.
 *
 * See file CREDITS for list of people who contributed to this
 * project.
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU General Public License as
 * published by the Free Software Foundation; either version 2 of
 * the License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston,
 * MA 02111-1307 USA
 */

/*
 * WARNING! This file is automatically generated - DO NOT EDIT!
 */
#define	KERNELBASE	-**********
#define	STATE	0
#define	NEXT_TASK	64
#define	COUNTER	52
#define	PROCESSOR	916
#define	SIGPENDING	8
#define	TSS	576
#define	MM	880
#define	TASK_STRUCT_SIZE	928
#define	KSP	0
#define	PG_TABLES	4
#define	PGD	8
#define	LAST_SYSCALL	20
#define	PT_REGS	12
#define	PF_TRACESYS	32
#define	TASK_FLAGS	4
#define	TSS_FPR0	24
#define	TSS_FPSCR	284
#define	TSS_SMP_FORK_RET	288
#define	TASK_UNION_SIZE	8192
#define	STACK_FRAME_OVERHEAD	16
#define	INT_FRAME_SIZE	192
#define	GPR0	16
#define	GPR1	20
#define	GPR2	24
#define	GPR3	28
#define	GPR4	32
#define	GPR5	36
#define	GPR6	40
#define	GPR7	44
#define	GPR8	48
#define	GPR9	52
#define	GPR10	56
#define	GPR11	60
#define	GPR12	64
#define	GPR13	68
#define	GPR14	72
#define	GPR15	76
#define	GPR16	80
#define	GPR17	84
#define	GPR18	88
#define	GPR19	92
#define	GPR20	96
#define	GPR21	100
#define	GPR22	104
#define	GPR23	108
#define	GPR24	112
#define	GPR25	116
#define	GPR26	120
#define	GPR27	124
#define	GPR28	128
#define	GPR29	132
#define	GPR30	136
#define	GPR31	140
#define	_NIP	144
#define	_MSR	148
#define	_CTR	156
#define	_LINK	160
#define	_CCR	168
#define	_XER	164
#define	_DAR	180
#define	_DSISR	184
#define	ORIG_GPR3	152
#define	RESULT	188
#define	TRAP	176
