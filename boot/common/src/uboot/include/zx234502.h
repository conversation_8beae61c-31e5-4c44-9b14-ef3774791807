/*  
 * (C) Copyright 2016, ZIXC Corporation.
 *
 */

#ifndef __ZX234502_H__
#define __ZX234502_H__


#define  ZX234502_I2C_SLAVE_ADDR  0x13

#define  ZX234502_REG_PIS          0xe
#define ZX234502_REG_PIS_POWERON_IT_MASK         (1<<6)
#define ZX234502_REG_PIS_POWERON_IT_SHIFT        6

#define ZX234502_REG_CBIS  0x0C/*Charg /Battery Int Status*/
#define ZX234502_REG_CBIS_NOBAT_MASK        (1<<5)
#define ZX234502_REG_CBIS_NOBAT_SHIFT       5

int zx234502_boost_flag(int *boost_flag);
int zx234502_battery_status(int *bat_state);
int zx234502_charger_enable(void);

#endif	/* __ZX234502_H__ */
