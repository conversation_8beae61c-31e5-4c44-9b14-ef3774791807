/* $Id: linux_logo.h,v 1.5 1998/07/30 16:30:58 jj Exp $
 * include/linux/linux_logo.h: This is a linux logo
 *                             to be displayed on boot.
 *
 * Copyright (C) 1996 <PERSON> (<EMAIL>)
 * Copyright (C) 1996,1998 J<PERSON><PERSON> (<EMAIL>)
 *
 * You can put anything here, but:
 * LINUX_LOGO_COLORS has to be less than 224
 * image size has to be 80x80
 * values have to start from 0x20
 * (i.e. RGB(linux_logo_red[0],
 *           linux_logo_green[0],
 *           linux_logo_blue[0]) is color 0x20)
 * BW image has to be 80x80 as well, with MS bit
 * on the left
 * Serial_console ascii image can be any size,
 * but should contain %s to display the version
 */

#if LINUX_LOGO_COLORS == 214

unsigned char linux_logo_red[] __initdata = {
  0x02, 0x9E, 0xE9, 0xC4, 0x50, 0xC9, 0xC4, 0xE9,
  0x65, 0xE3, 0xC2, 0x25, 0xA4, 0xEC, 0x90, 0xA6,
  0xC4, 0x6A, 0xD1, 0xF3, 0x12, 0xED, 0xA0, 0xC2,
  0xB8, 0xD5, 0xDB, 0xD2, 0x3E, 0x16, 0xEB, 0x54,
  0xA9, 0xCD, 0xF5, 0x0A, 0xBA, 0xB3, 0xDC, 0x74,
  0xCE, 0xF6, 0xD3, 0xC5, 0xEA, 0xB8, 0xED, 0x5E,
  0xE5, 0x26, 0xF4, 0xA9, 0x82, 0x94, 0xE6, 0x38,
  0xF2, 0x0F, 0x7F, 0x49, 0xE5, 0xF4, 0xD3, 0xC3,
  0xC2, 0x1E, 0xD5, 0xC6, 0xA4, 0xFA, 0x0A, 0xBA,
  0xD4, 0xEB, 0xEA, 0xEC, 0xA8, 0xBC, 0xB4, 0xDC,
  0x84, 0xE4, 0xCE, 0xEC, 0x92, 0xCD, 0xDC, 0x8B,
  0xCC, 0x1E, 0xF6, 0xB2, 0x60, 0x2A, 0x96, 0x52,
  0x0F, 0xBD, 0xFA, 0xCC, 0xB8, 0x7A, 0x4C, 0xD2,
  0x06, 0xEF, 0x44, 0x64, 0xF4, 0xBA, 0xCE, 0xE6,
  0x8A, 0x6F, 0x3C, 0x70, 0x7C, 0x9C, 0xBA, 0xDF,
  0x2C, 0x4D, 0x3B, 0xCA, 0xDE, 0xCE, 0xEE, 0x46,
  0x6A, 0xAC, 0x96, 0xE5, 0x96, 0x7A, 0xBA, 0xB6,
  0xE2, 0x7E, 0xAA, 0xC5, 0x96, 0x9E, 0xC2, 0xAA,
  0xDA, 0x35, 0xB6, 0x82, 0x88, 0xBE, 0xC2, 0x9E,
  0xB4, 0xD5, 0xDA, 0x9C, 0xA0, 0xD0, 0xA8, 0xC7,
  0x72, 0xF2, 0xDB, 0x76, 0xDC, 0xBE, 0xAA, 0xF4,
  0x87, 0x2F, 0x53, 0x8E, 0x36, 0xCE, 0xE6, 0xCA,
  0xCB, 0xE4, 0xD6, 0xAA, 0x42, 0x5D, 0xB4, 0x59,
  0x1C, 0xC8, 0x96, 0x6C, 0xDA, 0xCE, 0xE6, 0xCB,
  0x96, 0x16, 0xFA, 0xBE, 0xAE, 0xFE, 0x6E, 0xD6,
  0xCE, 0xB6, 0xE5, 0xED, 0xDB, 0xDC, 0xF4, 0x72,
  0x1F, 0xAE, 0xE6, 0xC2, 0xCA, 0xC4
};

unsigned char linux_logo_green[] __initdata = {
  0x02, 0x88, 0xC4, 0x85, 0x44, 0xA2, 0xA8, 0xE5,
  0x65, 0xA6, 0xC2, 0x24, 0xA4, 0xB4, 0x62, 0x86,
  0x94, 0x44, 0xD2, 0xB6, 0x12, 0xD4, 0x73, 0x96,
  0x92, 0x95, 0xB2, 0xC2, 0x36, 0x0E, 0xBC, 0x54,
  0x75, 0xA5, 0xF5, 0x0A, 0xB2, 0x83, 0xC2, 0x74,
  0x9B, 0xBD, 0xA2, 0xCA, 0xDA, 0x8C, 0xCB, 0x42,
  0xAC, 0x12, 0xDA, 0x7B, 0x54, 0x94, 0xD2, 0x24,
  0xBE, 0x06, 0x65, 0x33, 0xBB, 0xBC, 0xAB, 0x8C,
  0x92, 0x1E, 0x9B, 0xB6, 0x6E, 0xFB, 0x04, 0xA2,
  0xC8, 0xBD, 0xAD, 0xEC, 0x92, 0xBC, 0x7B, 0x9D,
  0x84, 0xC4, 0xC4, 0xB4, 0x6C, 0x93, 0xA3, 0x5E,
  0x8D, 0x13, 0xD6, 0x82, 0x4C, 0x2A, 0x7A, 0x5A,
  0x0D, 0x82, 0xBB, 0xCC, 0x8B, 0x6A, 0x3C, 0xBE,
  0x06, 0xC4, 0x44, 0x45, 0xDB, 0x96, 0xB6, 0xDE,
  0x8A, 0x4D, 0x3C, 0x5A, 0x7C, 0x9C, 0xAA, 0xCB,
  0x1C, 0x4D, 0x2E, 0xB2, 0xBE, 0xAA, 0xDE, 0x3E,
  0x6A, 0xAC, 0x82, 0xE5, 0x72, 0x62, 0x92, 0x9E,
  0xCA, 0x4A, 0x8E, 0xBE, 0x86, 0x6B, 0xAA, 0x9A,
  0xBE, 0x34, 0xAB, 0x76, 0x6E, 0x9A, 0x9E, 0x62,
  0x76, 0xCE, 0xD3, 0x92, 0x7C, 0xB8, 0x7E, 0xC6,
  0x5E, 0xE2, 0xC3, 0x54, 0xAA, 0x9E, 0x8A, 0xCA,
  0x63, 0x2D, 0x3B, 0x8E, 0x1A, 0x9E, 0xC2, 0xA6,
  0xCB, 0xDC, 0xD6, 0x8E, 0x26, 0x5C, 0xB4, 0x45,
  0x1C, 0xB8, 0x6E, 0x4C, 0xBC, 0xAE, 0xD6, 0x92,
  0x63, 0x16, 0xF6, 0x8C, 0x7A, 0xFE, 0x6E, 0xBA,
  0xC6, 0x86, 0xAA, 0xAE, 0xDB, 0xA4, 0xD4, 0x56,
  0x0E, 0x6E, 0xB6, 0xB2, 0xBE, 0xBE
};

unsigned char linux_logo_blue[] __initdata = {
  0x04, 0x28, 0x10, 0x0B, 0x14, 0x14, 0x74, 0xC7,
  0x64, 0x0E, 0xC3, 0x24, 0xA4, 0x0C, 0x10, 0x20,
  0x0D, 0x04, 0xD1, 0x0D, 0x13, 0x22, 0x0A, 0x40,
  0x14, 0x0C, 0x11, 0x94, 0x0C, 0x08, 0x0B, 0x56,
  0x09, 0x47, 0xF4, 0x0B, 0x9C, 0x07, 0x54, 0x74,
  0x0F, 0x0C, 0x0F, 0xC7, 0x6C, 0x14, 0x14, 0x11,
  0x0B, 0x04, 0x12, 0x0C, 0x05, 0x94, 0x94, 0x0A,
  0x34, 0x09, 0x14, 0x08, 0x2F, 0x15, 0x19, 0x11,
  0x28, 0x0C, 0x0B, 0x94, 0x08, 0xFA, 0x08, 0x7C,
  0xBC, 0x15, 0x0A, 0xEC, 0x64, 0xBB, 0x0A, 0x0C,
  0x84, 0x2C, 0xA0, 0x15, 0x10, 0x0D, 0x0B, 0x0E,
  0x0A, 0x07, 0x10, 0x3C, 0x24, 0x2C, 0x28, 0x5C,
  0x0A, 0x0D, 0x0A, 0xC1, 0x22, 0x4C, 0x10, 0x94,
  0x04, 0x0F, 0x45, 0x08, 0x31, 0x54, 0x3C, 0xBC,
  0x8C, 0x09, 0x3C, 0x18, 0x7C, 0x9C, 0x7C, 0x91,
  0x0C, 0x4D, 0x17, 0x74, 0x0C, 0x48, 0x9C, 0x3C,
  0x6A, 0xAC, 0x5C, 0xE3, 0x29, 0x3C, 0x2C, 0x7C,
  0x6C, 0x04, 0x14, 0xA9, 0x74, 0x07, 0x2C, 0x74,
  0x4C, 0x34, 0x97, 0x5C, 0x38, 0x0C, 0x5C, 0x04,
  0x0C, 0xBA, 0xBC, 0x78, 0x18, 0x88, 0x24, 0xC2,
  0x3C, 0xB4, 0x87, 0x0C, 0x14, 0x4C, 0x3C, 0x10,
  0x17, 0x2C, 0x0A, 0x8C, 0x04, 0x1C, 0x44, 0x2C,
  0xCD, 0xD8, 0xD4, 0x34, 0x0C, 0x5B, 0xB4, 0x1E,
  0x1D, 0xAC, 0x24, 0x18, 0x20, 0x5C, 0xB4, 0x1C,
  0x09, 0x14, 0xFC, 0x0C, 0x10, 0xFC, 0x6C, 0x7C,
  0xB4, 0x1C, 0x15, 0x17, 0xDB, 0x18, 0x21, 0x24,
  0x04, 0x04, 0x44, 0x8C, 0x8C, 0xB7
};

unsigned char linux_logo[] __initdata = {
  0xBF, 0x95, 0x90, 0xCB, 0x95, 0xA1, 0x2C, 0x2C,
  0x95, 0x55, 0xCB, 0x90, 0xCB, 0x95, 0x2C, 0x95,
  0xCB, 0x47, 0x94, 0x95, 0xA1, 0xD6, 0xD6, 0x2C,
  0x90, 0x47, 0x70, 0x2C, 0x6D, 0x2A, 0x6D, 0xD6,
  0xA1, 0x2C, 0x55, 0x95, 0x2C, 0x2C, 0x55, 0x55,
  0x95, 0xA1, 0xA1, 0xA1, 0x6D, 0xBF, 0x2A, 0x2A,
  0xBF, 0x83, 0xBF, 0x95, 0x90, 0xCB, 0x95, 0xA1,
  0x2C, 0x2C, 0x95, 0x55, 0xCB, 0x90, 0xCB, 0x95,
  0x2C, 0x95, 0xCB, 0x47, 0x94, 0x95, 0xA1, 0xD6,
  0xD6, 0x2C, 0x90, 0x47, 0x70, 0x2C, 0x6D, 0x2A,
  0x95, 0x47, 0x47, 0x90, 0x2C, 0x2C, 0x2C, 0x95,
  0x55, 0x55, 0xCB, 0x90, 0xCB, 0x55, 0x55, 0xCB,
  0x47, 0xE6, 0x70, 0x95, 0xD6, 0xD6, 0xA1, 0x2C,
  0x55, 0x55, 0x95, 0xD6, 0x6D, 0xD6, 0xA1, 0x2C,
  0x2C, 0x95, 0x55, 0x95, 0x95, 0x95, 0x2C, 0x2C,
  0xA1, 0xA1, 0x2C, 0x2C, 0xA1, 0xD6, 0xD6, 0xD6,
  0xD6, 0xD6, 0x95, 0x47, 0x47, 0x90, 0x2C, 0x2C,
  0x2C, 0x95, 0x55, 0x55, 0xCB, 0x90, 0xCB, 0x55,
  0x55, 0xCB, 0x47, 0xE6, 0x70, 0x95, 0xD6, 0xD6,
  0xA1, 0x2C, 0x55, 0x55, 0x95, 0xD6, 0x6D, 0xD6,
  0x90, 0x47, 0x47, 0x70, 0x2C, 0xA1, 0x2C, 0x95,
  0x55, 0x55, 0x90, 0xCB, 0x55, 0x55, 0x55, 0x70,
  0x94, 0x70, 0x95, 0xA1, 0xD6, 0xD6, 0xA1, 0x2C,
  0x95, 0x95, 0x2C, 0xA1, 0xD6, 0xA1, 0x2C, 0x2C,
  0x95, 0x55, 0xCB, 0x95, 0xD6, 0xA1, 0x2C, 0x95,
  0xA1, 0xD6, 0xD6, 0xA1, 0xA1, 0xD6, 0xA1, 0xA1,
  0xA1, 0x2C, 0x90, 0x47, 0x47, 0x70, 0x2C, 0xA1,
  0x2C, 0x95, 0x55, 0x55, 0x90, 0xCB, 0x55, 0x55,
  0x55, 0x70, 0x94, 0x70, 0x95, 0xA1, 0xD6, 0xD6,
  0xA1, 0x2C, 0x95, 0x95, 0x2C, 0xD6, 0xD6, 0xA1,
  0x94, 0xA0, 0x47, 0x55, 0x2C, 0xD6, 0xA1, 0x95,
  0x55, 0x55, 0xCB, 0xCB, 0x55, 0x55, 0xCB, 0xCB,
  0x55, 0x95, 0x2C, 0xA1, 0xD6, 0xD6, 0xA1, 0x2C,
  0x95, 0x95, 0x2C, 0x2C, 0x2C, 0x2C, 0x2C, 0x95,
  0x55, 0x55, 0x2C, 0x3F, 0x80, 0x20, 0x88, 0x88,
  0x88, 0x20, 0x88, 0xB1, 0x2C, 0xA1, 0x2C, 0x2C,
  0x95, 0xCB, 0x94, 0xA0, 0x47, 0x55, 0x2C, 0xD6,
  0xA1, 0x95, 0x55, 0x55, 0xCB, 0xCB, 0x55, 0x55,
  0xCB, 0xCB, 0x55, 0x95, 0x2C, 0xA1, 0xD6, 0xD6,
  0xA1, 0x2C, 0x95, 0x95, 0x2C, 0x2C, 0x2C, 0x2C,
  0x94, 0x94, 0x70, 0x2C, 0xA1, 0xD6, 0xA1, 0x2C,
  0x55, 0x55, 0xCB, 0x55, 0x55, 0x55, 0x55, 0x55,
  0x95, 0x2C, 0xD6, 0xD6, 0xD6, 0xA1, 0x2C, 0x95,
  0x55, 0x55, 0x95, 0x95, 0x95, 0x95, 0x95, 0x95,
  0x2C, 0x94, 0x80, 0x20, 0x20, 0x20, 0x20, 0x20,
  0x20, 0x20, 0x20, 0x20, 0x88, 0x92, 0xA1, 0x95,
  0x55, 0x90, 0x94, 0x94, 0x70, 0x2C, 0xA1, 0xD6,
  0xA1, 0x2C, 0x55, 0x55, 0xCB, 0x55, 0x55, 0x55,
  0x55, 0x55, 0x95, 0x2C, 0xD6, 0xD6, 0xD6, 0xA1,
  0x2C, 0x95, 0x55, 0x55, 0x55, 0x95, 0x95, 0x95,
  0x70, 0x70, 0x55, 0x2C, 0xD6, 0xD6, 0xA1, 0x95,
  0x55, 0x90, 0xCB, 0xCB, 0x55, 0x55, 0x2C, 0x2C,
  0xA1, 0xD6, 0xA1, 0xA1, 0x2C, 0x2C, 0x95, 0x55,
  0x55, 0x55, 0x95, 0x95, 0x2C, 0x95, 0x95, 0xD6,
  0xB1, 0x88, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20,
  0x20, 0x20, 0x20, 0x80, 0x34, 0x88, 0x43, 0x47,
  0x95, 0xCB, 0x70, 0x70, 0x55, 0x2C, 0xD6, 0xD6,
  0xA1, 0x95, 0x55, 0x90, 0xCB, 0xCB, 0x55, 0x55,
  0x2C, 0x2C, 0xA1, 0xD6, 0xA1, 0xA1, 0xA1, 0x2C,
  0x55, 0x55, 0x55, 0x55, 0x2C, 0x95, 0x2C, 0x2C,
  0x55, 0x55, 0x95, 0x2C, 0xA1, 0xA1, 0x2C, 0x55,
  0x90, 0x70, 0x90, 0x55, 0x95, 0x95, 0xA1, 0xA1,
  0xA1, 0xA1, 0xA1, 0xA1, 0x2C, 0x95, 0x95, 0x95,
  0x95, 0x2C, 0x2C, 0x2C, 0x2C, 0x2C, 0x2C, 0xD5,
  0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20,
  0x20, 0x20, 0x88, 0x7D, 0x3F, 0xB1, 0x80, 0x20,
  0x99, 0x2C, 0x55, 0x55, 0x95, 0x2C, 0xA1, 0xA1,
  0x2C, 0x55, 0x90, 0x70, 0x90, 0x55, 0x95, 0x95,
  0xA1, 0xA1, 0xA1, 0xA1, 0xA1, 0x2C, 0x2C, 0x2C,
  0x95, 0x55, 0x95, 0x95, 0x2C, 0x2C, 0x2C, 0x2C,
  0x95, 0x90, 0x55, 0x2C, 0xA1, 0xA1, 0x95, 0xCB,
  0x70, 0x94, 0x90, 0x55, 0x95, 0xA1, 0xA1, 0xA1,
  0x2C, 0x2C, 0x2C, 0x2C, 0x95, 0x95, 0x95, 0x95,
  0x95, 0x95, 0x95, 0x95, 0x95, 0x95, 0xA1, 0x88,
  0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20,
  0x20, 0x20, 0x20, 0xB1, 0x47, 0xD5, 0x7D, 0x43,
  0x20, 0x70, 0x95, 0x90, 0x55, 0x2C, 0xA1, 0xA1,
  0x95, 0xCB, 0x70, 0x94, 0x90, 0x55, 0x95, 0xA1,
  0xA1, 0xA1, 0x2C, 0x95, 0x2C, 0x2C, 0x95, 0x95,
  0x95, 0x95, 0x95, 0x2C, 0x95, 0x95, 0x95, 0x95,
  0x95, 0x90, 0x55, 0x2C, 0xD6, 0xD6, 0x2C, 0x90,
  0x94, 0x70, 0x55, 0x95, 0x2C, 0xD6, 0xD6, 0xA1,
  0x95, 0x95, 0x95, 0x2C, 0x2C, 0x95, 0x55, 0x55,
  0xCB, 0xCB, 0xCB, 0x55, 0xCB, 0x55, 0x47, 0x20,
  0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20,
  0x20, 0x20, 0x88, 0xB1, 0x3F, 0x92, 0x2B, 0x80,
  0x20, 0x80, 0xD6, 0x70, 0x55, 0x2C, 0xD6, 0xD6,
  0x2C, 0x90, 0x94, 0x70, 0x55, 0x95, 0x2C, 0xD6,
  0xD6, 0xA1, 0x2C, 0x95, 0x95, 0x2C, 0x2C, 0x95,
  0x95, 0x55, 0x90, 0xCB, 0xCB, 0xCB, 0xCB, 0x55,
  0xD6, 0x55, 0x95, 0xA1, 0xD6, 0xA1, 0x55, 0x70,
  0x94, 0x55, 0x95, 0xA1, 0xA1, 0xA1, 0xA1, 0x95,
  0x55, 0x55, 0x55, 0x95, 0x55, 0x55, 0xCB, 0x90,
  0x70, 0x90, 0xCB, 0x55, 0x55, 0xA1, 0xD8, 0x20,
  0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20,
  0x20, 0x20, 0x88, 0xD8, 0xE1, 0x88, 0x20, 0x20,
  0x88, 0x88, 0xE6, 0x55, 0x2C, 0xA1, 0xD6, 0xA1,
  0x55, 0x70, 0x94, 0x55, 0x95, 0xA1, 0xA1, 0xA1,
  0xA1, 0x95, 0x55, 0x55, 0x95, 0x95, 0x55, 0x55,
  0x90, 0x90, 0x90, 0x90, 0xCB, 0x55, 0x55, 0x55,
  0xD6, 0x2C, 0xA1, 0xD6, 0xD6, 0xA1, 0xCB, 0x70,
  0x70, 0x95, 0x2C, 0xA1, 0xA1, 0x2C, 0x2C, 0x55,
  0xCB, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55,
  0x55, 0x95, 0x2C, 0x95, 0x2C, 0xD6, 0x20, 0x20,
  0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20,
  0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20,
  0x20, 0x20, 0x80, 0xD6, 0xA1, 0xD6, 0xD6, 0xA1,
  0xCB, 0x70, 0x70, 0x95, 0x2C, 0xA1, 0xA1, 0x2C,
  0x2C, 0x55, 0xCB, 0xCB, 0x55, 0x55, 0x55, 0x55,
  0x55, 0x55, 0x55, 0x95, 0x2C, 0x2C, 0x2C, 0x2C,
  0xD6, 0xA1, 0xA1, 0xA1, 0xA1, 0x55, 0x70, 0x94,
  0xCB, 0x95, 0xA1, 0xA1, 0x2C, 0x95, 0xCB, 0x55,
  0x90, 0xCB, 0x55, 0x55, 0x55, 0x55, 0x95, 0xA1,
  0xA1, 0xA1, 0xA1, 0xA1, 0xA1, 0x95, 0x20, 0x20,
  0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20,
  0x88, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20,
  0x20, 0x20, 0x88, 0x95, 0xA1, 0xA1, 0xA1, 0x55,
  0x70, 0x94, 0xCB, 0x95, 0xA1, 0xA1, 0x2C, 0x95,
  0xCB, 0xCB, 0x90, 0xCB, 0x55, 0x55, 0x55, 0x55,
  0x95, 0x2C, 0xA1, 0xA1, 0xA1, 0xA1, 0xA1, 0xA1,
  0x2C, 0x2C, 0x2C, 0x95, 0x95, 0xCB, 0x70, 0x70,
  0x95, 0x2C, 0x2C, 0x95, 0xCB, 0x70, 0x90, 0xCB,
  0xCB, 0x55, 0x55, 0xCB, 0x55, 0x55, 0x2C, 0xD6,
  0xD6, 0xD6, 0xD6, 0xA1, 0x2C, 0x70, 0x20, 0x20,
  0x88, 0x43, 0xD8, 0x43, 0x20, 0x20, 0x20, 0x20,
  0x20, 0x88, 0x88, 0x43, 0x2B, 0xD8, 0x20, 0x20,
  0x20, 0x20, 0x20, 0x3F, 0x2C, 0x95, 0x95, 0xCB,
  0x70, 0x70, 0x95, 0x2C, 0x2C, 0x95, 0xCB, 0x90,
  0x90, 0xCB, 0x55, 0xCB, 0x55, 0xCB, 0x55, 0x95,
  0x2C, 0xD6, 0xD6, 0xD6, 0xD6, 0xA1, 0x2C, 0x2C,
  0xA1, 0x95, 0x95, 0x55, 0xCB, 0x70, 0x90, 0x55,
  0x2C, 0x2C, 0x2C, 0x55, 0x70, 0x70, 0x55, 0x95,
  0x95, 0xCB, 0x90, 0x90, 0x90, 0x95, 0x2C, 0xA1,
  0xD6, 0xD6, 0x2C, 0x2C, 0x95, 0x70, 0x20, 0x20,
  0x80, 0x2B, 0x34, 0x2B, 0x88, 0x20, 0x20, 0x20,
  0x88, 0xB1, 0x28, 0x28, 0x2B, 0x7D, 0x80, 0x20,
  0x20, 0x20, 0x20, 0x92, 0x95, 0x55, 0xCB, 0x70,
  0x90, 0x55, 0x2C, 0x2C, 0x2C, 0x55, 0x70, 0x70,
  0x55, 0x95, 0x55, 0x55, 0x90, 0x90, 0x90, 0x55,
  0x2C, 0xA1, 0xD6, 0xA1, 0x2C, 0x2C, 0x95, 0x95,
  0xA1, 0x95, 0x55, 0xCB, 0x90, 0x70, 0xCB, 0x95,
  0xA1, 0x95, 0x95, 0xCB, 0x90, 0xCB, 0x95, 0x2C,
  0x95, 0x70, 0x70, 0x90, 0x55, 0x2C, 0xA1, 0xA1,
  0x2C, 0x2C, 0x55, 0xCB, 0x55, 0x90, 0x20, 0x34,
  0x90, 0x6D, 0x70, 0xD8, 0x43, 0x20, 0x20, 0x88,
  0x3F, 0x55, 0xA1, 0x2A, 0xD6, 0x7D, 0x43, 0x20,
  0x20, 0x20, 0x88, 0x7D, 0x55, 0xCB, 0x90, 0x70,
  0xCB, 0x95, 0xA1, 0x95, 0x95, 0xCB, 0x70, 0xCB,
  0x95, 0xA1, 0x95, 0x70, 0x70, 0xCB, 0x55, 0x2C,
  0xA1, 0xA1, 0xA1, 0x95, 0x55, 0x55, 0x55, 0x95,
  0x2C, 0x55, 0x90, 0x70, 0x94, 0x90, 0x95, 0x2C,
  0x2C, 0x95, 0xCB, 0x90, 0x55, 0x95, 0xA1, 0xA1,
  0x95, 0x90, 0x90, 0x95, 0xA1, 0xD6, 0xD6, 0x6D,
  0xA1, 0x95, 0x55, 0xCB, 0x55, 0xCB, 0x20, 0x99,
  0xBF, 0xA3, 0xA3, 0x90, 0x20, 0x20, 0x20, 0x92,
  0x83, 0x6B, 0x6B, 0x6B, 0xA3, 0x70, 0x88, 0x20,
  0x20, 0x20, 0x20, 0x2B, 0x90, 0x70, 0x94, 0x90,
  0x95, 0x2C, 0x2C, 0x95, 0xCB, 0x90, 0x55, 0x95,
  0xA1, 0x2C, 0x55, 0x90, 0x90, 0x95, 0xA1, 0xD6,
  0xD6, 0x6D, 0xA1, 0x95, 0x55, 0xCB, 0x55, 0x55,
  0x2C, 0x55, 0x70, 0x70, 0x94, 0x90, 0x95, 0x2C,
  0x2C, 0x55, 0xCB, 0xCB, 0x95, 0x2C, 0x2C, 0x2C,
  0x55, 0x55, 0x95, 0xA1, 0x6D, 0xBF, 0x6D, 0xD6,
  0x95, 0x55, 0x90, 0xCB, 0x55, 0x95, 0x88, 0x95,
  0x2C, 0x3F, 0x6D, 0x6B, 0x34, 0x20, 0x20, 0x47,
  0x65, 0xD6, 0xE1, 0x3F, 0x2A, 0x6B, 0x2B, 0x20,
  0x20, 0x20, 0x20, 0x43, 0x70, 0x70, 0x94, 0x90,
  0x95, 0x2C, 0x2C, 0x55, 0x55, 0x55, 0x95, 0x2C,
  0xA1, 0x2C, 0x55, 0xCB, 0x95, 0xA1, 0x6D, 0xBF,
  0x6D, 0xD6, 0x2C, 0x55, 0x90, 0xCB, 0x95, 0x95,
  0x95, 0x55, 0x70, 0x94, 0x70, 0x55, 0x2C, 0xA1,
  0x2C, 0x55, 0xCB, 0x55, 0x2C, 0x95, 0x2C, 0x95,
  0x95, 0x95, 0xA1, 0x6D, 0xBF, 0x2A, 0xD6, 0x95,
  0x70, 0x94, 0x94, 0x70, 0x55, 0x55, 0x20, 0xBF,
  0xC9, 0xB1, 0x99, 0x42, 0xB1, 0x61, 0x7D, 0x94,
  0x65, 0xB1, 0x88, 0x99, 0xD5, 0xE5, 0x7F, 0x20,
  0x20, 0x20, 0x20, 0x43, 0x70, 0x94, 0x70, 0x55,
  0x2C, 0xA1, 0x2C, 0x55, 0x90, 0x55, 0x2C, 0x95,
  0x2C, 0x95, 0x95, 0x2C, 0xA1, 0x6D, 0xBF, 0xBF,
  0xD6, 0x55, 0x70, 0x94, 0x94, 0x70, 0xCB, 0x55,
  0x55, 0xCB, 0x70, 0x94, 0x70, 0x95, 0xA1, 0xA1,
  0x95, 0x55, 0x55, 0x95, 0x2C, 0x95, 0x95, 0x95,
  0x95, 0xA1, 0x6D, 0x2A, 0x2A, 0xD6, 0x55, 0x94,
  0xE6, 0xE6, 0x47, 0x70, 0x55, 0x95, 0x20, 0x2A,
  0xD8, 0x43, 0xC9, 0x83, 0x98, 0x79, 0x34, 0x9F,
  0x6B, 0x43, 0x20, 0x88, 0x2B, 0x65, 0xA0, 0x20,
  0x20, 0x20, 0x20, 0xE1, 0x70, 0x94, 0x70, 0x95,
  0xA1, 0xA1, 0x95, 0x55, 0x55, 0x95, 0x2C, 0x95,
  0x95, 0x95, 0x95, 0xA1, 0x6D, 0xBF, 0x2A, 0xD6,
  0x55, 0x94, 0xE6, 0xE6, 0x47, 0x70, 0x55, 0x55,
  0x94, 0x70, 0x94, 0x47, 0x70, 0x95, 0x2C, 0x2C,
  0x95, 0xCB, 0x95, 0x2C, 0x2C, 0xA1, 0x2C, 0x2C,
  0xA1, 0xD6, 0x6D, 0x6D, 0xA1, 0xCB, 0x47, 0x28,
  0xE6, 0x47, 0x70, 0x55, 0x95, 0xA1, 0x20, 0x2C,
  0x7F, 0x88, 0xF0, 0xC6, 0x25, 0x5E, 0xCF, 0x2F,
  0xE7, 0x9A, 0x20, 0x88, 0x99, 0x65, 0x3F, 0x20,
  0x20, 0x20, 0x20, 0x34, 0x94, 0x47, 0x70, 0x95,
  0xA1, 0x2C, 0x55, 0xCB, 0x95, 0x2C, 0x2C, 0xA1,
  0x2C, 0x2C, 0xA1, 0xD6, 0x6D, 0x6D, 0xA1, 0xCB,
  0x94, 0x28, 0xA0, 0x47, 0x70, 0x55, 0x95, 0x95,
  0x47, 0x70, 0x90, 0x94, 0x70, 0x95, 0xA1, 0x2C,
  0x55, 0x55, 0x2C, 0xA1, 0xA1, 0xA1, 0xA1, 0x2C,
  0xA1, 0x6D, 0x2A, 0xD6, 0x55, 0x47, 0x28, 0x28,
  0x47, 0x70, 0x55, 0x95, 0x2C, 0xA1, 0x20, 0x28,
  0xEC, 0x86, 0xBE, 0x48, 0x3E, 0x3E, 0x3A, 0x25,
  0x4E, 0xAE, 0x93, 0xD7, 0xEC, 0xD1, 0x34, 0x20,
  0x20, 0x20, 0x20, 0x43, 0x55, 0x94, 0x70, 0x95,
  0xA1, 0xA1, 0x55, 0xCB, 0x2C, 0xA1, 0xA1, 0xA1,
  0xA1, 0x2C, 0xA1, 0x6D, 0x6D, 0xD6, 0x55, 0x47,
  0x28, 0x28, 0x47, 0x70, 0x55, 0x95, 0x2C, 0x2C,
  0x95, 0x95, 0x55, 0x90, 0xCB, 0x2C, 0xA1, 0xA1,
  0x55, 0x55, 0x2C, 0xD6, 0xD6, 0xA1, 0xA1, 0x2C,
  0xD6, 0x6D, 0x6D, 0xA1, 0x70, 0x28, 0xD5, 0xE6,
  0x70, 0x55, 0x95, 0x2C, 0xA1, 0xD6, 0x20, 0xE1,
  0x26, 0x84, 0x76, 0x73, 0x9C, 0x22, 0x4E, 0x35,
  0x8C, 0x7A, 0x4E, 0xDC, 0x8E, 0x7E, 0x3D, 0x88,
  0x20, 0x20, 0x20, 0x88, 0x2C, 0x90, 0x90, 0x95,
  0xA1, 0x2C, 0x55, 0x55, 0x2C, 0xD6, 0xD6, 0xD6,
  0x2C, 0x2C, 0xD6, 0x2A, 0x6D, 0x2C, 0x70, 0x28,
  0xD5, 0xE6, 0x70, 0x55, 0x95, 0xA1, 0x2C, 0xA1,
  0xBF, 0xA1, 0x95, 0xCB, 0xCB, 0x2C, 0xA1, 0xA1,
  0x95, 0x95, 0xA1, 0xD6, 0xD6, 0xA1, 0x2C, 0x95,
  0xD6, 0x6D, 0xD6, 0x95, 0x94, 0x28, 0xE6, 0x70,
  0x55, 0x95, 0xA1, 0xA1, 0xA1, 0xD6, 0x20, 0x57,
  0xE4, 0xDF, 0x50, 0x3E, 0x22, 0x4E, 0x35, 0x8C,
  0x8C, 0x52, 0x52, 0x7A, 0x4E, 0x58, 0xD7, 0x20,
  0x20, 0x20, 0x20, 0x88, 0x2C, 0xCB, 0x55, 0x2C,
  0xA1, 0xA1, 0x95, 0x95, 0xA1, 0xD6, 0xD6, 0xA1,
  0x2C, 0x95, 0xA1, 0x6D, 0x6D, 0x95, 0x47, 0xA0,
  0xE6, 0x70, 0x55, 0x95, 0x2C, 0xA1, 0xA1, 0xA1,
  0xD2, 0x95, 0x55, 0x90, 0x55, 0x2C, 0xD6, 0xA1,
  0x95, 0x95, 0xA1, 0xD6, 0xD6, 0x2C, 0x95, 0x2C,
  0xA1, 0x6D, 0xA1, 0x55, 0x94, 0x47, 0x94, 0xCB,
  0x55, 0x95, 0x2C, 0xA1, 0xD6, 0xD6, 0x59, 0xC8,
  0xE3, 0x76, 0x2D, 0x3E, 0x22, 0x4E, 0x8C, 0x35,
  0x52, 0x52, 0xEE, 0x3A, 0x4D, 0xED, 0x24, 0x20,
  0x20, 0x20, 0x20, 0x20, 0x28, 0xCB, 0x55, 0x2C,
  0xD6, 0xA1, 0x95, 0x95, 0xA1, 0xD6, 0xA1, 0x2C,
  0x95, 0x2C, 0xD6, 0x6D, 0xA1, 0x55, 0x94, 0xE6,
  0x70, 0xCB, 0x55, 0x95, 0xA1, 0xD6, 0xD6, 0xA1,
  0xD0, 0x94, 0x94, 0x90, 0x55, 0x2C, 0xA1, 0xA1,
  0x55, 0x95, 0xA1, 0xA1, 0xA1, 0x2C, 0x95, 0x2C,
  0xA1, 0xD6, 0x2C, 0x70, 0x94, 0x94, 0x94, 0x94,
  0x70, 0x55, 0xA1, 0xD6, 0xA1, 0xD6, 0x88, 0x77,
  0x38, 0xC4, 0x3E, 0x69, 0x4E, 0x35, 0x8C, 0xEE,
  0x35, 0x89, 0x30, 0x30, 0x4A, 0x48, 0x3C, 0x20,
  0x20, 0x88, 0x20, 0x20, 0xD8, 0x2C, 0x55, 0x2C,
  0xD6, 0xA1, 0x95, 0x95, 0x2C, 0xD6, 0xA1, 0x2C,
  0x95, 0x2C, 0xA1, 0xD6, 0x2C, 0x90, 0x94, 0x47,
  0x94, 0x94, 0x70, 0x55, 0x2C, 0xD6, 0xA1, 0x95,
  0x95, 0x28, 0x47, 0x90, 0x95, 0x2C, 0xA1, 0x2C,
  0x95, 0x55, 0x95, 0xA1, 0xD6, 0xA1, 0x2C, 0x2C,
  0xA1, 0xA1, 0x55, 0x70, 0x94, 0x47, 0x94, 0x94,
  0x70, 0x2C, 0xD6, 0xD6, 0x2C, 0xA1, 0x43, 0x98,
  0x54, 0x48, 0x3E, 0x22, 0x35, 0xEE, 0xEE, 0x9C,
  0x4D, 0x45, 0x75, 0x4A, 0xDF, 0x7B, 0x3D, 0x20,
  0xD8, 0x28, 0x2B, 0x88, 0x20, 0x95, 0x95, 0x2C,
  0xA1, 0x2C, 0x55, 0x55, 0x2C, 0xA1, 0xD6, 0xA1,
  0x2C, 0x95, 0xA1, 0x2C, 0x55, 0x70, 0x94, 0x94,
  0x94, 0x94, 0x70, 0x95, 0xD6, 0xD6, 0x2C, 0x95,
  0x70, 0x28, 0x47, 0x55, 0x95, 0x2C, 0x2C, 0x2C,
  0x95, 0x95, 0x95, 0xA1, 0xA1, 0xA1, 0x95, 0x55,
  0x95, 0x95, 0x55, 0x70, 0x70, 0x70, 0x94, 0x70,
  0x55, 0xD6, 0x6D, 0xD6, 0x95, 0x2C, 0x20, 0x43,
  0xBB, 0xC8, 0x36, 0x30, 0x30, 0x38, 0x45, 0x6E,
  0xE3, 0x75, 0x78, 0x37, 0xBD, 0xD9, 0x3F, 0x20,
  0x88, 0xD5, 0x70, 0xB1, 0x88, 0xA0, 0x95, 0x2C,
  0x2C, 0xA1, 0x95, 0x55, 0x95, 0xA1, 0xA1, 0xA1,
  0x2C, 0x55, 0x95, 0x2C, 0x55, 0x70, 0x70, 0x70,
  0x94, 0x70, 0x55, 0xD6, 0x6D, 0x6D, 0x95, 0x55,
  0x94, 0x47, 0x70, 0x95, 0x2C, 0x2C, 0x2C, 0xA1,
  0x2C, 0x95, 0x2C, 0xA1, 0xD6, 0xA1, 0x2C, 0x55,
  0x55, 0x95, 0x95, 0x55, 0x55, 0x55, 0x55, 0x95,
  0xA1, 0x6D, 0x4B, 0xD6, 0x55, 0xD6, 0x20, 0xD8,
  0xD6, 0x67, 0xDA, 0x4D, 0xED, 0x62, 0x78, 0x78,
  0x23, 0x84, 0x67, 0xF5, 0x4B, 0xBF, 0x90, 0x88,
  0x88, 0x2B, 0x47, 0x99, 0x20, 0x43, 0xD6, 0x2C,
  0x2C, 0xA1, 0x2C, 0x95, 0x2C, 0xA1, 0xD6, 0xA1,
  0x95, 0x95, 0x55, 0x95, 0x55, 0x55, 0x55, 0x55,
  0x55, 0x95, 0xD6, 0x6D, 0xBF, 0xD6, 0x55, 0xCB,
  0x55, 0x55, 0x55, 0x2C, 0x2C, 0x2C, 0x2C, 0xA1,
  0x2C, 0x2C, 0x2C, 0xA1, 0xA1, 0x2C, 0x2C, 0x95,
  0x55, 0x95, 0x95, 0x2C, 0x2C, 0x2C, 0x2C, 0xA1,
  0x6D, 0x2A, 0x2A, 0xA1, 0x55, 0x55, 0x20, 0xD8,
  0x6D, 0xAB, 0x96, 0x7E, 0x64, 0x53, 0x36, 0x36,
  0xC6, 0x63, 0x6D, 0xD0, 0x6B, 0xE5, 0xA3, 0x7D,
  0x20, 0x88, 0x80, 0x88, 0x20, 0x20, 0xC9, 0xA1,
  0x2C, 0xA1, 0xA1, 0x2C, 0x2C, 0xA1, 0xA1, 0xA1,
  0x95, 0x95, 0x55, 0x95, 0x95, 0x2C, 0x2C, 0x2C,
  0x2C, 0xA1, 0x6D, 0xBF, 0x6D, 0xA1, 0x55, 0x55,
  0x95, 0x95, 0x95, 0x95, 0x2C, 0x2C, 0x2C, 0xA1,
  0xA1, 0x95, 0x95, 0x2C, 0x2C, 0x2C, 0x2C, 0x95,
  0x55, 0x55, 0x2C, 0x2C, 0xA1, 0xA1, 0xD6, 0xD6,
  0x6D, 0x6D, 0xA1, 0x55, 0x2C, 0xD8, 0x20, 0xB1,
  0xA3, 0x4B, 0x6D, 0xD9, 0xA7, 0x6C, 0xAF, 0xB2,
  0x6D, 0x2A, 0x83, 0x42, 0xE5, 0xE5, 0x65, 0x2C,
  0x20, 0x20, 0x88, 0x20, 0x20, 0x20, 0x88, 0x95,
  0x2C, 0xA1, 0x2C, 0x95, 0x95, 0x2C, 0x2C, 0x2C,
  0x2C, 0x95, 0x55, 0x55, 0x2C, 0x2C, 0xA1, 0xA1,
  0xD6, 0xD6, 0x6D, 0x6D, 0xA1, 0x55, 0xCB, 0x55,
  0x95, 0x55, 0x95, 0x95, 0x2C, 0x2C, 0x95, 0x2C,
  0x2C, 0x95, 0x95, 0x95, 0x95, 0x95, 0x2C, 0x95,
  0x55, 0x95, 0x2C, 0x2C, 0xA1, 0xA1, 0xD6, 0xA1,
  0xA1, 0x2C, 0x55, 0x55, 0x28, 0x88, 0x43, 0x2A,
  0xE5, 0xA3, 0x6D, 0x6D, 0x6D, 0x6D, 0x6D, 0x6D,
  0xBF, 0xA3, 0x42, 0xE5, 0xE5, 0xE5, 0xE5, 0x65,
  0xB1, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0xD8,
  0xD6, 0x2C, 0x2C, 0x95, 0x95, 0x95, 0x95, 0x2C,
  0x95, 0x95, 0x55, 0x95, 0x2C, 0x2C, 0xA1, 0xA1,
  0xA1, 0xA1, 0xA1, 0x2C, 0x95, 0x90, 0x90, 0x55,
  0x90, 0xCB, 0x55, 0x95, 0x95, 0x95, 0x95, 0x95,
  0x2C, 0x2C, 0x95, 0x55, 0x95, 0x95, 0x95, 0x55,
  0x55, 0xCB, 0x55, 0x2C, 0x95, 0x95, 0x95, 0x95,
  0x55, 0x90, 0x90, 0x90, 0xE1, 0x43, 0x28, 0xE5,
  0xE5, 0x65, 0xD0, 0x6D, 0x6D, 0x6D, 0x2A, 0xD2,
  0x42, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5,
  0xD6, 0x20, 0x20, 0x20, 0x20, 0x20, 0x88, 0x88,
  0xD5, 0x2C, 0x2C, 0x2C, 0x95, 0x55, 0x95, 0x95,
  0x95, 0x55, 0x55, 0xCB, 0x55, 0x95, 0x2C, 0x95,
  0x95, 0x95, 0x55, 0x90, 0x70, 0x70, 0x70, 0x90,
  0x70, 0x70, 0xCB, 0x55, 0x55, 0x95, 0x95, 0x95,
  0x2C, 0x95, 0x95, 0x55, 0x55, 0x55, 0x55, 0xCB,
  0x70, 0x70, 0x70, 0xCB, 0x90, 0x90, 0x70, 0x94,
  0x94, 0x94, 0x2C, 0x80, 0x20, 0xE1, 0xA3, 0xE5,
  0xE5, 0xE5, 0x42, 0xEC, 0xD0, 0x83, 0xA3, 0x65,
  0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5,
  0x65, 0x7D, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20,
  0x88, 0x2C, 0x95, 0x95, 0x95, 0x55, 0x55, 0x55,
  0x55, 0xCB, 0x70, 0x70, 0x90, 0x90, 0x90, 0x90,
  0x70, 0x94, 0x94, 0x94, 0x70, 0x70, 0x70, 0x70,
  0x70, 0x55, 0x55, 0x55, 0x95, 0x95, 0x95, 0x95,
  0x2C, 0x2C, 0x95, 0x55, 0x55, 0x55, 0x55, 0x55,
  0x90, 0x70, 0x90, 0x55, 0x55, 0xCB, 0x70, 0x94,
  0x94, 0x95, 0xD8, 0x20, 0x88, 0x70, 0xE5, 0xE5,
  0xE5, 0xE5, 0xE5, 0xE5, 0x65, 0xE5, 0xE5, 0xE5,
  0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5,
  0xE5, 0x47, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20,
  0x20, 0xE1, 0x6D, 0x2C, 0x95, 0x55, 0x55, 0x55,
  0x55, 0x55, 0x90, 0x70, 0x70, 0x55, 0x55, 0xCB,
  0x70, 0x94, 0x94, 0x94, 0x70, 0x90, 0x70, 0x94,
  0x55, 0x2C, 0x2C, 0x2C, 0x95, 0x2C, 0x95, 0x95,
  0x2C, 0x2C, 0x2C, 0x55, 0x55, 0x55, 0x55, 0x55,
  0xCB, 0xCB, 0x95, 0x2C, 0x2C, 0x95, 0x55, 0x90,
  0x55, 0x99, 0x20, 0x20, 0xE1, 0xA3, 0xE5, 0xE5,
  0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5,
  0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5,
  0xE5, 0xD6, 0x88, 0x20, 0x20, 0x20, 0x20, 0x20,
  0x20, 0x20, 0x2B, 0x6D, 0x95, 0x95, 0x55, 0x55,
  0x55, 0x55, 0xCB, 0x55, 0x95, 0x2C, 0x2C, 0x95,
  0x55, 0x90, 0xCB, 0xCB, 0xCB, 0xCB, 0x90, 0x70,
  0x2C, 0xD6, 0xD6, 0x2C, 0x2C, 0x95, 0x95, 0x95,
  0x95, 0x95, 0x95, 0x2C, 0x95, 0x95, 0x95, 0x95,
  0x95, 0x95, 0x2C, 0xA1, 0x2C, 0x95, 0x55, 0x95,
  0xE6, 0x88, 0x20, 0x20, 0x3F, 0xA3, 0xE5, 0xE5,
  0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5,
  0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5,
  0x42, 0xA3, 0x88, 0x20, 0x20, 0x20, 0x20, 0x20,
  0x20, 0x20, 0x88, 0x2B, 0xD6, 0x95, 0x95, 0x95,
  0x95, 0x95, 0x95, 0x95, 0x2C, 0xA1, 0x2C, 0x95,
  0x55, 0x55, 0x95, 0x95, 0x95, 0x55, 0x55, 0x55,
  0xA1, 0xD6, 0xD6, 0xA1, 0x2C, 0x2C, 0x95, 0x2C,
  0x2C, 0x2C, 0x95, 0x2C, 0x95, 0x95, 0x55, 0x95,
  0x95, 0x2C, 0x2C, 0x2C, 0x95, 0xCB, 0xCB, 0x94,
  0x20, 0x20, 0x20, 0x20, 0xE6, 0x83, 0x65, 0xE5,
  0xE5, 0xE5, 0xE5, 0x42, 0x6B, 0x6B, 0xE5, 0xE5,
  0xE5, 0xE5, 0xE5, 0x42, 0x6B, 0x6B, 0xA3, 0xD2,
  0xD2, 0x6B, 0xC9, 0x20, 0x20, 0x88, 0x20, 0x20,
  0x20, 0x20, 0x20, 0x88, 0x8A, 0xA1, 0x95, 0x95,
  0x95, 0x55, 0x95, 0x2C, 0xA1, 0x2C, 0x95, 0xCB,
  0xCB, 0x55, 0x95, 0x95, 0x95, 0x55, 0x55, 0x95,
  0x6D, 0x6D, 0x6D, 0xD6, 0xA1, 0x2C, 0x2C, 0x95,
  0x2C, 0x95, 0x2C, 0x95, 0x95, 0x95, 0x95, 0x95,
  0x95, 0x95, 0x95, 0x55, 0x70, 0x70, 0x2C, 0x80,
  0x88, 0x20, 0x20, 0x80, 0x94, 0xD6, 0x32, 0x6B,
  0xE5, 0xE5, 0xE5, 0x42, 0x6B, 0xE5, 0xE5, 0xE5,
  0xE5, 0xE5, 0xE5, 0xA3, 0xD2, 0xD0, 0xBF, 0x2A,
  0x2A, 0xD0, 0x6D, 0x34, 0x20, 0xE1, 0x88, 0x20,
  0x20, 0x20, 0x20, 0x20, 0x88, 0xA1, 0x95, 0x95,
  0x95, 0x95, 0x95, 0x95, 0x95, 0x55, 0x70, 0x70,
  0x70, 0x90, 0xCB, 0xCB, 0xCB, 0x95, 0x95, 0x2C,
  0xD0, 0x6D, 0xD6, 0xD6, 0xA1, 0xA1, 0xA1, 0x2C,
  0x2C, 0x2C, 0x2C, 0x95, 0x55, 0x55, 0x55, 0x95,
  0x95, 0x2C, 0x95, 0x55, 0xCB, 0xCB, 0x95, 0x88,
  0x20, 0x20, 0x88, 0xD8, 0x2C, 0xD1, 0xE5, 0xE5,
  0xE5, 0xE5, 0xE5, 0x65, 0x65, 0xE5, 0xE5, 0xE5,
  0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0x42, 0x6B, 0xEC,
  0xBF, 0x2A, 0xEC, 0x95, 0x20, 0x34, 0x2B, 0xE1,
  0x20, 0x20, 0x20, 0x20, 0x20, 0x99, 0x95, 0x55,
  0x55, 0x55, 0x95, 0x95, 0x95, 0x55, 0xCB, 0xCB,
  0x55, 0x55, 0xCB, 0xCB, 0xCB, 0x55, 0x95, 0x95,
  0x32, 0xA1, 0xA1, 0xA1, 0xA1, 0xA1, 0x2C, 0x2C,
  0xA1, 0x95, 0x95, 0x95, 0x55, 0xCB, 0xCB, 0x55,
  0x95, 0x95, 0x95, 0x95, 0x95, 0x55, 0x99, 0x20,
  0xE1, 0xE1, 0x43, 0x47, 0x6B, 0xE5, 0xE5, 0xE5,
  0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5,
  0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5,
  0x42, 0xEC, 0xBF, 0xA3, 0x8A, 0x20, 0x88, 0xD8,
  0x2B, 0x20, 0x20, 0x20, 0x88, 0x88, 0x2C, 0xCB,
  0xCB, 0x95, 0x95, 0x2C, 0x95, 0x95, 0x55, 0x95,
  0x55, 0x55, 0x55, 0x55, 0x55, 0x95, 0x55, 0x95,
  0x6D, 0x55, 0x55, 0x55, 0x95, 0x95, 0x2C, 0x95,
  0x2C, 0x95, 0x95, 0x55, 0x55, 0x55, 0x55, 0x95,
  0x95, 0x95, 0x95, 0x95, 0x95, 0xA1, 0x34, 0x20,
  0xC9, 0x20, 0xE1, 0xA3, 0xE5, 0xE5, 0xE5, 0xE5,
  0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5,
  0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5,
  0xE5, 0xE5, 0xA3, 0x83, 0x6D, 0x20, 0x88, 0x88,
  0x2B, 0x34, 0x20, 0x20, 0x20, 0x88, 0xD5, 0x55,
  0x55, 0x55, 0x95, 0x95, 0x95, 0x95, 0x95, 0x95,
  0x95, 0x95, 0x95, 0x95, 0x55, 0x55, 0x95, 0x95,
  0x2C, 0x55, 0xCB, 0x55, 0xCB, 0x55, 0x55, 0x95,
  0x95, 0x95, 0x95, 0x2C, 0x2C, 0x2C, 0x2C, 0x2C,
  0x2C, 0x95, 0x95, 0x55, 0x95, 0x2C, 0x20, 0xD8,
  0xE1, 0x20, 0x70, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5,
  0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5,
  0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5,
  0xE5, 0xE5, 0xE5, 0x65, 0xA3, 0x92, 0x43, 0x7D,
  0xD8, 0xC9, 0x88, 0x20, 0x20, 0x20, 0x43, 0xD6,
  0x2C, 0x2C, 0x95, 0x95, 0x95, 0x55, 0x95, 0x2C,
  0x95, 0x95, 0x95, 0x95, 0x95, 0x2C, 0x95, 0x2C,
  0xA1, 0x55, 0x55, 0x55, 0x55, 0x95, 0x95, 0x55,
  0x55, 0x55, 0x95, 0x95, 0x2C, 0x2C, 0xA1, 0x2C,
  0xA1, 0x2C, 0x2C, 0x95, 0x2C, 0x99, 0x88, 0xB1,
  0x20, 0xD8, 0x42, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5,
  0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5,
  0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5,
  0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xCB, 0x34, 0x8A,
  0xC9, 0x34, 0x2B, 0x20, 0x20, 0x20, 0x20, 0x90,
  0xA1, 0xA1, 0xA1, 0x2C, 0x2C, 0x95, 0x95, 0x2C,
  0x2C, 0x95, 0x95, 0x95, 0x95, 0x2C, 0x2C, 0x2C,
  0xD6, 0x2C, 0x55, 0x55, 0x95, 0x2C, 0x2C, 0x2C,
  0x55, 0xCB, 0x55, 0x2C, 0x2C, 0xA1, 0x2C, 0xA1,
  0xA1, 0xA1, 0x2C, 0x2C, 0x6D, 0x43, 0xD8, 0x80,
  0x88, 0xCB, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5,
  0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5,
  0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5,
  0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0x32, 0x80, 0xE1,
  0x80, 0x20, 0xB1, 0x20, 0x20, 0x20, 0x20, 0xC9,
  0xD6, 0xA1, 0xA1, 0xA1, 0x2C, 0xA1, 0x2C, 0x2C,
  0x2C, 0x55, 0x55, 0x55, 0x95, 0x95, 0x95, 0x55,
  0xD6, 0x95, 0x95, 0x95, 0x2C, 0xA1, 0x2C, 0x2C,
  0x95, 0x95, 0x95, 0x95, 0x95, 0x2C, 0x95, 0x2C,
  0x2C, 0x2C, 0x2C, 0x95, 0xCB, 0x20, 0xC9, 0x20,
  0xE1, 0xA3, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5,
  0xE5, 0xE5, 0xE5, 0x42, 0xE5, 0xE5, 0xE5, 0xE5,
  0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5,
  0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xD8, 0x20,
  0x20, 0x20, 0x2B, 0x43, 0x20, 0x20, 0x20, 0x88,
  0xD6, 0x2C, 0x2C, 0x2C, 0x95, 0x95, 0x95, 0x55,
  0x95, 0x55, 0x55, 0xCB, 0x55, 0xCB, 0xCB, 0x55,
  0x2C, 0x55, 0x55, 0x95, 0x2C, 0x2C, 0xA1, 0x95,
  0x55, 0x95, 0x55, 0x95, 0x95, 0x95, 0x95, 0x95,
  0x55, 0xCB, 0x70, 0xCB, 0xC9, 0x80, 0x2B, 0x20,
  0xA0, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5,
  0xE5, 0xE5, 0xE5, 0x42, 0xE5, 0xE5, 0xE5, 0xE5,
  0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5,
  0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0x92, 0x20,
  0x20, 0x20, 0xE1, 0xD8, 0x20, 0x20, 0x20, 0x20,
  0x95, 0x95, 0x55, 0xCB, 0x90, 0x90, 0x70, 0x90,
  0x90, 0x90, 0xCB, 0xCB, 0xCB, 0xCB, 0x55, 0x95,
  0x95, 0x55, 0x55, 0x95, 0x95, 0x2C, 0x2C, 0x2C,
  0x95, 0x95, 0x55, 0x55, 0x55, 0x95, 0x95, 0x55,
  0x90, 0x47, 0xA0, 0x55, 0x20, 0x2B, 0x43, 0x88,
  0x6D, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5,
  0xE5, 0xE5, 0xE5, 0x6B, 0xE5, 0xE5, 0xE5, 0xE5,
  0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5,
  0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0x28, 0x20,
  0x20, 0x20, 0xE1, 0xE1, 0x20, 0x20, 0x20, 0x20,
  0x28, 0x55, 0x90, 0x47, 0xA0, 0x47, 0x94, 0x70,
  0x55, 0x95, 0x95, 0x55, 0xCB, 0x55, 0x55, 0x2C,
  0x2C, 0x2C, 0x95, 0x95, 0x95, 0x2C, 0x2C, 0x2C,
  0x95, 0x2C, 0x95, 0x95, 0x95, 0x95, 0x95, 0x55,
  0x94, 0xE6, 0x70, 0x2B, 0x88, 0x2B, 0x88, 0xE1,
  0x65, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5,
  0xE5, 0xE5, 0xE5, 0x6B, 0xE5, 0xE5, 0xE5, 0xE5,
  0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5,
  0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0x47, 0x20,
  0x20, 0x20, 0xE1, 0x34, 0x20, 0x20, 0x20, 0x20,
  0xB1, 0x95, 0x94, 0xE6, 0xA0, 0x47, 0x70, 0x55,
  0x2C, 0xA1, 0x2C, 0x55, 0x90, 0xCB, 0x2C, 0xD6,
  0x6D, 0xA1, 0x2C, 0x95, 0x95, 0xA1, 0x2C, 0xA1,
  0x2C, 0x2C, 0x95, 0x95, 0x95, 0x95, 0x95, 0x55,
  0x70, 0xE6, 0x70, 0x20, 0x20, 0x7D, 0x20, 0x8A,
  0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5,
  0xE5, 0xE5, 0x65, 0xA3, 0xE5, 0xE5, 0xE5, 0xE5,
  0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5,
  0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0x94, 0x20,
  0x20, 0x20, 0xD8, 0x88, 0x20, 0x20, 0x20, 0x20,
  0xD8, 0x2C, 0x94, 0x47, 0x47, 0x90, 0x95, 0x95,
  0xA1, 0x6D, 0xA1, 0x90, 0x94, 0x55, 0x2C, 0xD6,
  0xD0, 0xA1, 0x95, 0x95, 0x2C, 0x2C, 0xA1, 0x2C,
  0x95, 0x95, 0x55, 0x55, 0x55, 0x95, 0x2C, 0x2C,
  0xCB, 0x95, 0xD8, 0x20, 0x20, 0xB1, 0x88, 0x28,
  0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5,
  0xE5, 0xE5, 0xE2, 0xA3, 0xE5, 0xE5, 0xE5, 0xE5,
  0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5,
  0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xCB, 0x20,
  0x20, 0x20, 0x2B, 0x20, 0x20, 0x20, 0x20, 0x20,
  0x88, 0xD6, 0x55, 0x47, 0x94, 0x55, 0x2C, 0xA1,
  0xA1, 0xD6, 0x95, 0x94, 0x94, 0x55, 0xD6, 0x6D,
  0xBF, 0x95, 0x90, 0xCB, 0x2C, 0x2C, 0x2C, 0x2C,
  0x55, 0x95, 0xCB, 0x90, 0x90, 0x95, 0x2C, 0x95,
  0x90, 0x70, 0x20, 0x20, 0x34, 0x8A, 0x20, 0x94,
  0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5,
  0xE5, 0xE5, 0x65, 0x6B, 0xE5, 0xE5, 0xE5, 0xE5,
  0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5,
  0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xCB, 0x20,
  0x20, 0x88, 0x2B, 0x20, 0x20, 0x20, 0x20, 0x20,
  0x88, 0xD6, 0xCB, 0x47, 0x94, 0x55, 0xA1, 0xD6,
  0xD6, 0x2C, 0xCB, 0x47, 0x70, 0xA1, 0x6D, 0x2A,
  0x95, 0x47, 0x47, 0x70, 0x95, 0xA1, 0x2C, 0x95,
  0x55, 0x55, 0x90, 0x90, 0x55, 0x55, 0x55, 0x90,
  0x47, 0xD5, 0x20, 0x20, 0x80, 0xD5, 0x43, 0xCB,
  0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5,
  0xE5, 0xE5, 0x42, 0x6B, 0xE5, 0xE5, 0xE5, 0xE5,
  0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5,
  0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xCB, 0x20,
  0x20, 0x80, 0x34, 0x20, 0x20, 0x20, 0x88, 0x20,
  0x20, 0x2C, 0x47, 0xE6, 0x70, 0x2C, 0xD6, 0xD6,
  0xA1, 0x2C, 0x55, 0xCB, 0x95, 0xA1, 0x6D, 0xD6,
  0x90, 0x47, 0x47, 0x90, 0x2C, 0xA1, 0x2C, 0x95,
  0x55, 0x55, 0x90, 0x90, 0x55, 0x55, 0x55, 0x70,
  0x94, 0x8A, 0x20, 0x88, 0x88, 0xE1, 0xD8, 0x95,
  0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5,
  0xE5, 0xE5, 0xE2, 0x42, 0xE5, 0xE5, 0xE5, 0xE5,
  0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5,
  0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0x47, 0x20,
  0x43, 0x7D, 0x43, 0x80, 0x88, 0x20, 0x20, 0x20,
  0x88, 0xCB, 0x94, 0x70, 0x55, 0xA1, 0xD6, 0xD6,
  0xA1, 0x2C, 0x2C, 0x95, 0xA1, 0xA1, 0xD6, 0xA1,
  0x94, 0xE6, 0x47, 0x55, 0x2C, 0xD6, 0xA1, 0x95,
  0x55, 0x55, 0xCB, 0xCB, 0x55, 0x55, 0xCB, 0xCB,
  0x55, 0xA0, 0x43, 0x86, 0x86, 0x43, 0xD8, 0xCB,
  0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5,
  0xE5, 0xE5, 0x65, 0x6B, 0xE5, 0xE5, 0xE5, 0xE5,
  0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5,
  0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0x3F, 0x80,
  0xD8, 0x80, 0x88, 0x34, 0xD8, 0x2B, 0xD8, 0x20,
  0x99, 0x90, 0x55, 0x95, 0x2C, 0xA1, 0xD6, 0xD6,
  0xA1, 0x95, 0x95, 0x95, 0x2C, 0x2C, 0x2C, 0x2C,
  0x94, 0x94, 0x70, 0x2C, 0xA1, 0xD6, 0xA1, 0x2C,
  0x55, 0x55, 0xCB, 0x55, 0x55, 0x55, 0x55, 0x55,
  0x95, 0x44, 0xBC, 0x3E, 0x5D, 0xD3, 0x79, 0x92,
  0xA3, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5,
  0xE5, 0xE5, 0x65, 0x42, 0xE5, 0xE5, 0xE5, 0xE5,
  0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5,
  0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0x65, 0x9A, 0x34,
  0x20, 0x20, 0x20, 0x20, 0x20, 0x43, 0x99, 0xE1,
  0x70, 0x55, 0x95, 0xA1, 0xD6, 0xD6, 0xD6, 0xA1,
  0x2C, 0x95, 0x55, 0x55, 0x95, 0x95, 0x95, 0x95,
  0x70, 0x70, 0x55, 0x2C, 0xD6, 0xD6, 0xA1, 0x95,
  0x55, 0x90, 0xCB, 0xCB, 0x55, 0x55, 0x2C, 0x2C,
  0x32, 0x9D, 0xEB, 0x5D, 0x69, 0x49, 0x84, 0xF0,
  0xB1, 0xEC, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5,
  0xE5, 0xE5, 0x42, 0x6B, 0xE5, 0xE5, 0xE5, 0xE5,
  0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5,
  0xE5, 0xE5, 0xE5, 0xE5, 0xC1, 0x4E, 0x21, 0x20,
  0x20, 0x20, 0x20, 0x20, 0x20, 0x34, 0xC9, 0xD8,
  0xBB, 0xA1, 0xA1, 0xA1, 0xA1, 0xA1, 0x2C, 0x2C,
  0x95, 0x55, 0x55, 0x55, 0x95, 0x95, 0x2C, 0x2C,
  0x55, 0xCB, 0x95, 0x2C, 0xA1, 0xA1, 0x2C, 0x55,
  0x90, 0x70, 0x90, 0x55, 0x95, 0x95, 0x6D, 0xD0,
  0xC2, 0x48, 0x6A, 0x49, 0x69, 0x82, 0x5D, 0x2F,
  0x59, 0x7D, 0xBF, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5,
  0xE5, 0xE5, 0x65, 0x6B, 0xE5, 0xE5, 0xE5, 0xE5,
  0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5,
  0xE5, 0xE5, 0xE5, 0xE5, 0xEA, 0xC7, 0x7E, 0x66,
  0x20, 0x20, 0x20, 0x20, 0x20, 0x34, 0x43, 0x5A,
  0x46, 0x27, 0xA1, 0xA1, 0xA1, 0xA1, 0x2C, 0x95,
  0x95, 0x55, 0x95, 0x2C, 0x2C, 0x2C, 0x2C, 0x2C,
  0x95, 0x90, 0x55, 0x2C, 0xA1, 0xA1, 0x95, 0x55,
  0x94, 0x94, 0x2C, 0x2A, 0x72, 0x3B, 0x56, 0xDD,
  0xDF, 0x29, 0x5D, 0x49, 0x89, 0x5D, 0x3E, 0x69,
  0x93, 0x66, 0x34, 0xA1, 0xE5, 0xE5, 0xE5, 0xE5,
  0xE5, 0xE5, 0x65, 0x42, 0xE5, 0xE5, 0xE5, 0xE5,
  0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5,
  0xE5, 0xE5, 0xE5, 0xE5, 0xEA, 0x3E, 0x5A, 0x66,
  0x20, 0x20, 0x20, 0x20, 0x20, 0x66, 0x5B, 0x73,
  0x89, 0x4C, 0xBF, 0x2C, 0x95, 0x2C, 0x2C, 0x95,
  0x95, 0x95, 0x95, 0x95, 0x95, 0x95, 0x95, 0x95,
  0x2C, 0x70, 0x55, 0x2C, 0xD6, 0xD6, 0x2C, 0xCB,
  0x70, 0x55, 0xE7, 0x60, 0x4A, 0x48, 0xCD, 0x4A,
  0x29, 0x73, 0x5D, 0x82, 0x49, 0x49, 0x49, 0x49,
  0x3A, 0x57, 0x88, 0x88, 0x70, 0xE5, 0xE5, 0xE5,
  0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5,
  0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5,
  0xE5, 0xE5, 0xE5, 0x42, 0x73, 0x50, 0xBE, 0x79,
  0x20, 0x20, 0x20, 0x20, 0x66, 0xCC, 0x37, 0x9C,
  0x3E, 0xCE, 0xBF, 0x95, 0x95, 0x95, 0x2C, 0x95,
  0x95, 0x55, 0xCB, 0xCB, 0xCB, 0xCB, 0xCB, 0x55,
  0xA1, 0x55, 0x95, 0xA1, 0xD6, 0xA1, 0x55, 0x94,
  0x94, 0xE8, 0x60, 0xC4, 0x3E, 0x2D, 0x2D, 0x2D,
  0x33, 0x5D, 0x82, 0x49, 0x49, 0x49, 0x49, 0x49,
  0x89, 0xAA, 0x59, 0x20, 0x20, 0x28, 0xE5, 0xE5,
  0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5,
  0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5,
  0xE5, 0xE5, 0xEC, 0x4A, 0x2D, 0x50, 0x78, 0x2E,
  0x57, 0x51, 0xF0, 0x57, 0x31, 0x4D, 0x50, 0x2D,
  0x5D, 0xF2, 0xA1, 0x2C, 0x95, 0x95, 0x55, 0x55,
  0x90, 0x90, 0x70, 0x90, 0xCB, 0x55, 0x55, 0x55,
  0x6D, 0x2C, 0xA1, 0xD6, 0xD6, 0xA1, 0x55, 0x94,
  0x70, 0xB9, 0x75, 0x50, 0x3E, 0x49, 0x49, 0x49,
  0x5D, 0x82, 0x49, 0x49, 0x82, 0x49, 0x49, 0x49,
  0x89, 0x69, 0x4F, 0x20, 0x20, 0x20, 0x8A, 0x42,
  0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5,
  0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5,
  0xE5, 0xE5, 0x83, 0x4A, 0x3A, 0x50, 0x62, 0x23,
  0x81, 0xB8, 0xB8, 0xE9, 0x5F, 0x29, 0x33, 0x5D,
  0x5D, 0x73, 0xE8, 0xCB, 0x55, 0x55, 0x55, 0x55,
  0x55, 0x55, 0x95, 0x95, 0x2C, 0x2C, 0x2C, 0x2C,
  0xD6, 0xA1, 0xA1, 0xA1, 0xA1, 0x55, 0x70, 0x70,
  0xCB, 0x68, 0x75, 0x50, 0x82, 0x49, 0x49, 0x49,
  0x5D, 0x49, 0x49, 0x5D, 0x49, 0x49, 0x5D, 0x82,
  0x69, 0x5D, 0x25, 0xF0, 0x20, 0x20, 0x20, 0xE1,
  0x2A, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5,
  0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5,
  0xE5, 0x4B, 0xF4, 0xDF, 0x50, 0x73, 0x76, 0x48,
  0x75, 0xDF, 0x75, 0x62, 0xC4, 0x33, 0x82, 0x49,
  0x5D, 0x5D, 0xA8, 0xF5, 0x55, 0x55, 0x55, 0x55,
  0x2C, 0x2C, 0xA1, 0xA1, 0xA1, 0xA1, 0xA1, 0xA1,
  0x2C, 0x2C, 0x2C, 0x95, 0x95, 0xCB, 0x70, 0x70,
  0x95, 0x83, 0x5F, 0xEA, 0x2D, 0x49, 0x49, 0x49,
  0x49, 0x49, 0x49, 0x49, 0x49, 0x49, 0x49, 0x49,
  0x5D, 0x49, 0x22, 0x5A, 0x79, 0x20, 0x20, 0x20,
  0x80, 0xD2, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5,
  0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5,
  0x65, 0xD0, 0x63, 0x5F, 0x29, 0x2D, 0x2D, 0xEA,
  0x29, 0x29, 0x76, 0x50, 0x2D, 0x82, 0x49, 0x49,
  0x3E, 0x49, 0x5C, 0xB0, 0xBA, 0x95, 0x55, 0x55,
  0x2C, 0xA1, 0xD6, 0xD6, 0xD6, 0xA1, 0x2C, 0x2C,
  0xA1, 0x95, 0x95, 0x55, 0xCB, 0x70, 0x70, 0x55,
  0x2C, 0x83, 0x60, 0x76, 0x5D, 0x49, 0x49, 0x49,
  0x49, 0x49, 0x49, 0x49, 0x49, 0x49, 0x49, 0x49,
  0x49, 0x5D, 0x89, 0xDC, 0x8B, 0x20, 0x20, 0x20,
  0x20, 0x95, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5,
  0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5,
  0xE2, 0x32, 0x85, 0xE3, 0x29, 0x2D, 0x33, 0x2D,
  0x2D, 0x2D, 0x6A, 0x2D, 0x33, 0x5D, 0x49, 0x82,
  0x49, 0x49, 0x82, 0x73, 0x5C, 0x9E, 0x2C, 0x55,
  0x2C, 0xA1, 0xD6, 0xA1, 0x2C, 0x2C, 0x95, 0x95,
  0x2C, 0x95, 0x55, 0xCB, 0x90, 0x90, 0xCB, 0x95,
  0x2C, 0x6D, 0x41, 0x6F, 0x3E, 0x49, 0x49, 0x49,
  0x49, 0x49, 0x49, 0x49, 0x49, 0x49, 0x49, 0x49,
  0x49, 0x82, 0x3E, 0x4E, 0x38, 0xCA, 0x20, 0x20,
  0x20, 0x55, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5,
  0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0x65,
  0x42, 0xA0, 0xD4, 0xE3, 0x29, 0x2D, 0x82, 0x5D,
  0x5D, 0x82, 0x82, 0x49, 0x49, 0x49, 0x49, 0x49,
  0x49, 0x3E, 0x49, 0x49, 0x49, 0x5C, 0x56, 0xD6,
  0xA1, 0xA1, 0xA1, 0x95, 0x55, 0x55, 0x55, 0x95,
  0xA1, 0x55, 0x90, 0x70, 0x94, 0x70, 0x95, 0x2C,
  0x2C, 0xD6, 0xDD, 0x6F, 0x33, 0x49, 0x49, 0x49,
  0x49, 0x49, 0x49, 0x49, 0x49, 0x49, 0x49, 0x49,
  0x5D, 0x5D, 0x82, 0x69, 0x22, 0x62, 0x80, 0x34,
  0x94, 0x6B, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5,
  0xE5, 0xE5, 0xE5, 0xE5, 0x65, 0xE5, 0x65, 0x6B,
  0xD5, 0x88, 0x5B, 0xE3, 0x29, 0x5D, 0x5D, 0x5D,
  0x5D, 0x5D, 0x5D, 0x5D, 0x49, 0x49, 0x49, 0x82,
  0x49, 0x49, 0x89, 0x49, 0x82, 0x49, 0x71, 0xBA,
  0x6D, 0x6D, 0xA1, 0x95, 0x55, 0xCB, 0x55, 0x55,
  0x2C, 0x55, 0x70, 0x70, 0x70, 0x90, 0x95, 0xA1,
  0x2C, 0xA1, 0x41, 0x76, 0x5D, 0x5D, 0x49, 0x49,
  0x49, 0x49, 0x49, 0x49, 0x49, 0x49, 0x49, 0x49,
  0x49, 0x5D, 0x82, 0x5D, 0x89, 0x5E, 0x96, 0x65,
  0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5,
  0xE5, 0xE5, 0xE5, 0xE5, 0x65, 0x65, 0xEC, 0xB1,
  0x20, 0x20, 0xCA, 0x23, 0x29, 0x33, 0x49, 0x5D,
  0x49, 0x82, 0x49, 0x49, 0x49, 0x49, 0x49, 0x82,
  0x49, 0x82, 0x5D, 0x5D, 0x5D, 0x2D, 0x5C, 0x8F,
  0x6D, 0xD6, 0x2C, 0x55, 0x90, 0xCB, 0x95, 0x95,
  0x95, 0x55, 0x70, 0x94, 0x70, 0x55, 0x2C, 0xA1,
  0x95, 0xE8, 0x5F, 0x76, 0x33, 0x5D, 0x49, 0x49,
  0x49, 0x49, 0x49, 0x49, 0x49, 0x49, 0x49, 0x49,
  0x49, 0x49, 0x49, 0x49, 0x3E, 0x9C, 0x2F, 0x68,
  0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5, 0xE5,
  0x65, 0xE5, 0x65, 0xE5, 0x6B, 0x90, 0x80, 0x20,
  0x20, 0x20, 0x4F, 0x81, 0x50, 0x3E, 0x49, 0x49,
  0x49, 0x49, 0x49, 0x49, 0x49, 0x49, 0x49, 0x49,
  0x69, 0x69, 0x49, 0x5D, 0x2D, 0xC4, 0x46, 0xA3,
  0xD6, 0x55, 0x70, 0x94, 0x94, 0x70, 0xCB, 0x55,
  0x55, 0xCB, 0x70, 0x47, 0x70, 0x95, 0xA1, 0xA1,
  0x95, 0xBD, 0x75, 0x2D, 0x33, 0x49, 0x49, 0x49,
  0x49, 0x49, 0x5D, 0x49, 0x49, 0x49, 0x49, 0x49,
  0x49, 0x49, 0x49, 0x49, 0x5D, 0x2D, 0xB5, 0xDB,
  0xD6, 0x65, 0xE5, 0x65, 0xE5, 0xE5, 0x65, 0xE5,
  0x65, 0x65, 0x6B, 0x95, 0x2B, 0x88, 0x20, 0x20,
  0x20, 0x20, 0x8B, 0x81, 0x29, 0x33, 0x49, 0x49,
  0x49, 0x49, 0x49, 0x49, 0x49, 0x49, 0x49, 0x49,
  0x49, 0x3E, 0x3E, 0x5E, 0x41, 0x97, 0x27, 0xD6,
  0x55, 0x94, 0xE6, 0xE6, 0x47, 0x70, 0x55, 0x55,
  0x94, 0x70, 0x94, 0x94, 0x70, 0x55, 0xA1, 0x2C,
  0x6D, 0xC5, 0x39, 0x6A, 0x5D, 0x5D, 0x49, 0x49,
  0x49, 0x49, 0x49, 0x49, 0x49, 0x49, 0x49, 0x49,
  0x49, 0x49, 0x49, 0x49, 0x3E, 0xEA, 0x30, 0x77,
  0xE1, 0xC9, 0x94, 0x2C, 0xD6, 0xD6, 0xA1, 0x55,
  0x47, 0x9F, 0x43, 0x20, 0x20, 0x20, 0x20, 0x20,
  0x20, 0x80, 0x91, 0x81, 0x6A, 0x2D, 0x49, 0x49,
  0x49, 0x5D, 0x5D, 0x49, 0x49, 0x5D, 0x5D, 0x82,
  0xEB, 0x4A, 0x41, 0xC2, 0x8F, 0xF5, 0xA1, 0x55,
  0x94, 0x28, 0xA0, 0x47, 0x70, 0x55, 0x95, 0x95,
  0x47, 0x70, 0x70, 0x94, 0x90, 0x95, 0xA1, 0x2C,
  0xE8, 0xA6, 0x39, 0x76, 0x50, 0x50, 0x2D, 0x2D,
  0x3E, 0x3E, 0x5D, 0x3E, 0x5D, 0x5D, 0x49, 0x82,
  0x49, 0x49, 0x49, 0x82, 0x82, 0x50, 0x75, 0xE0,
  0x57, 0x20, 0x88, 0x88, 0x20, 0x20, 0x88, 0x20,
  0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20,
  0x20, 0x79, 0x91, 0x81, 0x76, 0x33, 0x49, 0x49,
  0x5D, 0x82, 0x49, 0x49, 0x3E, 0x6A, 0xEA, 0x29,
  0xDF, 0x97, 0xBF, 0x6D, 0x6D, 0xD6, 0x55, 0x47,
  0x28, 0x28, 0x47, 0x70, 0x55, 0x95, 0x2C, 0x2C,
  0x95, 0x95, 0x55, 0x90, 0x90, 0x95, 0xA1, 0xA1,
  0xD6, 0x26, 0x45, 0x81, 0x5F, 0x30, 0x48, 0x6F,
  0x6F, 0x29, 0x29, 0x6A, 0x2D, 0x2D, 0x5D, 0x49,
  0x49, 0x49, 0x49, 0x49, 0x2D, 0x76, 0x6E, 0x77,
  0x5B, 0x66, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20,
  0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20,
  0x20, 0x79, 0xA9, 0xB8, 0x39, 0x50, 0x5D, 0x5D,
  0x5D, 0x5D, 0x3E, 0x2D, 0x29, 0x76, 0xCD, 0x37,
  0xB9, 0xA1, 0xA1, 0x6D, 0x6D, 0x2C, 0x94, 0x28,
  0xD5, 0xE6, 0x70, 0x55, 0x95, 0xA1, 0x2C, 0xA1,
  0xBF, 0xA1, 0x95, 0xCB, 0x55, 0x95, 0xA1, 0x2C,
  0x95, 0x83, 0xDE, 0x87, 0xB6, 0xBE, 0x40, 0x6E,
  0x81, 0x81, 0x78, 0x78, 0x39, 0x6F, 0xEA, 0x2D,
  0x2D, 0x33, 0x33, 0x33, 0x76, 0x30, 0x64, 0x54,
  0x5B, 0x66, 0x20, 0x20, 0x66, 0x20, 0x88, 0x20,
  0x20, 0x20, 0x88, 0x20, 0x20, 0x20, 0x20, 0x20,
  0x88, 0x34, 0x8B, 0xF1, 0x23, 0x6F, 0x50, 0x2D,
  0x2D, 0x6A, 0x29, 0x6F, 0x78, 0x84, 0x9B, 0xD2,
  0x2C, 0x2C, 0xD6, 0x6D, 0x6D, 0x2C, 0x47, 0xA0,
  0xE6, 0x70, 0x55, 0x95, 0x2C, 0xA1, 0xA1, 0xA1,
  0xD2, 0x95, 0x55, 0xCB, 0x55, 0x2C, 0xD6, 0xA1,
  0x95, 0x95, 0xA1, 0xD6, 0x6D, 0x6D, 0xBA, 0xF3,
  0x8D, 0x36, 0x74, 0x36, 0xF1, 0xB8, 0x23, 0x78,
  0x62, 0x4A, 0x29, 0x62, 0x23, 0xF1, 0x54, 0x31,
  0x57, 0x2B, 0x90, 0x95, 0x2C, 0x2C, 0x2C, 0x2C,
  0xA1, 0xA1, 0xA1, 0xA1, 0x2C, 0x2C, 0x2C, 0xCB,
  0xE6, 0x7D, 0xCA, 0xB7, 0xB8, 0x75, 0x6F, 0x6F,
  0x76, 0x6F, 0x78, 0x81, 0x53, 0xBD, 0x6D, 0x2C,
  0x95, 0x95, 0xA1, 0x6D, 0xA1, 0x55, 0x94, 0xE6,
  0x70, 0xCB, 0x55, 0x95, 0xA1, 0xD6, 0xD6, 0xA1,
  0xD0, 0x94, 0x94, 0x90, 0x95, 0x2C, 0xD6, 0xA1,
  0x95, 0x55, 0x2C, 0xA1, 0xD6, 0xA1, 0x95, 0x2C,
  0xD6, 0x68, 0xAB, 0x6C, 0xA4, 0x77, 0x77, 0xAD,
  0x40, 0x53, 0x6E, 0x40, 0xB7, 0x54, 0x31, 0xD7,
  0xAC, 0xD6, 0x55, 0x55, 0x95, 0x95, 0x95, 0x55,
  0x95, 0x2C, 0x2C, 0xA1, 0x95, 0x95, 0x2C, 0xA1,
  0x6D, 0xD2, 0x7C, 0x54, 0xAD, 0x40, 0x6E, 0x81,
  0x81, 0x6E, 0x36, 0xDA, 0xE8, 0xD6, 0xD6, 0x2C,
  0x2C, 0x2C, 0xA1, 0xD6, 0x95, 0x90, 0x94, 0x47,
  0x94, 0x94, 0x70, 0x55, 0x2C, 0xD6, 0xA1, 0x95,
  0x95, 0x28, 0x47, 0x90, 0x95, 0x2C, 0xA1, 0x2C,
  0x55, 0x95, 0x2C, 0xA1, 0xA1, 0x2C, 0x2C, 0x2C,
  0x2C, 0xA1, 0x55, 0x70, 0x95, 0x2C, 0xB2, 0xB4,
  0xC3, 0xC3, 0x54, 0x54, 0xA9, 0x31, 0xCA, 0x2A,
  0x95, 0x90, 0x55, 0x95, 0x2C, 0xA1, 0x2C, 0x95,
  0x95, 0x2C, 0x2C, 0x2C, 0x2C, 0x2C, 0x2C, 0xD6,
  0x6D, 0x2A, 0xB2, 0x4F, 0x31, 0x2E, 0xE0, 0xAD,
  0xB7, 0xC8, 0xB4, 0xF5, 0x2C, 0xA1, 0xA1, 0xA1,
  0x95, 0x2C, 0xA1, 0x2C, 0x95, 0x70, 0x94, 0x94,
  0x94, 0x94, 0x70, 0x95, 0xD6, 0xD6, 0x2C, 0x95,
  0x94, 0x28, 0x47, 0xCB, 0x95, 0x2C, 0xA1, 0xA1,
  0x95, 0x55, 0x2C, 0xA1, 0xD6, 0xA1, 0x95, 0x95,
  0x95, 0x2C, 0x55, 0x70, 0x70, 0x70, 0x94, 0x2C,
  0x63, 0xBB, 0xA5, 0xD7, 0xCA, 0xB3, 0x6D, 0x2C,
  0x55, 0x55, 0x95, 0x2C, 0x2C, 0x2C, 0x95, 0x95,
  0x95, 0x2C, 0x2C, 0x2C, 0x2C, 0x2C, 0x2C, 0xA1,
  0xD6, 0x2C, 0x70, 0x95, 0xAC, 0xC0, 0xDB, 0xEF,
  0xEF, 0xA2, 0xE8, 0x95, 0x95, 0xA1, 0xD6, 0xA1,
  0x95, 0x55, 0x2C, 0x95, 0x55, 0x70, 0x70, 0x70,
  0x94, 0x70, 0x55, 0xD6, 0x6D, 0x6D, 0x95, 0x55,
  0x70, 0x47, 0x70, 0x95, 0x2C, 0x2C, 0x2C, 0xA1,
  0x2C, 0x95, 0x2C, 0xA1, 0xD6, 0xA1, 0x95, 0x55,
  0x55, 0x95, 0x55, 0x55, 0x55, 0x55, 0x55, 0x95,
  0xA1, 0xF5, 0xBF, 0xBF, 0xA1, 0x95, 0x95, 0x95,
  0x95, 0x55, 0x2C, 0x2C, 0x95, 0x55, 0x55, 0x95,
  0x95, 0x95, 0xA1, 0xA1, 0xA1, 0xA1, 0x2C, 0xA1,
  0x2C, 0x55, 0x70, 0x94, 0x90, 0x2C, 0x6D, 0x6D,
  0x6D, 0xA1, 0x2C, 0x95, 0x2C, 0xA1, 0xD6, 0xA1,
  0x2C, 0x55, 0x55, 0x95, 0x55, 0x55, 0x55, 0x55,
  0x55, 0x95, 0xD6, 0x6D, 0xBF, 0xD6, 0x55, 0xCB,
  0x55, 0x55, 0x55, 0x2C, 0x2C, 0x2C, 0x2C, 0xA1,
  0xA1, 0x95, 0x2C, 0xA1, 0xA1, 0xA1, 0x2C, 0x95,
  0x55, 0x95, 0x95, 0x2C, 0x2C, 0x2C, 0x2C, 0xA1,
  0x6D, 0xBF, 0x6D, 0x2C, 0x55, 0x55, 0x95, 0x95,
  0xCB, 0xCB, 0x55, 0x55, 0xCB, 0x55, 0x55, 0x95,
  0x95, 0x2C, 0x2C, 0xA1, 0xA1, 0xA1, 0x2C, 0x2C,
  0xA1, 0x95, 0xCB, 0xCB, 0x95, 0x95, 0x2C, 0x2C,
  0x2C, 0xA1, 0x2C, 0x2C, 0x2C, 0xA1, 0xA1, 0x2C,
  0x2C, 0x95, 0x55, 0x95, 0x95, 0x2C, 0x2C, 0x2C,
  0x2C, 0xA1, 0x6D, 0xBF, 0x6D, 0xA1, 0x55, 0x55,
  0x95, 0x95, 0x95, 0x95, 0x2C, 0x2C, 0x2C, 0x2C,
  0x2C, 0x95, 0x95, 0x95, 0x2C, 0x2C, 0x2C, 0x95,
  0x55, 0x95, 0x2C, 0x2C, 0xA1, 0xA1, 0xD6, 0xD6,
  0x6D, 0x6D, 0xA1, 0x95, 0xCB, 0x55, 0x95, 0x55,
  0x90, 0x70, 0xCB, 0xCB, 0x90, 0xCB, 0x95, 0x95,
  0x2C, 0x2C, 0xA1, 0xD6, 0xA1, 0xA1, 0xA1, 0xA1,
  0xA1, 0xA1, 0x2C, 0x95, 0x95, 0x2C, 0x2C, 0x2C,
  0x2C, 0xA1, 0x2C, 0x95, 0x95, 0x95, 0x2C, 0x2C,
  0x2C, 0x95, 0x55, 0x55, 0x2C, 0x2C, 0xA1, 0xA1,
  0xD6, 0xD6, 0x6D, 0x6D, 0xA1, 0x55, 0xCB, 0x55
};

#endif

#ifdef INCLUDE_LINUX_LOGOBW

unsigned char linux_logo_bw[] __initdata = {
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x80, 0x00, 0x3F,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x1F,
  0xFE, 0x1F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFE, 0x3F, 0xFF, 0x0F, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFE, 0x7F, 0xFF, 0xC7, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFC, 0xFF, 0xFF, 0xC3,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFC, 0xFF,
  0xFB, 0xE3, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFD, 0xFF, 0xFF, 0xE1, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xF9, 0xFF, 0xFF, 0xF1, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xF9, 0xFF, 0xFF, 0xF1,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF9, 0xFF,
  0xFF, 0xF8, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xF9, 0xFF, 0xFF, 0xF8, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xF9, 0xCF, 0xC3, 0xF8, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xF9, 0x87, 0x81, 0xF9,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF9, 0xA7,
  0x99, 0xF9, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xF9, 0xF3, 0xBC, 0xF9, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xF9, 0xE3, 0xBC, 0xF9, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xF9, 0xB0, 0x3C, 0xF9,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF9, 0xB0,
  0x19, 0xF0, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xF9, 0xC0, 0x03, 0xF0, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xF9, 0x80, 0x01, 0xF8, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xF9, 0x80, 0x01, 0xF8,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF9, 0x80,
  0x01, 0xF8, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xF9, 0xC0, 0x21, 0xD8, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xF9, 0xB1, 0x80, 0xEC, 0xC0, 0x1F,
  0xFF, 0xFF, 0xFF, 0xFF, 0xF1, 0x90, 0x00, 0xE4,
  0x00, 0x01, 0xFF, 0xFF, 0xFF, 0xFF, 0xF1, 0x8C,
  0xC0, 0x7C, 0x04, 0x81, 0xFF, 0xFF, 0xFF, 0xFF,
  0xE3, 0x80, 0x00, 0x7C, 0x40, 0x11, 0xFF, 0xFF,
  0xFF, 0xFF, 0xE3, 0x80, 0x00, 0x7F, 0xD2, 0x29,
  0xFF, 0xFF, 0xFF, 0xFF, 0x87, 0x00, 0x00, 0x3F,
  0x80, 0x19, 0xFF, 0xFF, 0xFF, 0xFF, 0x0E, 0x00,
  0x00, 0x3F, 0x80, 0x19, 0xFF, 0xFF, 0xFF, 0xFF,
  0x1E, 0x00, 0x00, 0x1F, 0x80, 0x19, 0xFF, 0xFF,
  0xFF, 0xFE, 0x1C, 0x00, 0x00, 0x1E, 0x80, 0x19,
  0xFF, 0xFF, 0xFF, 0xFE, 0x3C, 0x00, 0x00, 0x1E,
  0x80, 0x11, 0xFF, 0xFF, 0xFF, 0xFC, 0x7C, 0x00,
  0x00, 0x0F, 0x80, 0x11, 0xFF, 0xFF, 0xFF, 0xFC,
  0xF8, 0x00, 0x00, 0x0E, 0x80, 0x11, 0xFF, 0xFF,
  0xFF, 0xFC, 0xF8, 0x00, 0x00, 0x06, 0x00, 0x11,
  0xFF, 0xFF, 0xFF, 0xF8, 0xF8, 0x00, 0x00, 0x06,
  0x00, 0x01, 0xFF, 0xFF, 0xFF, 0xF9, 0xF0, 0x00,
  0x00, 0x02, 0x00, 0x09, 0xFF, 0xFF, 0xFF, 0xF1,
  0xF0, 0x00, 0x00, 0x02, 0x80, 0x10, 0xFF, 0xFF,
  0xFF, 0xF1, 0xE0, 0x00, 0x00, 0x00, 0x97, 0x10,
  0xFF, 0xFF, 0xFF, 0xE3, 0xE0, 0x00, 0x00, 0x00,
  0xDF, 0xF0, 0xFF, 0xFF, 0xFF, 0xE3, 0xC0, 0x00,
  0x00, 0x00, 0xFF, 0xF8, 0xFF, 0xFF, 0xFF, 0xC7,
  0xC0, 0x00, 0x00, 0x01, 0xFF, 0xF8, 0xFF, 0xFF,
  0xFF, 0xC7, 0x80, 0x00, 0x00, 0x01, 0xFF, 0xF8,
  0xFF, 0xFF, 0xFF, 0x8F, 0x80, 0x00, 0x00, 0x01,
  0xFF, 0xF8, 0xFF, 0xFF, 0xFF, 0x8F, 0x80, 0x00,
  0x00, 0x01, 0xFF, 0xF8, 0xFF, 0xFF, 0xFF, 0x9F,
  0x80, 0x00, 0x00, 0x01, 0xFF, 0xF8, 0xFF, 0xFF,
  0xFF, 0x9F, 0x80, 0x00, 0x00, 0x01, 0x80, 0x18,
  0xFF, 0xFF, 0xFF, 0x9E, 0x80, 0x00, 0x00, 0x03,
  0xA8, 0x11, 0xFF, 0xFF, 0xFF, 0x9F, 0x80, 0x00,
  0x00, 0x02, 0x00, 0x01, 0xFF, 0xFF, 0xFF, 0x99,
  0x80, 0x00, 0x00, 0x00, 0x00, 0x09, 0xFF, 0xFF,
  0xFF, 0x00, 0x80, 0x00, 0x00, 0x01, 0xC0, 0x01,
  0xFF, 0xFF, 0xFE, 0x20, 0x60, 0x00, 0x00, 0x00,
  0xFF, 0xC3, 0xFF, 0xFF, 0xF8, 0x00, 0x30, 0x00,
  0x00, 0x00, 0xFF, 0x0F, 0xFF, 0xFF, 0xC0, 0x40,
  0x38, 0x00, 0x00, 0x00, 0xFE, 0x47, 0xFF, 0xFF,
  0x81, 0x00, 0x1C, 0x00, 0x00, 0x00, 0xFC, 0x23,
  0xFF, 0xFF, 0x90, 0x00, 0x1E, 0x00, 0x00, 0x00,
  0x78, 0x11, 0xFF, 0xFF, 0x80, 0x00, 0x0F, 0x80,
  0x00, 0x00, 0x00, 0x01, 0xFF, 0xFF, 0x80, 0x00,
  0x07, 0xC0, 0x00, 0x00, 0x00, 0x08, 0xFF, 0xFF,
  0xC0, 0x00, 0x07, 0xC0, 0x00, 0x00, 0x00, 0x04,
  0x7F, 0xFF, 0x80, 0x00, 0x03, 0xC0, 0x00, 0x10,
  0x00, 0x00, 0x1F, 0xFF, 0x80, 0x00, 0x01, 0x80,
  0x00, 0x30, 0x00, 0x00, 0x0F, 0xFF, 0x80, 0x00,
  0x00, 0x00, 0x00, 0x70, 0x00, 0x01, 0x4F, 0xFF,
  0x80, 0x00, 0x00, 0x00, 0x00, 0xF0, 0x00, 0x00,
  0x0F, 0xFF, 0xC0, 0x00, 0x00, 0x80, 0x03, 0xF0,
  0x00, 0x00, 0x8F, 0xFF, 0x80, 0x00, 0x00, 0x40,
  0x0F, 0xF0, 0x00, 0x04, 0x1F, 0xFF, 0x80, 0x00,
  0x00, 0x7F, 0xFF, 0xF0, 0x00, 0x10, 0x1F, 0xFF,
  0xC0, 0x00, 0x00, 0x7F, 0xFF, 0xF0, 0x00, 0x40,
  0xFF, 0xFF, 0x98, 0x00, 0x00, 0xFF, 0xFF, 0xF0,
  0x00, 0x83, 0xFF, 0xFF, 0x81, 0xE0, 0x01, 0xFF,
  0xFF, 0xF8, 0x02, 0x07, 0xFF, 0xFF, 0x80, 0x3F,
  0x07, 0xE0, 0x00, 0x1C, 0x0C, 0x1F, 0xFF, 0xFF,
  0xF8, 0x03, 0xFF, 0x80, 0x00, 0x1F, 0x78, 0x1F,
  0xFF, 0xFF, 0xFF, 0x80, 0x7F, 0x00, 0x07, 0x0F,
  0xF0, 0x7F, 0xFF, 0xFF, 0xFF, 0xFE, 0x0C, 0x07,
  0xFF, 0x83, 0xC0, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0x00, 0x1F, 0xFF, 0xC0, 0x03, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 0x07, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
};

#endif

#ifdef INCLUDE_LINUX_LOGO16

unsigned char linux_logo16_red[] __initdata = {
    0x00, 0x90, 0xb0, 0x9c, 0xf7, 0x35, 0x83, 0xa5,
    0x65, 0x8f, 0x98, 0xc9, 0xdb, 0xe1, 0xe7, 0xf8
};

unsigned char linux_logo16_green[] __initdata = {
    0x00, 0x90, 0xb0, 0x9c, 0xf7, 0x2e, 0x83, 0xa5,
    0x65, 0x6e, 0x98, 0x89, 0xbf, 0xac, 0xda, 0xf8
};

unsigned char linux_logo16_blue[] __initdata = {
    0x00, 0x90, 0xaf, 0x9c, 0xf7, 0x2b, 0x82, 0xa5,
    0x65, 0x41, 0x97, 0x1e, 0x60, 0x29, 0xa5, 0xf8
};

unsigned char linux_logo16[] __initdata = {
    0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa,
    0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xa1, 0x11, 0x11,
    0x61, 0x16, 0x66, 0x66, 0x11, 0x11, 0x11, 0x11,
    0x11, 0x11, 0x1a, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa,
    0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa,
    0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa,
    0xaa, 0xaa, 0xaa, 0xaa, 0x33, 0xa8, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x87, 0x77, 0x77, 0x77, 0x77,
    0x77, 0x77, 0x73, 0x33, 0x33, 0x3a, 0xaa, 0xaa,
    0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa,
    0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa,
    0xaa, 0xaa, 0xa3, 0x33, 0x33, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x77, 0x77, 0x77, 0x77,
    0x77, 0x27, 0x77, 0x77, 0x77, 0x33, 0x3a, 0xaa,
    0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa,
    0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa,
    0xaa, 0xa3, 0x33, 0x33, 0x30, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x55, 0x50, 0x08, 0x33, 0x77, 0x77,
    0x77, 0x72, 0x72, 0x27, 0x77, 0x77, 0x33, 0x33,
    0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa,
    0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa,
    0xa3, 0x33, 0x33, 0x77, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x58, 0x85, 0x00, 0x11, 0x11, 0xaa,
    0xa3, 0x37, 0x77, 0x72, 0x22, 0x22, 0x77, 0x73,
    0x33, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa,
    0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xa3,
    0x33, 0x37, 0x77, 0x33, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x56, 0x85, 0x00, 0x06, 0x66, 0x11,
    0x11, 0x1a, 0xa3, 0x37, 0x77, 0x72, 0x22, 0x77,
    0x73, 0x33, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa,
    0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0x33,
    0x33, 0x33, 0x33, 0x30, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x55, 0x00, 0x00, 0x06, 0x66, 0x66,
    0x66, 0x66, 0x11, 0x1a, 0xa3, 0x77, 0x72, 0x22,
    0x77, 0x73, 0x3a, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa,
    0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0x33, 0x33,
    0x33, 0x33, 0x33, 0xa0, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x11, 0x11,
    0x66, 0x66, 0x66, 0x66, 0x11, 0xa3, 0x77, 0x22,
    0x22, 0x77, 0x33, 0x33, 0xaa, 0xaa, 0xaa, 0xaa,
    0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0x33, 0x33, 0x33,
    0x33, 0x3a, 0xa1, 0x10, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x77, 0x33,
    0xaa, 0x11, 0x16, 0x66, 0x66, 0x61, 0x1a, 0x37,
    0x22, 0x22, 0x77, 0x33, 0x3a, 0xaa, 0xaa, 0xaa,
    0xaa, 0xaa, 0xaa, 0xaa, 0xa3, 0x33, 0x33, 0x33,
    0x3a, 0xa1, 0x11, 0x10, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x05, 0x00, 0x00, 0x00, 0x02, 0x22,
    0x22, 0x77, 0x3a, 0x11, 0x66, 0x66, 0x66, 0x1a,
    0x37, 0x22, 0x22, 0x77, 0x33, 0x3a, 0xaa, 0xaa,
    0xaa, 0xaa, 0xaa, 0xaa, 0x33, 0x33, 0x33, 0x3a,
    0xa1, 0x11, 0x11, 0x10, 0x00, 0x00, 0x50, 0x00,
    0x00, 0x05, 0x80, 0x50, 0x00, 0x00, 0x07, 0x72,
    0x22, 0x22, 0x22, 0x73, 0xa1, 0x66, 0x66, 0x61,
    0x1a, 0x77, 0x22, 0x27, 0x73, 0x33, 0xaa, 0xaa,
    0xaa, 0xaa, 0xaa, 0xaa, 0x33, 0x33, 0x3a, 0xaa,
    0x11, 0x11, 0x1a, 0xa0, 0x08, 0x71, 0x05, 0x00,
    0x00, 0x12, 0x22, 0x50, 0x00, 0x00, 0x07, 0x77,
    0x77, 0x72, 0x22, 0x22, 0x27, 0x31, 0x16, 0x66,
    0x61, 0x13, 0x77, 0x22, 0x77, 0x33, 0x3a, 0xaa,
    0xaa, 0xaa, 0xaa, 0xa3, 0x33, 0x33, 0xaa, 0xa1,
    0x11, 0x1a, 0x33, 0x70, 0x07, 0x2e, 0x70, 0x00,
    0x01, 0x44, 0x42, 0x60, 0x00, 0x00, 0x02, 0x22,
    0x22, 0x22, 0x22, 0x22, 0x22, 0x27, 0x31, 0x66,
    0x66, 0x61, 0xa3, 0x72, 0x22, 0x77, 0x33, 0xaa,
    0xaa, 0xaa, 0xa3, 0x33, 0x33, 0xaa, 0xaa, 0x11,
    0x1a, 0x33, 0x77, 0x30, 0x04, 0x82, 0x40, 0x00,
    0x54, 0x48, 0x54, 0x40, 0x00, 0x00, 0x01, 0xaa,
    0x32, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x31,
    0x66, 0x66, 0x11, 0x37, 0x22, 0x27, 0x73, 0x3a,
    0xaa, 0xaa, 0xa3, 0x33, 0x3a, 0xaa, 0xaa, 0xaa,
    0xa3, 0x77, 0xaa, 0x10, 0x50, 0x08, 0x46, 0x05,
    0x54, 0x80, 0x50, 0x42, 0x00, 0x00, 0x08, 0x66,
    0x66, 0x1a, 0x32, 0x22, 0x22, 0x22, 0x22, 0x27,
    0x31, 0x66, 0x66, 0x13, 0x72, 0x22, 0x77, 0x33,
    0xaa, 0xaa, 0xaa, 0x33, 0xaa, 0xa1, 0xaa, 0xa3,
    0x37, 0xa1, 0x1a, 0x30, 0x50, 0x06, 0x26, 0x00,
    0x54, 0x00, 0x00, 0x44, 0x00, 0x00, 0x08, 0xe2,
    0xee, 0xee, 0xee, 0xee, 0xee, 0xee, 0x22, 0x22,
    0x27, 0xa6, 0x66, 0x61, 0xa7, 0x72, 0x27, 0x73,
    0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0x33,
    0x31, 0x11, 0x37, 0x70, 0x02, 0x00, 0xab, 0xbb,
    0xb6, 0x00, 0x00, 0xf4, 0x00, 0x00, 0xee, 0xee,
    0xee, 0xee, 0xee, 0xee, 0xee, 0xee, 0xee, 0x22,
    0x22, 0x23, 0x16, 0x66, 0x1a, 0x37, 0x22, 0x77,
    0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xa3, 0x3a,
    0x11, 0xa7, 0x33, 0x10, 0x04, 0x09, 0xbd, 0xdd,
    0xbd, 0xd0, 0x04, 0x45, 0x00, 0x0e, 0xee, 0xee,
    0xee, 0xee, 0xee, 0xee, 0xee, 0xee, 0xee, 0x22,
    0x22, 0x22, 0x71, 0x66, 0x66, 0x13, 0x72, 0x27,
    0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0x33, 0x11,
    0xa3, 0x73, 0xa1, 0x60, 0x08, 0xbd, 0xdd, 0xdd,
    0xdd, 0xdd, 0xdb, 0x90, 0x00, 0x02, 0xec, 0xee,
    0xee, 0xee, 0xee, 0xee, 0xee, 0xee, 0xce, 0x22,
    0x22, 0x22, 0x27, 0xa6, 0x66, 0x61, 0x37, 0x27,
    0x1a, 0xaa, 0xaa, 0xaa, 0xaa, 0xa3, 0xa1, 0x1a,
    0x33, 0xa1, 0x16, 0x60, 0x0b, 0xbd, 0xdd, 0xdd,
    0xcd, 0xdd, 0xdd, 0xd9, 0x00, 0x00, 0xec, 0xcc,
    0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xce, 0xa2,
    0x22, 0x22, 0x22, 0x7a, 0x66, 0x66, 0x13, 0x77,
    0x1a, 0xaa, 0xaa, 0xaa, 0xaa, 0x3a, 0x11, 0x33,
    0xaa, 0x11, 0x66, 0x60, 0x9b, 0xdd, 0xdd, 0xdd,
    0xcd, 0xdd, 0xdb, 0xb9, 0x00, 0x00, 0xec, 0xcc,
    0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xee, 0x61,
    0x72, 0x22, 0x22, 0x22, 0xa1, 0x66, 0x61, 0x37,
    0x1a, 0xaa, 0xaa, 0xaa, 0xa3, 0xa1, 0x13, 0x3a,
    0x11, 0x11, 0x11, 0x10, 0x5b, 0xdd, 0xdd, 0xdc,
    0xdd, 0xdd, 0xbd, 0xd9, 0x00, 0x00, 0xec, 0xcc,
    0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xee, 0x86,
    0x17, 0x22, 0x22, 0x22, 0x23, 0x16, 0x66, 0xaa,
    0xaa, 0xa3, 0x3a, 0xaa, 0xaa, 0x1a, 0x3a, 0xa1,
    0x11, 0x11, 0x1a, 0x70, 0x05, 0xbd, 0xdd, 0xdd,
    0xdb, 0x5b, 0xdd, 0xb0, 0x00, 0x60, 0x2e, 0xcc,
    0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xe6, 0x88,
    0x66, 0x32, 0x22, 0x22, 0x22, 0x36, 0x66, 0x11,
    0x33, 0x33, 0x3a, 0xaa, 0x11, 0xaa, 0xaa, 0xa1,
    0x11, 0x1a, 0x3a, 0x60, 0x02, 0x99, 0xbb, 0xb9,
    0x9b, 0xbb, 0xbc, 0x22, 0x00, 0x86, 0x5e, 0xcc,
    0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xe1, 0x68,
    0x86, 0x63, 0x22, 0x22, 0x22, 0x2a, 0x66, 0x66,
    0x33, 0x33, 0xaa, 0xaa, 0x1a, 0xaa, 0xaa, 0x11,
    0x1a, 0xa7, 0x68, 0x80, 0x02, 0x2b, 0xbd, 0xbb,
    0xbb, 0xb9, 0x22, 0x22, 0x00, 0x06, 0x6e, 0xcc,
    0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xc7, 0xa6,
    0x88, 0x86, 0x32, 0x22, 0x22, 0x27, 0xa6, 0x66,
    0x33, 0x3a, 0xaa, 0xa1, 0xaa, 0xaa, 0xa1, 0x11,
    0xa3, 0xa6, 0x88, 0x80, 0x02, 0x22, 0x9b, 0xbb,
    0xbb, 0x22, 0x24, 0xf4, 0x60, 0x00, 0x0c, 0xcc,
    0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xc2, 0x21,
    0x68, 0x88, 0x63, 0x22, 0x22, 0x22, 0x71, 0x66,
    0x33, 0x3a, 0x11, 0x11, 0xaa, 0xaa, 0x11, 0xaa,
    0x71, 0x88, 0x88, 0x00, 0x02, 0xe2, 0x26, 0x99,
    0x22, 0x22, 0x4f, 0xf4, 0x40, 0x00, 0x0c, 0xcc,
    0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0x22, 0x22,
    0x16, 0x88, 0x86, 0xa2, 0x22, 0x22, 0x27, 0x11,
    0x33, 0xa1, 0x11, 0x11, 0xaa, 0x31, 0x1a, 0xa3,
    0x68, 0x88, 0x81, 0x00, 0x54, 0x42, 0x22, 0x22,
    0x22, 0x44, 0xff, 0xff, 0x48, 0x00, 0x00, 0x99,
    0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0x99, 0x22, 0x22,
    0x21, 0x88, 0x88, 0x6a, 0x22, 0x22, 0x22, 0x31,
    0x3a, 0xa1, 0x11, 0x1a, 0xa3, 0x11, 0x33, 0x36,
    0x88, 0x86, 0x30, 0x00, 0x4f, 0x44, 0x22, 0x22,
    0x24, 0xff, 0xff, 0xff, 0x44, 0x00, 0x00, 0x99,
    0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0x95, 0x22, 0x72,
    0x22, 0x18, 0x88, 0x86, 0x32, 0x22, 0x22, 0x27,
    0xaa, 0x11, 0x11, 0x1a, 0x31, 0x13, 0x33, 0x68,
    0x88, 0x6a, 0x00, 0x02, 0x4f, 0x4f, 0x42, 0x24,
    0x4f, 0xff, 0xff, 0xff, 0xf4, 0x50, 0x00, 0x99,
    0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0x99, 0x22, 0x73,
    0x72, 0x26, 0x88, 0x88, 0x63, 0x22, 0x22, 0x22,
    0x11, 0x11, 0x11, 0xa3, 0xa1, 0x73, 0xa6, 0x88,
    0x81, 0xa5, 0x00, 0x04, 0x4f, 0x4f, 0x44, 0x4f,
    0xff, 0xff, 0xff, 0xff, 0xf4, 0x40, 0x00, 0x99,
    0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0x99, 0x12, 0x27,
    0xaa, 0x22, 0x68, 0x55, 0x86, 0x72, 0x22, 0x22,
    0x11, 0x11, 0x1a, 0x33, 0x13, 0x3a, 0x18, 0x88,
    0x1a, 0x10, 0x00, 0x44, 0x4f, 0x4f, 0xff, 0x4f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x00, 0x99,
    0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0x99, 0x61, 0x22,
    0x3a, 0xa2, 0x26, 0x85, 0x58, 0x67, 0x22, 0x22,
    0x61, 0x61, 0x1a, 0x7a, 0x37, 0x31, 0x88, 0x81,
    0x11, 0x00, 0x05, 0xe4, 0x44, 0xff, 0xff, 0xff,
    0x4f, 0xf4, 0x44, 0xff, 0xff, 0xf5, 0x00, 0x99,
    0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0x99, 0x88, 0x12,
    0x2a, 0xaa, 0x72, 0x68, 0x55, 0x81, 0x22, 0x22,
    0x66, 0x61, 0xa3, 0x33, 0x73, 0x16, 0x88, 0x11,
    0x10, 0x00, 0x08, 0x74, 0x44, 0x4f, 0x44, 0x44,
    0xf4, 0xf4, 0x44, 0x44, 0xe2, 0x44, 0x00, 0x99,
    0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0x99, 0x88, 0x81,
    0x22, 0xaa, 0xa7, 0x26, 0x85, 0x88, 0x12, 0x22,
    0x66, 0x61, 0x37, 0xa7, 0x3a, 0x66, 0x66, 0x11,
    0x80, 0x00, 0x0a, 0x72, 0x44, 0x4f, 0x44, 0x4f,
    0xff, 0x44, 0x44, 0x22, 0x22, 0x24, 0x00, 0x99,
    0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0x99, 0x85, 0x88,
    0x12, 0x2a, 0xaa, 0x22, 0x68, 0x58, 0x63, 0x22,
    0x66, 0x1a, 0x73, 0x77, 0x31, 0x66, 0x61, 0x11,
    0x00, 0x00, 0x07, 0x44, 0xff, 0x4f, 0xf4, 0x4f,
    0xff, 0x4f, 0x44, 0xf4, 0x42, 0x22, 0x40, 0x9b,
    0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xb9, 0x85, 0x55,
    0x81, 0x27, 0xaa, 0xa2, 0x78, 0x88, 0x86, 0x72,
    0x66, 0x13, 0x77, 0x73, 0x11, 0x66, 0x61, 0x76,
    0x00, 0x50, 0x84, 0xf4, 0xff, 0x4f, 0xf4, 0xff,
    0xff, 0x4f, 0x44, 0xff, 0x4f, 0x42, 0x40, 0x9b,
    0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xb9, 0x68, 0x55,
    0x58, 0x12, 0x3a, 0xaa, 0x23, 0x88, 0x88, 0xa7,
    0x66, 0xa7, 0x77, 0x7a, 0x16, 0x66, 0x1a, 0x15,
    0x05, 0x00, 0x4f, 0xf4, 0xff, 0x4f, 0xf4, 0xff,
    0xff, 0x4f, 0x44, 0xff, 0x4f, 0x44, 0x24, 0x9b,
    0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xb9, 0x26, 0x55,
    0x55, 0x81, 0x23, 0xaa, 0x32, 0x18, 0x88, 0x6a,
    0x61, 0x37, 0x77, 0x31, 0x66, 0x66, 0x17, 0x60,
    0x05, 0x08, 0x4f, 0xf4, 0xff, 0x4f, 0xf4, 0xff,
    0xff, 0x4f, 0x44, 0xff, 0x4f, 0x4f, 0x4e, 0x99,
    0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0x99, 0xa2, 0x65,
    0x55, 0x58, 0xa2, 0x7a, 0xa2, 0x26, 0x88, 0x61,
    0x61, 0x32, 0x27, 0xa1, 0x66, 0x61, 0x31, 0x60,
    0x00, 0x04, 0x4f, 0xf4, 0xff, 0x44, 0x44, 0xff,
    0xff, 0x4f, 0x44, 0xff, 0x4f, 0x44, 0xf4, 0x99,
    0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0x9b, 0xaa, 0x26,
    0x55, 0x55, 0x87, 0x27, 0x33, 0x27, 0x68, 0x61,
    0x1a, 0x72, 0x27, 0xa6, 0x66, 0x6a, 0x71, 0x00,
    0x80, 0x84, 0xff, 0xf4, 0xff, 0x44, 0x44, 0xff,
    0xff, 0x4f, 0x44, 0xff, 0x4f, 0x44, 0xf4, 0x99,
    0x9b, 0x9b, 0x99, 0xb9, 0xb9, 0x99, 0xaa, 0xa2,
    0x85, 0x55, 0x56, 0x22, 0x27, 0x22, 0x36, 0x66,
    0x13, 0x22, 0x23, 0x16, 0x86, 0x63, 0x73, 0x00,
    0x00, 0x44, 0xf4, 0xf4, 0xff, 0x44, 0x44, 0xff,
    0xff, 0x4f, 0x44, 0xff, 0x4f, 0x4f, 0x4f, 0x99,
    0x9b, 0x99, 0x99, 0x99, 0xb9, 0x99, 0xaa, 0xaa,
    0x28, 0x55, 0x58, 0x12, 0x22, 0x22, 0x21, 0x11,
    0xa3, 0x27, 0x7a, 0x66, 0x86, 0x17, 0x75, 0x05,
    0x05, 0xff, 0xf4, 0xf4, 0xff, 0x44, 0x44, 0xff,
    0xff, 0x4f, 0x44, 0x4f, 0x4f, 0x44, 0x4f, 0x99,
    0x99, 0x99, 0x99, 0x99, 0x99, 0x99, 0x3a, 0xaa,
    0xa2, 0x85, 0x58, 0x67, 0x72, 0x22, 0x27, 0xa1,
    0x37, 0x27, 0x7a, 0x68, 0x86, 0xa2, 0x70, 0x00,
    0x02, 0xff, 0xf4, 0xf4, 0xff, 0x44, 0x44, 0x4f,
    0xff, 0x4f, 0x44, 0xf4, 0xf4, 0xf4, 0xf4, 0x99,
    0x99, 0x99, 0x99, 0x99, 0x99, 0x99, 0x23, 0xaa,
    0xa7, 0x78, 0x88, 0x81, 0x77, 0x22, 0x27, 0x3a,
    0x72, 0x73, 0x71, 0x68, 0x66, 0x32, 0x50, 0x00,
    0x04, 0x4f, 0xf4, 0xf4, 0xff, 0x44, 0x44, 0x4f,
    0xff, 0x4f, 0x44, 0xf4, 0xf4, 0xf4, 0x44, 0x95,
    0x99, 0x99, 0x99, 0x99, 0x99, 0x55, 0x12, 0x3a,
    0xaa, 0x21, 0x88, 0x81, 0x77, 0x27, 0x73, 0x73,
    0x72, 0x33, 0x36, 0x86, 0x61, 0x72, 0x00, 0x00,
    0x04, 0x44, 0xf4, 0xf4, 0xf4, 0x44, 0x44, 0x4f,
    0xff, 0x4f, 0x44, 0xff, 0x4f, 0x4f, 0x44, 0x55,
    0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x8a, 0x27,
    0xaa, 0x77, 0x68, 0x61, 0x23, 0x71, 0x11, 0x3a,
    0x27, 0xa3, 0x36, 0x86, 0x61, 0x20, 0x00, 0x00,
    0x04, 0xf4, 0xf4, 0xf4, 0xf4, 0x44, 0x44, 0x4f,
    0xff, 0x4f, 0x44, 0xff, 0x4f, 0x4f, 0x41, 0x59,
    0x99, 0x99, 0x99, 0x99, 0x99, 0x95, 0x58, 0x77,
    0x27, 0x32, 0x36, 0x63, 0x23, 0x71, 0x66, 0x11,
    0x27, 0x13, 0xa6, 0x86, 0x6a, 0x20, 0x00, 0x50,
    0x04, 0x4f, 0x4f, 0x4f, 0x4f, 0x44, 0x44, 0x4f,
    0xff, 0x4f, 0x44, 0xff, 0x4f, 0x4f, 0x41, 0x99,
    0x9b, 0xbb, 0xbb, 0xbb, 0xb9, 0x99, 0x68, 0x13,
    0x32, 0x22, 0x73, 0xa7, 0x2a, 0x31, 0x88, 0x66,
    0x7a, 0x13, 0x18, 0x66, 0x63, 0x20, 0x00, 0x06,
    0x0f, 0x4f, 0x4f, 0x4f, 0x4f, 0x44, 0x44, 0x4f,
    0xff, 0x4f, 0x44, 0xff, 0x4f, 0x4f, 0x49, 0x95,
    0xa9, 0xa9, 0x99, 0x97, 0x92, 0x99, 0x65, 0x6a,
    0x17, 0x22, 0x23, 0x72, 0x27, 0xaa, 0x88, 0x88,
    0xa1, 0x17, 0x68, 0x66, 0x67, 0x70, 0x00, 0x05,
    0x0f, 0x4f, 0x4f, 0x4f, 0x4f, 0x44, 0x44, 0x4f,
    0xff, 0x4f, 0x44, 0xff, 0xf4, 0xf4, 0x49, 0x9c,
    0x2e, 0xee, 0xee, 0xee, 0xee, 0xa9, 0x65, 0x8a,
    0x1a, 0xaa, 0x37, 0x72, 0x27, 0x37, 0x88, 0x88,
    0x11, 0x17, 0x68, 0x66, 0x67, 0x10, 0x9d, 0xd0,
    0x84, 0x44, 0xff, 0x4f, 0x4f, 0x44, 0xf4, 0x4f,
    0xff, 0x4f, 0x44, 0xff, 0xf4, 0xf4, 0x4f, 0x69,
    0xcc, 0xee, 0xee, 0xee, 0xec, 0x99, 0x88, 0x63,
    0x61, 0x68, 0x61, 0x72, 0x22, 0x7a, 0x68, 0x88,
    0x11, 0x17, 0x88, 0x66, 0x12, 0x1b, 0xdd, 0xdd,
    0x02, 0x44, 0x4f, 0x4f, 0x4f, 0x44, 0x44, 0x4f,
    0xff, 0x4f, 0x44, 0xff, 0xff, 0x4f, 0x4c, 0xc5,
    0x0c, 0xc1, 0x11, 0x1c, 0xc0, 0x26, 0x66, 0x17,
    0x66, 0x88, 0x88, 0x12, 0x22, 0x23, 0xa8, 0x88,
    0x11, 0x13, 0x88, 0x66, 0x17, 0xbb, 0xdd, 0xdd,
    0xd0, 0x8f, 0xff, 0xf4, 0xf4, 0x44, 0xf4, 0x4f,
    0xff, 0x4f, 0x44, 0xf4, 0x4f, 0x44, 0xdd, 0xdd,
    0x00, 0x00, 0x00, 0x05, 0x9d, 0x21, 0x66, 0x27,
    0xa6, 0x65, 0x58, 0x67, 0x22, 0x27, 0x28, 0x88,
    0x11, 0xaa, 0x86, 0x68, 0x1a, 0xbb, 0xdd, 0xdd,
    0xdb, 0x05, 0xf4, 0xf4, 0xf4, 0xf4, 0x44, 0x4f,
    0xff, 0x4f, 0x44, 0xf4, 0xf4, 0xf4, 0xdd, 0xdb,
    0x00, 0x00, 0x00, 0x00, 0xdd, 0xda, 0x66, 0x22,
    0x71, 0x15, 0x55, 0x81, 0x22, 0x22, 0x76, 0x88,
    0x11, 0x31, 0x88, 0x88, 0xab, 0xbd, 0xdd, 0xdd,
    0xdd, 0x00, 0x04, 0x44, 0xff, 0xff, 0x4f, 0x4f,
    0xff, 0x4f, 0x44, 0xf4, 0xf4, 0x44, 0xdd, 0xdb,
    0x00, 0x00, 0x00, 0x0b, 0xdd, 0xda, 0x11, 0x22,
    0x23, 0x68, 0x55, 0x86, 0x22, 0x22, 0x7a, 0x88,
    0x1a, 0x71, 0x88, 0x89, 0xbb, 0xdd, 0xdd, 0xdd,
    0xdd, 0xd0, 0x00, 0x4f, 0x44, 0xff, 0x4f, 0x4f,
    0xff, 0x4f, 0x44, 0xf4, 0xff, 0xe2, 0xdd, 0xdb,
    0x90, 0x00, 0x05, 0xbd, 0xdd, 0xb8, 0x63, 0x22,
    0x27, 0xa6, 0x55, 0x88, 0x77, 0x22, 0x22, 0x88,
    0x1a, 0x28, 0xbd, 0xdb, 0xdd, 0xdd, 0xdd, 0xdd,
    0xdd, 0xdb, 0x00, 0x07, 0x44, 0x4f, 0x4f, 0x4f,
    0xff, 0x4f, 0x44, 0x4f, 0x4f, 0x22, 0xdd, 0xdb,
    0xbb, 0x9b, 0xbb, 0xbd, 0xdd, 0xd5, 0x86, 0x22,
    0x22, 0x77, 0x85, 0x88, 0x17, 0x22, 0x22, 0x88,
    0xaa, 0x2b, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd,
    0xdd, 0xdd, 0x00, 0x00, 0x54, 0x4f, 0x4f, 0x4f,
    0xff, 0x4f, 0x44, 0xf4, 0x44, 0x22, 0xbd, 0xdd,
    0xbb, 0xbb, 0xbb, 0xdd, 0xdd, 0xdd, 0x88, 0x72,
    0x27, 0x22, 0x88, 0x88, 0x67, 0x72, 0x22, 0x18,
    0x33, 0x2d, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd,
    0xdd, 0xdd, 0xd0, 0x00, 0x05, 0x4f, 0x4f, 0x4f,
    0xff, 0x4f, 0x44, 0x44, 0x4f, 0x22, 0xbd, 0xdd,
    0xdb, 0xbb, 0xdd, 0xdd, 0xdd, 0xdd, 0x88, 0x17,
    0x27, 0x72, 0x68, 0x88, 0x87, 0x32, 0x22, 0x36,
    0x37, 0x2d, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd,
    0xdd, 0xdd, 0xd5, 0x00, 0x00, 0x4f, 0x4f, 0x4f,
    0xff, 0xf4, 0xf4, 0xf4, 0xf4, 0x22, 0xbb, 0xdd,
    0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xd8, 0x67,
    0x72, 0x77, 0x38, 0x88, 0x83, 0x37, 0x22, 0x26,
    0x72, 0x2b, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd,
    0xdd, 0xdd, 0xdd, 0x00, 0x00, 0x4f, 0x4f, 0x4f,
    0xff, 0xf4, 0xf4, 0xf4, 0x44, 0x25, 0xbb, 0xdd,
    0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xd3,
    0x32, 0x73, 0x76, 0x88, 0x81, 0x33, 0x22, 0x2a,
    0x22, 0x2b, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd,
    0xdd, 0xdd, 0xdd, 0xb0, 0x54, 0x4f, 0x4f, 0x4f,
    0xff, 0xf4, 0xf4, 0xff, 0x44, 0x00, 0xbb, 0xdd,
    0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd,
    0xa7, 0x73, 0x26, 0x88, 0x86, 0x7a, 0x72, 0x27,
    0x22, 0x2b, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd,
    0xdd, 0xdd, 0xdd, 0xdb, 0x44, 0xff, 0x4f, 0x4f,
    0xff, 0xf4, 0xf4, 0x44, 0x40, 0x05, 0xbb, 0xdd,
    0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd,
    0x13, 0x23, 0x21, 0x68, 0x86, 0x17, 0x72, 0x22,
    0x22, 0x2b, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd,
    0xdd, 0xdd, 0xdd, 0xdb, 0x44, 0x4f, 0x4f, 0x4f,
    0xff, 0xff, 0x44, 0x42, 0x00, 0x05, 0xbd, 0xdd,
    0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd,
    0x87, 0x27, 0x27, 0x16, 0x66, 0x67, 0x22, 0x22,
    0x72, 0x7b, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd,
    0xdd, 0xdd, 0xdd, 0xdd, 0x94, 0x44, 0x44, 0x44,
    0x44, 0x44, 0x44, 0x00, 0x00, 0x05, 0xbb, 0xdd,
    0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xb8,
    0x86, 0x22, 0x22, 0x7a, 0x68, 0x81, 0x22, 0x22,
    0x37, 0x7b, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd,
    0xdd, 0xdd, 0xdd, 0xdb, 0xb5, 0x44, 0x44, 0x44,
    0x44, 0x47, 0x00, 0x00, 0x00, 0x05, 0xbd, 0xdd,
    0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xd8, 0x68,
    0x58, 0x72, 0x22, 0x27, 0x18, 0x86, 0x72, 0x22,
    0x1a, 0xbb, 0xbd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd,
    0xdd, 0xdd, 0xdd, 0xdb, 0xb5, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x09, 0xbb, 0xdd,
    0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xb9, 0x18, 0x85,
    0x58, 0x12, 0x22, 0x36, 0x18, 0x88, 0x32, 0x22,
    0x61, 0x3b, 0xbb, 0xbb, 0xbd, 0xdd, 0xdd, 0xdd,
    0xdd, 0xdd, 0xdd, 0xdb, 0xb9, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x09, 0xbb, 0xdd,
    0xdd, 0xdd, 0xdd, 0xdd, 0xb9, 0x7a, 0x68, 0x85,
    0x88, 0x62, 0x27, 0x16, 0x18, 0x88, 0x12, 0x27,
    0x86, 0x18, 0x9b, 0xbb, 0xbb, 0xbb, 0xbb, 0xbd,
    0xdd, 0xdd, 0xdd, 0xbb, 0xb5, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x05, 0xbb, 0xbd,
    0xdd, 0xdd, 0xdb, 0xbb, 0x87, 0x31, 0x68, 0x65,
    0x88, 0x82, 0x23, 0x16, 0x18, 0x88, 0x12, 0x23,
    0x88, 0x67, 0x27, 0xa8, 0x9b, 0xbb, 0xbb, 0xbb,
    0xbd, 0xdd, 0xbb, 0xbb, 0x95, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x05, 0x9b, 0xbb,
    0xbb, 0xbb, 0xbb, 0x96, 0x87, 0x16, 0x68, 0x18,
    0x88, 0x62, 0x31, 0x66, 0x18, 0x88, 0x62, 0x73,
    0x88, 0x63, 0x27, 0x33, 0x65, 0x55, 0x99, 0x9b,
    0xbb, 0xbb, 0xbb, 0x99, 0x55, 0x0a, 0xa1, 0x86,
    0x81, 0x68, 0x88, 0x55, 0x58, 0x85, 0x9b, 0xbb,
    0xbb, 0xbb, 0x95, 0x88, 0x83, 0x66, 0x66, 0x18,
    0x66, 0x82, 0xa1, 0x66, 0x18, 0x88, 0x62, 0x33,
    0x88, 0x81, 0x27, 0x7a, 0x18, 0x58, 0x86, 0x85,
    0x99, 0x99, 0x99, 0x95, 0x53, 0x2a, 0xaa, 0x88,
    0x67, 0x31, 0x68, 0x55, 0x58, 0x85, 0x59, 0xbb,
    0xbb, 0xb9, 0x58, 0x68, 0x83, 0x66, 0x61, 0x16,
    0x66, 0x62, 0x16, 0x66, 0x68, 0x88, 0x62, 0xaa,
    0x88, 0x86, 0x27, 0x77, 0x78, 0x55, 0x88, 0x22,
    0x25, 0x55, 0x95, 0x55, 0x6a, 0xa2, 0x2a, 0x88,
    0x62, 0x27, 0x37, 0x38, 0x88, 0x87, 0x55, 0x59,
    0x95, 0x58, 0x16, 0x88, 0x8a, 0x66, 0x63, 0x68,
    0x86, 0x67, 0x66, 0x66, 0x68, 0x88, 0x12, 0x11,
    0x88, 0x88, 0x72, 0x77, 0x78, 0x85, 0x58, 0x17,
    0x23, 0x32, 0x55, 0x55, 0x81, 0x13, 0x73, 0x66,
    0x62, 0x7a, 0xaa, 0x38, 0x88, 0x58, 0x27, 0x55,
    0x58, 0x32, 0x38, 0x88, 0x81, 0x66, 0xa2, 0x88,
    0x86, 0x61, 0x66, 0x61, 0x66, 0x68, 0x13, 0x11,
    0x88, 0x88, 0x12, 0x22, 0x71, 0x85, 0x58, 0x62,
    0x23, 0xa2, 0x68, 0x88, 0x81, 0x66, 0x88, 0x88,
    0x63, 0x2a, 0xaa, 0x28, 0x88, 0x55, 0x86, 0x61,
    0x66, 0x66, 0x68, 0x88, 0x66, 0x66, 0x77, 0x88,
    0x68, 0x16, 0x66, 0x62, 0x66, 0x68, 0xa1, 0x61,
    0x88, 0x88, 0x62, 0x22, 0x22, 0x85, 0x55, 0x83,
    0x72, 0x37, 0xa8, 0x88, 0x61, 0x66, 0x85, 0x55,
    0x86, 0x23, 0xaa, 0x71, 0x88, 0x85, 0x88, 0x66,
    0x88, 0x86, 0x88, 0x88, 0x16, 0x61, 0x21, 0x88,
    0x66, 0xa6, 0x86, 0x17, 0x66, 0x66, 0x31, 0x61,
    0x88, 0x88, 0x87, 0x72, 0x22, 0x68, 0x55, 0x86,
    0x77, 0x77, 0x36, 0x88, 0x13, 0x68, 0x85, 0x55,
    0x58, 0x12, 0x73, 0x72, 0x76, 0x88, 0x88, 0x68,
    0x88, 0x88, 0x88, 0x66, 0x36, 0x63, 0x26, 0x86,
    0x86, 0x36, 0x86, 0x11, 0x66, 0x66, 0x76, 0x61,
    0x88, 0x88, 0x81, 0x22, 0x22, 0x38, 0x85, 0x58,
    0x37, 0x22, 0x21, 0x68, 0xa2, 0x31, 0x68, 0x55,
    0x55, 0x81, 0x22, 0x22, 0xa8, 0x88, 0x88, 0x68,
    0x86, 0x88, 0x68, 0x81, 0x36, 0x17, 0x21, 0x68,
    0x86, 0x16, 0x66, 0x26, 0x66, 0x61, 0x36, 0x66,
    0x68, 0x88, 0x86, 0x27, 0x22, 0x28, 0x88, 0x88,
    0x17, 0x72, 0x2a, 0x66, 0xa2, 0x22, 0x36, 0x55,
    0x55, 0x58, 0x37, 0x3a, 0x16, 0x66, 0x66, 0x66,
    0x66, 0x18, 0x88, 0x67, 0x16, 0x12, 0x71, 0x68,
    0x81, 0x68, 0x61, 0x76, 0x66, 0x6a, 0x16, 0x66,
    0x88, 0x88, 0x86, 0x77, 0x22, 0x26, 0x88, 0x88,
    0x13, 0x37, 0x71, 0x66, 0xa2, 0x33, 0x2a, 0x85,
    0x55, 0x55, 0x17, 0x73, 0x16, 0x66, 0x66, 0x68,
    0x63, 0x88, 0x88, 0xa2, 0x66, 0xa2, 0xa6, 0x88,
    0x61, 0x68, 0x6a, 0x76, 0x66, 0x6a, 0x66, 0x6a
};

#endif
