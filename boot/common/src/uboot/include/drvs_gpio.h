/*******************************************************************************
 * Copyright by ZIXC Corporation.
 *
 * File Name:    drvs_gpio.h
 * File Mark:
 * Description:
 * Others:
 * Version:       v1.0
 * Author:        zhangdongdong
 * Date:          2015-07-31
 * History 1:
 *     Date:
 *     Version:
 *     Author:
 *     Modification:
 * History 2:
  ********************************************************************************/

#ifndef _DRVS_GPIO_H
#define _DRVS_GPIO_H
#include <common.h>
#include <config.h>

/****************************************************************************
* 	                                        Include files
****************************************************************************/


/****************************************************************************
* 	                                        Macros
****************************************************************************/
#define  GPIO0       0
#define  GPIO1       1
#define  GPIO2       2
#define  GPIO3       3
#define  GPIO4       4
#define  GPIO5       5
#define  GPIO6       6
#define  GPIO7       7
#define  GPIO8       8
#define  GPIO9       9
#define  GPIO10      10
#define  GPIO11      11
#define  GPIO12      12
#define  GPIO13      13
#define  GPIO14      14
#define  GPIO15      15
#define  GPIO16      16
#define  GPIO17      17
#define  GPIO18      18
#define  GPIO19      19
#define  GPIO20      20
#define  GPIO21      21
#define  GPIO22      22
#define  GPIO23      23
#define  GPIO24      24
#define  GPIO25      25
#define  GPIO26      26
#define  GPIO27      27
#define  GPIO28      28
#define  GPIO29      29
#define  GPIO30      30
#define  GPIO31      31
#define  GPIO32      32
#define  GPIO33      33
#define  GPIO34      34
#define  GPIO35      35
#define  GPIO36      36
#define  GPIO37      37
#define  GPIO38      38
#define  GPIO39      39
#define  GPIO40      40
#define  GPIO41      41
#define  GPIO42      42
#define  GPIO43      43
#define  GPIO44      44
#define  GPIO45      45
#define  GPIO46      46
#define  GPIO47      47
#define  GPIO48      48
#define  GPIO49      49
#define  GPIO50      50
#define  GPIO51      51
#define  GPIO52      52
#define  GPIO53      53
#define  GPIO54      54
#define  GPIO55      55
#define  GPIO56      56
#define  GPIO57      57
#define  GPIO58      58
#define  GPIO59      59
#define  GPIO60      60
#define  GPIO61      61
#define  GPIO62      62
#define  GPIO63      63
#define  GPIO64      64
#define  GPIO65      65
#define  GPIO66      66
#define  GPIO67      67
#define  GPIO68      68
#define  GPIO69      69
#define  GPIO70      70
#define  GPIO71      71
#define  GPIO72      72
#define  GPIO73      73
#define  GPIO74      74
#define  GPIO75      75
#define  GPIO76      76
#define  GPIO77      77
#define  GPIO78      78
#define  GPIO79      79
#define  GPIO80      80
#define  GPIO81      81
#define  GPIO82      82
#define  GPIO83      83
#define  GPIO84      84
#define  GPIO85      85
#define  GPIO86      86
#define  GPIO87      87
#define  GPIO88      88
#define  GPIO89      89
#define  GPIO90      90
#define  GPIO91      91
#define  GPIO92      92
#define  GPIO93      93
#define  GPIO94      94
#define  GPIO95      95
#define  GPIO96      96
#define  GPIO97      97
#define  GPIO98      98
#define  GPIO99      99
#define  GPIO100     100
#define  GPIO101     101
#define  GPIO102     102
#define  GPIO103     103
#define  GPIO104     104
#define  GPIO105     105
#define  GPIO106     106
#define  GPIO107     107
#define  GPIO108     108
#define  GPIO109     109
#define  GPIO110     110
#define  GPIO111     111
#define  GPIO112     112
#define  GPIO113     113
#define  GPIO114     114
#define  GPIO115     115
#define  GPIO116     116
#define  GPIO117     117
#define  GPIO118     118
#define  GPIO119     119
#define  GPIO120     120
#define  GPIO121     121
#define  GPIO122     122
#define  GPIO123     123
#define  GPIO124     124
#define  GPIO125     125
#define  GPIO126     126
#define  GPIO127     127
#define  GPIO128     128
#define  GPIO129     129
#define  GPIO130     130
#define  GPIO131     131
#define  GPIO132     132
#define  GPIO133     133
#define  GPIO134     134
#define  GPIO135     135

#define  MAX_GPIO_NUM   GPIO135
#define  INVLID_GPIO    0xffff

/****************************************************************************
* 	                                        Types
****************************************************************************/
typedef signed char SINT8;
typedef unsigned char UINT8;

typedef signed short SINT16;
typedef unsigned short UINT16;

typedef signed int SINT32;
typedef unsigned int UINT32;
typedef signed char  CHAR;

typedef enum{
	GPIO_IN = 101,
	GPIO_OUT = 102,
}T_ZDrvGpio_IoDirection;

typedef enum{
	GPIO_LOW = 201,
	GPIO_HIGH = 202,
}T_ZDrvGpio_IoVal;


typedef enum{
	GPIO_PULL_DOWN = 0x1,
	GPIO_NO_ACTION = 0x2,
	GPIO_PULL_UP = 0x3,
}T_ZDrvGpio_PullUp;

typedef enum
{
    /*[31:24]:gpio_id   [23:12]:level1_sel   [11:0]:level2_sel*/
    GPIO0_GPIO0					= 0x00000000,
    GPIO0_NAND_WE					= 0x00001000,
	GPIO0_LCD_OE_N					= 0x00001001,
    GPIO1_GPIO1              		= 0x01000000,
    GPIO1_NAND_CS0           		= 0x01001000,
	GPIO1_LCD_CS_N					= 0x01001001,
    GPIO2_GPIO2              		= 0x02000000,
    GPIO2_NAND_READY         		= 0x02001000,
	GPIO2_LCD_RS					= 0x02001001,
    GPIO3_GPIO3              		= 0x03000000,
    GPIO3_NAND_CLE           		= 0x03001000,
    GPIO3_LCD_RESET_N        		= 0x03001001,
    GPIO4_GPIO4              		= 0x04000000,
    GPIO4_NAND_ALE           		= 0x04001000,
	GPIO4_LCD_WE_N					= 0x04001001,
    GPIO5_GPIO5              		= 0x05000000,
    GPIO5_NAND_RE            		= 0x05001000,
    GPIO5_LCD_TE         			= 0x05001001,
    GPIO6_GPIO6              		= 0x06000000,
    GPIO6_NAND_WRITE_PROTECT 		= 0x06001000,
    GPIO6_LCD_D0					= 0x06001001,
    GPIO7_GPIO7              		= 0x07000000,
    GPIO7_NAND_DATA0         		= 0x07001000,
    GPIO7_LCD_D1            		= 0x07001001,
    GPIO8_GPIO8              		= 0x08000000,
    GPIO8_NAND_DATA1         		= 0x08001000,
    GPIO8_LCD_D2           			= 0x08001001,
    GPIO9_GPIO9              		= 0x09000000,
    GPIO9_NAND_DATA2         		= 0x09001000,
    GPIO9_LCD_D3      				= 0x09001001,
    GPIO10_GPIO10            		= 0x0A000000,
    GPIO10_NAND_DATA3        		= 0x0A001000,
    GPIO10_LCD_D4      				= 0x0A001001,
    GPIO11_GPIO11            		= 0x0B000000,
    GPIO11_NAND_DATA4        		= 0x0B001000,
    GPIO11_LCD_D5      				= 0x0B001001,
    GPIO12_GPIO12            		= 0x0C000000,
    GPIO12_NAND_DATA5        		= 0x0C001000,
    GPIO12_LCD_D6      				= 0x0C001001,
    GPIO13_GPIO13            		= 0x0D000000,
    GPIO13_NAND_DATA6        		= 0x0D001000,
    GPIO13_LCD_D7					= 0x0D001001,
    GPIO14_GPIO14            		= 0x0E000000,
    GPIO14_NAND_DATA7        		= 0x0E001000,
    GPIO14_LCD_D8         	 		= 0x0E001001,
    GPIO15_CLK_OUT0          		= 0x0F000000,
    GPIO15_GPIO15            		= 0x0F000001,
    GPIO16_GPIO16            		= 0x10000000,
    GPIO16_CLK_OUT1          		= 0x10000001,
    GPIO17_GPIO17            		= 0x11000000,
    GPIO17_CLK_OUT2          		= 0x11000001,
    GPIO17_TEST_CLK_OUT      		= 0x11000002,
    GPIO17_TDM_MCLK_OUT           	= 0x11001000,
    GPIO17_I2S0_MCLK_OUT          	= 0x11001001,
    GPIO17_I2S1_MCLK_OUT      		= 0x11001002,
    GPIO18_GPIO18            		= 0x12000000,
    GPIO18_CLK_32K_OUT       		= 0x12000001,
    GPIO19_GPIO19          			= 0x13000000,
    GPIO19_RMII_CLK_I            	= 0x13001000,
    GPIO20_GPIO20          			= 0x14000000,
    GPIO20_RMII_CLK_O            	= 0x14001000,
	GPIO21_CLK_REQ0          		= 0x15000000,
    GPIO21_GPIO21           		= 0x15000001,
	GPIO22_CLK_REQ1 				= 0x16000000,
	GPIO22_GPIO22					= 0x16000001,
    GPIO23_PWRCTRL          		= 0x17000000,
    GPIO23_GPIO23            		= 0x17000001,
    GPIO24_GPIO24            		= 0x18000000,
	GPIO25_GPIO25			 		= 0x19000000,
    GPIO25_SSP0_CS           		= 0x19001000,
	GPIO26_GPIO26 					= 0x1A000000,
    GPIO26_SSP0_CLK          		= 0x1A001000,
    GPIO27_GPIO27            		= 0x1B000000,
    GPIO27_SSP0_RXD          		= 0x1B001000,
    GPIO28_GPIO28            		= 0x1C000000,
    GPIO28_SSP0_TXD          		= 0x1C001000,
    GPIO29_UART0_RXD         		= 0x1D000000,
    GPIO29_GPIO29            		= 0x1D000001,
    GPIO29_UART0_TXD         		= 0x1D000002,
    GPIO29_FRAME_SYNC        		= 0x1D001000,
    GPIO29_TEST_PIN10        		= 0x1D001001,
    GPIO30_UART0_TXD         		= 0x1E000000,
    GPIO30_GPIO30            		= 0x1E000001,
    GPIO30_UART0_RXD         		= 0x1E000002,
    GPIO30_LTE_PRE_TX        		= 0x1E001000,
    GPIO30_TEST_PIN11        		= 0x1E001001,
    GPIO31_UART0_CTS         		= 0x1F000000,
    GPIO31_GPIO31            		= 0x1F000001,
    GPIO31_LTE_TPU_OUT3      		= 0x1F001000,
    GPIO31_UART1_TXD         		= 0x1F001001,
	GPIO31_TEST_PIN12				= 0x1F001002,
    GPIO32_UART0_RTS         		= 0x20000000,
    GPIO32_GPIO32            		= 0x20000001,
    GPIO32_LTE_TPU_OUT4      		= 0x20001000,
    GPIO32_UART1_RXD         		= 0x20001001,
    GPIO33_GPIO33            		= 0x21000000,
	GPIO33_UART1_RXD				= 0x21001000,
	GPIO33_UART2_TXD				= 0x21001001,
	GPIO33_UART2_RXD				= 0x21001002,
	GPIO34_GPIO34					= 0x22000000,
	GPIO34_UART1_TXD				= 0x22001000,
	GPIO34_UART2_RXD				= 0x22001001,
	GPIO34_UART2_TXD				= 0x22001002,
	GPIO35_GPIO35					= 0x23000000,
    GPIO35_I2S0_WS           		= 0x23001000,
    GPIO35_TEST_PIN0         		= 0x23001001,
    GPIO35_TDM_FS            		= 0x23001002,
    GPIO36_GPIO36            		= 0x24000000,
    GPIO36_I2S0_CLK          		= 0x24001000,
    GPIO36_TEST_PIN1         		= 0x24001001,
    GPIO36_TDM_CLK           		= 0x24001002,
    GPIO37_GPIO37            		= 0x25000000,
    GPIO37_I2S0_DIN          		= 0x25001000,
    GPIO37_TEST_PIN2         		= 0x25001001,
    GPIO37_TDM_DATA_IN       		= 0x25001002,
    GPIO38_GPIO38            		= 0x26000000,
    GPIO38_I2S0_DOUT         		= 0x26001000,
    GPIO38_TEST_PIN3         		= 0x26001001,
    GPIO38_TDM_DATA_OUT      		= 0x26001002,
    GPIO39_GPIO39            		= 0x27000000,
    GPIO39_I2S1_WS           		= 0x27001000,
    GPIO39_TEST_PIN4         		= 0x27001001,
    GPIO39_TDM_FS            		= 0x27001002,
	GPIO39_PWM0			 			= 0x27001003,
    GPIO40_GPIO40            		= 0x28000000,
    GPIO40_I2S1_CLK          		= 0x28001000,
    GPIO40_TEST_PIN5         		= 0x28001001,
    GPIO40_TDM_CLK           		= 0x28001002,
	GPIO40_PWM1			 			= 0x28001003,
    GPIO41_GPIO41            		= 0x29000000,
    GPIO41_I2S1_DIN          		= 0x29001000,
    GPIO41_TEST_PIN6         		= 0x29001001,
    GPIO41_TDM_DATA_IN       		= 0x29001002,
    GPIO42_GPIO42            		= 0x2A000000,
    GPIO42_I2S1_DOUT         		= 0x2A001000,
    GPIO42_TEST_PIN7         		= 0x2A001001,
    GPIO42_TDM_DATA_OUT      		= 0x2A001002,
    GPIO43_SCL0              		= 0x2B000000,
    GPIO43_GPIO43            		= 0x2B000001,
    GPIO44_SDA0              		= 0x2C000000,
    GPIO44_GPIO44            		= 0x2C000001,
    GPIO45_GPIO45            		= 0x2D000000,
    GPIO45_SCL1              		= 0x2D001000,
    GPIO46_GPIO46            		= 0x2E000000,
    GPIO46_SDA1              		= 0x2E001000,
    GPIO47_GPIO47            		= 0x2F000000,
    GPIO47_EXT_INT0          		= 0x2F000001,
    GPIO48_GPIO48            		= 0x30000000,
    GPIO48_EXT_INT1          		= 0x30000001,
    GPIO49_GPIO49            		= 0x31000000,
    GPIO49_EXT_INT2          		= 0x31000001,
    GPIO50_GPIO50            		= 0x32000000,
    GPIO50_EXT_INT3          		= 0x32000001,
    GPIO50_TEST_PIN8         		= 0x32001000,
    GPIO51_GPIO51            		= 0x33000000,
    GPIO51_EXT_INT4          		= 0x33000001,
    GPIO51_TEST_PIN9         		= 0x33001000,
    GPIO52_GPIO52            		= 0x34000000,
    GPIO52_EXT_INT5          		= 0x34000001,
    GPIO52_TEST_PIN13        		= 0x34001000,
    GPIO53_GPIO53            		= 0x35000000,
    GPIO53_EXT_INT6          		= 0x35000001,
    GPIO53_TEST_PIN14        		= 0x35001000,
    GPIO54_GPIO54            		= 0x36000000,
    GPIO54_EXT_INT7          		= 0x36000001,
    GPIO54_TEST_PIN15        		= 0x36001000,
    GPIO55_GPIO55            		= 0x37000000,
    GPIO55_RMII_TXEN        		= 0x37001000,
    GPIO56_GPIO56            		= 0x38000000,
    GPIO56_RMII_RXEN        		= 0x38001000,
    GPIO57_GPIO57            		= 0x39000000,
    GPIO57_RMII_RXD0        		= 0x39001000,
    GPIO58_GPIO58            		= 0x3A000000,
    GPIO58_RMII_RXD1        		= 0x3A001000,
    GPIO59_GPIO59            		= 0x3B000000,
    GPIO59_RMII_TXD0        		= 0x3B001000,
    GPIO60_GPIO60            		= 0x3C000000,
    GPIO60_RMII_TXD1        		= 0x3C001000,
    GPIO61_GPIO61            		= 0x3D000000,
    GPIO61_MDC_SCLK        			= 0x3D001000,
    GPIO62_GPIO62            		= 0x3E000000,
    GPIO62_MDC_SDIO        			= 0x3E001000,
    GPIO63_GPIO63            		= 0x3F000000,
    GPIO63_PHY_RST       			= 0x3F001000,
    GPIO64_GPIO64            		= 0x40000000,
    GPIO64_PHY_INT       			= 0x40001000,
	GPIO65_GPIO65					= 0x41000000,
    GPIO66_GPIO66            		= 0x42000000,
    GPIO66_KEY_COL2    				= 0x42000001,
	GPIO66_EMMC_CLK					= 0x42001000,
	GPIO67_GPIO67            		= 0x43000000,
    GPIO67_KEY_COL3    				= 0x43000001,
	GPIO67_EMMC_CMD					= 0x43001000,
	GPIO68_GPIO68            		= 0x44000000,
    GPIO68_KEY_COL4    				= 0x44000001,
	GPIO68_EMMC_DATA0				= 0x44001000,
	GPIO69_GPIO69            		= 0x45000000,
    GPIO69_KEY_ROW2   				= 0x45000001,
	GPIO69_EMMC_DATA1				= 0x45001000,
	GPIO70_GPIO70           		= 0x46000000,
    GPIO70_KEY_ROW3   				= 0x46000001,
	GPIO70_EMMC_DATA2				= 0x46001000,

	GPIO71_GPIO71           		= 0x47000000,
    GPIO71_KEY_ROW4   				= 0x47000001,
	GPIO71_EMMC_DATA3				= 0x47001000,
	GPIO72_GPIO72           		= 0x48000000,
	GPIO72_SD1_HOST_SDCLK			= 0x48001000,
    GPIO73_GPIO73            		= 0x49000000,
    GPIO73_M_JTAG_TDO        		= 0x49000001,
    GPIO73_SD1_CMD           		= 0x49001000,
    GPIO73_PS_JTAG_TDO       		= 0x49001001,
    GPIO73_PHY_JTAG_TDO      		= 0x49001002,
    GPIO73_AP_JTAG_TDO       		= 0x49001003,
    GPIO74_GPIO74            		= 0x4A000000,
    GPIO74_M_JTAG_TCK        		= 0x4A000001,
    GPIO74_SD1_DATA0         		= 0x4A001000,
    GPIO74_PS_JTAG_TCK       		= 0x4A001001,
    GPIO74_PHY_JTAG_TCK      		= 0x4A001002,
    GPIO74_AP_JTAG_TCK       		= 0x4A001003,
    GPIO75_GPIO75            		= 0x4B000000,
    GPIO75_M_JTAG_TRST       		= 0x4B000001,
    GPIO75_SD1_DATA1         		= 0x4B001000,
    GPIO75_PS_JTAG_TRST      		= 0x4B001001,
    GPIO75_PHY_JTAG_TRST     		= 0x4B001002,
    GPIO75_AP_JTAG_TRST      		= 0x4B001003,
    GPIO76_GPIO76            		= 0x4C000000,
    GPIO76_M_JTAG_TMS        		= 0x4C000001,
    GPIO76_SD1_DATA2         		= 0x4C001000,
    GPIO76_PS_JTAG_TMS       		= 0x4C001001,
    GPIO76_PHY_JTAG_TMS      		= 0x4C001002,
    GPIO76_AP_JTAG_TMS       		= 0x4C001003,
    GPIO77_GPIO77            		= 0x4D000000,
    GPIO77_M_JTAG_TDI        		= 0x4D000001,
    GPIO77_SD1_DATA3         		= 0x4D001000,
    GPIO77_PS_JTAG_TDI       		= 0x4D001001,
    GPIO77_PHY_JTAG_TDI      		= 0x4D001002,
    GPIO77_AP_JTAG_TDI       		= 0x4D001003,
    GPIO78_M_JTAG_TCK        		= 0x4E000000,
    GPIO78_GPIO78            		= 0x4E000001,
    GPIO78_PS_JTAG_TCK       		= 0x4E001000,
    GPIO78_PHY_JTAG_TCK      		= 0x4E001001,
    GPIO78_AP_JTAG_TCK       		= 0x4E001002,
    GPIO79_M_JTAG_TDI        		= 0x4F000000,
    GPIO79_GPIO79            		= 0x4F000001,
    GPIO79_PS_JTAG_TDI       		= 0x4F001000,
    GPIO79_PHY_JTAG_TDI      		= 0x4F001001,
    GPIO79_AP_JTAG_TDI       		= 0x4F001002,
    GPIO80_M_JTAG_TDO        		= 0x50000000,
    GPIO80_GPIO80            		= 0x50000001,
    GPIO80_PS_JTAG_TDO       		= 0x50001000,
    GPIO80_PHY_JTAG_TDO      		= 0x50001001,
    GPIO80_AP_JTAG_TDO       		= 0x50001002,
    GPIO81_M_JTAG_TMS        		= 0x51000000,
    GPIO81_GPIO81            		= 0x51000001,
    GPIO81_PS_JTAG_TMS       		= 0x51001000,
    GPIO81_PHY_JTAG_TMS      		= 0x51001001,
    GPIO81_AP_JTAG_TMS       		= 0x51001002,
    GPIO82_M_JTAG_TRST       		= 0x52000000,
    GPIO82_GPIO82            		= 0x52000001,
    GPIO82_PS_JTAG_TRST      		= 0x52001000,
    GPIO82_PHY_JTAG_TRST     		= 0x52001001,
    GPIO82_AP_JTAG_TRST      		= 0x52001002,
    GPIO83_KEY_COL0          		= 0x53000000,
    GPIO83_GPIO83           		= 0x53000001,
    GPIO84_KEY_COL1          		= 0x54000000,
    GPIO84_GPIO84            		= 0x54000001,
    GPIO85_KEY_ROW0          		= 0x55000000,
    GPIO85_GPIO85            		= 0x55000001,
    GPIO86_KEY_ROW1          		= 0x56000000,
    GPIO86_GPIO86            		= 0x56000001,
    GPIO87_GPIO87					= 0x57000000,
    GPIO87_CAM_SPI_CS				= 0x57001000,
    GPIO88_GPIO88					= 0x58000000,
    GPIO88_CAM_SPI_CLK				= 0x58001000,
    GPIO89_GPIO89					= 0x59000000,
    GPIO89_CAM_SPI_DATA0			= 0x59001000,
    GPIO90_GPIO90					= 0x5A000000,
    GPIO90_CAM_SPI_DATA1			= 0x5A001000,
    GPIO90_CAM_SPI_TXD				= 0x5A001001,
    GPIO91_GPIO91					= 0x5B000000,
    GPIO91_CAM_SPI_DATA2			= 0x5B001000,
    GPIO92_GPIO92					= 0x5C000000,
    GPIO92_CAM_SPI_DATA3			= 0x5C001000,
	GPIO93_GPIO93					= 0x5D000000,
    GPIO93_SPIFC_CS					= 0x5D001000,
    GPIO94_GPIO94					= 0x5E000000,
    GPIO94_SPIFC_CLK				= 0x5E001000,
    GPIO95_GPIO95					= 0x5F000000,
    GPIO95_SPIFC_DATA0				= 0x5F001000,
    GPIO96_GPIO96					= 0x60000000,
    GPIO96_SPIFC_DATA1				= 0x60001000,
    GPIO97_GPIO97					= 0x61000000,
    GPIO97_SPIFC_DATA2				= 0x61001000,
    GPIO98_GPIO98					= 0x62000000,
    GPIO98_SPIFC_DATA3				= 0x62001000,
	GPIO99_GPIO99					= 0x63000000,
	GPIO100_GPIO100					= 0x64000000,
	GPIO100_RF_SPI_STR				= 0x64001000,
	GPIO101_GPIO101					= 0x65000000,
	GPIO101_RF_SPI_CLK				= 0x65001000,
	GPIO102_GPIO102					= 0x66000000,
	GPIO102_RF_SPI_DATA				= 0x66001000,
	GPIO103_GPIO103 				= 0x67000000,
	GPIO104_GPIO104 				= 0x68000000,
	GPIO104_TD_G0_GPIO2				= 0x68001081,
	GPIO104_LTE_TPU_OUT0_5			= 0x68001009,
	GPIO104_W_G0_GPIO2				= 0x68001011,
	GPIO104_GSM_T_OUT_O_0         	= 0x68001021,
	GPIO105_GPIO105 				= 0x69000000,
	GPIO105_TD_G0_GPIO3				= 0x69001081,
	GPIO105_LTE_TPU_OUT0_6			= 0x69001009,
	GPIO105_W_G0_GPIO3				= 0x69001011,
	GPIO105_GSM_T_OUT_O_1         	= 0x69001021,
	GPIO106_GPIO106 				= 0x6A000000,
	GPIO106_TD_G0_GPIO4				= 0x6A001081,
	GPIO106_LTE_TPU_OUT0_7			= 0x6A001009,
	GPIO106_W_G0_GPIO4				= 0x6A001011,
	GPIO106_GSM_T_OUT_O_2         	= 0x6A001021,
	GPIO107_GPIO107 				= 0x6B000000,
	GPIO107_TD_G0_GPIO5				= 0x6B001081,
	GPIO107_LTE_TPU_OUT0_8			= 0x6B001009,
	GPIO107_W_G0_GPIO5				= 0x6B001011,
	GPIO107_GSM_T_OUT_O_3         	= 0x6B001021,
	GPIO108_GPIO108 				= 0x6C000000,
	GPIO108_TD_G0_GPIO6				= 0x6C001081,
	GPIO108_LTE_TPU_OUT0_9			= 0x6C001009,
	GPIO108_W_G0_GPIO6				= 0x6C001011,
	GPIO108_GSM_T_OUT_O_4        	= 0x6C001021,
	GPIO109_GPIO109 				= 0x6D000000,
	GPIO109_TD_G0_GPIO7				= 0x6D001081,
	GPIO109_LTE_TPU_OUT0_10			= 0x6D001009,
	GPIO109_W_G0_GPIO7				= 0x6D001011,
	GPIO109_GSM_T_OUT_O_5         	= 0x6D001021,
	GPIO110_GPIO110					= 0x6E000000,
	GPIO110_TD_G0_GPIO8				= 0x6E001081,
	GPIO110_LTE_TPU_OUT0_11			= 0x6E001009,
	GPIO110_W_G0_GPIO8				= 0x6E001011,
	GPIO110_GSM_T_OUT_O_6        	= 0x6E001021,
	GPIO111_GPIO111					= 0x6F000000,
	GPIO111_TD_G0_GPIO9				= 0x6F001081,
	GPIO111_LTE_TPU_OUT0_12			= 0x6F001009,
	GPIO111_W_G0_GPIO9				= 0x6F001011,
	GPIO111_GSM_T_OUT_O_7         	= 0x6F001021,
	GPIO112_GPIO112					= 0x70000000,
	GPIO112_MIPI_RFFE_CLK0			= 0x70001006,
	GPIO112_TD_G0_GPIO10			= 0x70001081,
	GPIO112_LTE_TPU_OUT0_13			= 0x70001009,
	GPIO112_W_G0_GPIO10				= 0x70001011,
	GPIO112_GSM_T_OUT_O_8         	= 0x70001021,
	GPIO113_GPIO113					= 0x71000000,
	GPIO113_MIPI_RFFE_DATA0			= 0x71001006,
	GPIO113_TD_G0_GPIO11			= 0x71001081,
	GPIO113_LTE_TPU_OUT0_14			= 0x71001009,
	GPIO113_W_G0_GPIO11				= 0x71001011,
	GPIO113_GSM_T_OUT_O_9         	= 0x71001021,
	GPIO114_GPIO114					= 0x72000000,
	GPIO114_MIPI_RFFE_CLK1			= 0x72001006,
	GPIO114_ABB_I2C_SEL_PINMUX		= 0x72000001,
	GPIO114_TD_G0_GPIO12			= 0x72001081,
	GPIO114_LTE_TPU_OUT0_15			= 0x72001009,
	GPIO114_W_G0_GPIO12				= 0x72001011,
	GPIO114_GSM_T_OUT_O_10        	= 0x72001021,
	GPIO115_GPIO115					= 0x73000000,
	GPIO115_ABB_I2C_SDA_PINMUX		= 0x73000001,
	GPIO115_MIPI_RFFE_DATA1			= 0x73001006,
	GPIO115_TD_G1_GPIO0				= 0x73001081,
	GPIO115_LTE_TPU_OUT1_0			= 0x73001009,
	GPIO115_W_G1_GPIO0				= 0x73001011,
	GPIO115_GSM_T_OUT_O_11       	= 0x73001021,

	GPIO133_GPIO133					= 0x85000000,
	GPIO133_SIM1_RST				= 0x85000001,
	GPIO133_TD_G1_GPIO1			= 0x85001081,
	GPIO133_LTE_TPU_OUT1_1		= 0x85001009,
	GPIO133_W_G1_GPIO1			= 0x85001011,
	GPIO133_GSM_T_OUT_O_12       	= 0x85001021,
	GPIO134_GPIO134				= 0x86000000,
	GPIO134_SIM1_CLK				= 0x86000001,
	GPIO134_TD_G1_GPIO2			= 0x86001081,
	GPIO134_LTE_TPU_OUT1_2		= 0x86001009,
	GPIO134_W_G1_GPIO2			= 0x86001011,
	GPIO134_GSM_T_OUT_O_13       	= 0x86001021,
	GPIO135_GPIO135				= 0x87000000,
	GPIO135_SIM1_RST				= 0x87000001,
	GPIO135_TD_G1_GPIO3			= 0x87001081,
	GPIO135_LTE_TPU_OUT1_3		= 0x87001009,
	GPIO135_W_G1_GPIO3			= 0x87001011,
	GPIO135_GSM_T_OUT_O_14       	= 0x87001021,
	GPIO116_SIM_RST					= 0x74000000,
	GPIO116_GPIO116 				= 0x74000001,
	GPIO117_SIM_CLK					= 0x75000000,
	GPIO117_GPIO117 				= 0x75000001,
	GPIO118_SIM_DATA				= 0x76000000,
	GPIO118_GPIO118 				= 0x76000001,
	GPIO119_GPIO119					= 0x77000000,
	GPIO119_EXT_INT8				= 0x77000001,
	GPIO119_M_JTAG_TDO				= 0x77000002,
	GPIO119_URAT0_RTS				= 0x77000003,
	GPIO119_PSJTAG_TDO				= 0x77001000,
	GPIO119_PHYJTAG_TDO				= 0x77001001,
	GPIO119_APJTAG_TDO				= 0x77001002,
	GPIO119_PWM0					= 0x77001003,
    GPIO120_GPIO120            		= 0x78000000,
    GPIO120_EXT_INT9         		= 0x78000001,
    GPIO120_M_JTAG_TCK        		= 0x78000002,
	GPIO120_UART0_CTS				= 0x78000003,
    GPIO120_PSJTAG_TCK       		= 0x78001000,
    GPIO120_PHYJTAG_TCK      		= 0x78001001,
    GPIO120_APJTAG_TCK       		= 0x78001002,
	GPIO120_PWM1					= 0x78001003,
	GPIO121_GPIO121            		= 0x79000000,
    GPIO121_EXT_INT10         		= 0x79000001,
    GPIO121_M_JTAG_TRST        		= 0x79000002,
    GPIO121_PSJTAG_TRST       		= 0x79001000,
    GPIO121_PHYJTAG_TRST      		= 0x79001001,
    GPIO121_APJTAG_TRST      		= 0x79001002,
	GPIO121_UART2_RXD				= 0x79001003,
	GPIO122_GPIO122            		= 0x7A000000,
    GPIO122_EXT_INT11         		= 0x7A000001,
    GPIO122_M_JTAG_TMS        		= 0x7A000002,
    GPIO122_PSJTAG_TMS       		= 0x7A001000,
    GPIO122_PHYJTAG_TMS      		= 0x7A001001,
    GPIO122_APJTAG_TMS      		= 0x7A001002,
	GPIO122_UART2_TXD				= 0x7A001003,
	GPIO123_GPIO123            		= 0x7B000000,
    GPIO123_EXT_INT12         		= 0x7B000001,
    GPIO123_M_JTAG_TDI        		= 0x7B000002,
    GPIO123_PSJTAG_TDI       		= 0x7B001000,
    GPIO123_PHYJTAG_TDI      		= 0x7B001001,
    GPIO123_APJTAG_TDI      		= 0x7B001002,
	GPIO123_UART2_RTS				= 0x7B001003,
	GPIO124_GPIO124            		= 0x7C000000,
    GPIO124_EXT_INT13         		= 0x7C000001,
	GPIO124_UART2_CTS				= 0x7C001000,
	GPIO125_GPIO125            		= 0x7D000000,
    GPIO125_EXT_INT14         		= 0x7D000001,
	GPIO125_UART1_RTS				= 0x7D001000,
	GPIO126_GPIO126            		= 0x7E000000,
    GPIO126_EXT_INT15         		= 0x7E000001,
    GPIO126_KEY_COL2				= 0x7E000002,
	GPIO126_UART1_CTS				= 0x7E001000,
	GPIO127_GPIO127            		= 0x7F000000,
    GPIO127_EXT_INT8         		= 0x7F000001,
    GPIO127_KEY_COL3				= 0x7F000002,
	GPIO128_GPIO128            		= 0x80000000,
    GPIO128_EXT_INT9         		= 0x80000001,
    GPIO128_KEY_COL4				= 0x80000002,
	GPIO129_GPIO129            		= 0x81000000,
    GPIO129_EXT_INT10         		= 0x81000001,
    GPIO129_KEY_COL5				= 0x81000002,
	GPIO130_GPIO130            		= 0x82000000,
    GPIO130_EXT_INT11         		= 0x82000001,
    GPIO130_KEY_ROW2				= 0x82000002,
	GPIO131_GPIO131            		= 0x83000000,
    GPIO131_EXT_INT12         		= 0x83000001,
    GPIO131_KEY_ROW3				= 0x83000002,
	GPIO132_GPIO132            		= 0x84000000,
    GPIO132_EXT_INT13       		= 0x84000001,
    GPIO132_KEY_ROW4				= 0x84000002,


}T_ZDrvGpio_FuncSel;


/****************************************************************************
* 	                                        Constants
****************************************************************************/

/****************************************************************************
* 	                                        Global  Variables
****************************************************************************/

/****************************************************************************
* 	                                        Function Prototypes
****************************************************************************/


/****************************************************************************
*											  Function Definitions
****************************************************************************/




/**************************************************************************
* Functin:	zDrvGpio_SetFunc
* Description:    set the pin use ,used as GPIO or other module,when use for GPIO
* Parameters:
*		Input:
*			  gpio_id: gpio id
*			  func_sel: sel pd or aon func
*		Output:
*			NONE
* Returns:
*		success or parameter fault
* Others:
*		None.
**************************************************************************/
SINT32 zDrvGpio_SetFunc(UINT32 gpio_id, T_ZDrvGpio_FuncSel func_sel);

/**************************************************************************
* Functin:	zDrvGpio_PullUpDown
* Description:    internal pull up or pull down
* Parameters:
*		Input:
*			  gpio_id: gpio id
*			  value: pull up or down val
*		Output:
*			NONE
* Returns:
*		success or parameter fault
* Others:
*		None.
**************************************************************************/
SINT32 zDrvGpio_PullUpDown(UINT32 gpio_id, UINT32 val);

/**************************************************************************
* Functin:	zDrvGpio_PullUpDown
* Description:    internal pull up or pull down
* Parameters:
*		Input:
*			  gpio_id: gpio id
*			  value: pull up or down val
*		Output:
*			NONE
* Returns:
*		success or parameter fault
* Others:
*		None.
**************************************************************************/

void zDrvGpio_SetDirection(UINT32 gpio_id, T_ZDrvGpio_IoDirection value);

/**************************************************************************
* Functin:	zDrvGpio_GetDirection
* Description:    get direction
* Parameters:
*		Input:
*			  gpio_id: gpio id
*		Output:
*			  gpio input or output
* Returns:
*
* Others:
*		None.
**************************************************************************/
T_ZDrvGpio_IoDirection zDrvGpio_GetDirection(UINT32 gpio_id);


/**************************************************************************
* Functin:	zDrvGpio_SetOutputValue
* Description:    set output value
* Parameters:
*		Input:
*			  gpio_id: gpio id
*			  value: high or low.
*		Output:
*			NONE
* Returns:
*		success or parameter fault
* Others:
*		None.
**************************************************************************/
void zDrvGpio_SetOutputValue(UINT32 gpio_id, T_ZDrvGpio_IoVal value);

/**************************************************************************
* Functin:	zDrvGpio_GetOutputValue
* Description:    get output value
* Parameters:
*		Input:
*			  gpio_id: gpio id
*		Output:
*			  output high or low
* Returns:
*		NONE
* Others:
*		None.
**************************************************************************/
T_ZDrvGpio_IoVal zDrvGpio_GetOutputValue(UINT32 gpio_id);

/**************************************************************************
* Functin:	zDrvGpio_GetInputValue
* Description:    get input value
* Parameters:
*		Input:
*			  gpio_id: gpio id
*		Output:
*			  input high or low
* Returns:
*		NONE
* Others:
*		None.
**************************************************************************/
T_ZDrvGpio_IoVal zDrvGpio_GetInputValue(UINT32 gpio_id);


#endif
