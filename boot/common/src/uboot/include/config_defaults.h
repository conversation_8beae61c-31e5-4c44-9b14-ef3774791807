/*
 * config_defaults.h - sane defaults for everyone
 *
 * Copyright (c) 2009 Analog Devices Inc.
 *
 * Licensed under the GPL-2 or later.
 */

#ifndef _CONFIG_DEFAULTS_H_
#define _CONFIG_DEFAULTS_H_

/* Support bootm-ing different OSes */
#define CONFIG_BOOTM_LINUX 1
#define CONFIG_GZIP 1
#define CONFIG_ZLIB 1

/* add by svk@20241203*/
#define CONFIG_QRZL_UE 1
#define CONFIG_JCV_HW_MZ803_V3_2 1

#define CONFIG_JFFS2_LZO
#define CONFIG_SYS_JFFS2_SORT_FRAGMENTS
#define CONFIG_SYS_JFFS2_SORT_FRAGMENTS_DIR

//#define CONFIG_ZX297520V3E_UFI_MINI
#define CONFIG_ZX297520V3E_MIFI_MINI
#define CONFIG_ZX297520V3E_NOR_FLASH
#define CONFIG_ZX297520V3E_JFFS2_COMPRESS
#define CONFIG_ZX297520V3_UFI_MINI_32K_NOR
//#define CONFIG_ZX297520V3_AIC8800

#define ZX_RM_WDT_RESTART 1
#define CONFIG_I2S0_TO_32K

#endif
