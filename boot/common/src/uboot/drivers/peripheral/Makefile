#
# (C) Copyright 2000-2007
# <PERSON>, DENX Software Engineering, <EMAIL>.
#
# See file CREDITS for list of people who contributed to this
# project.
#
# This program is free software; you can redistribute it and/or
# modify it under the terms of the GNU General Public License as
# published by the Free Software Foundation; either version 2 of
# the License, or (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.	See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program; if not, write to the Free Software
# Foundation, Inc., 59 Temple Place, Suite 330, Boston,
# MA 02111-1307 USA
#

include $(TOPDIR)/config.mk

LIB	:= $(obj)libperipheral.o

COBJS-$(CONFIG_ZX297520V3E_CPE)	+= peripheral.o
COBJS-$(CONFIG_ZX297520V3E_CPE_SWITCH)	+= peripheral_cpe_switch.o
COBJS-$(CONFIG_ZX297520V3E_EVB)	+= peripheral.o
COBJS-$(CONFIG_ZX297520V3E_FWP) += peripheral_fwp.o
COBJS-$(CONFIG_ZX297520V3E_MDL)	+= peripheral.o
COBJS-$(CONFIG_ZX297520V3E_MDL_MINI) += peripheral.o
COBJS-$(CONFIG_ZX297520V3E_MDL_MINI_64) += peripheral.o
COBJS-$(CONFIG_ZX297520V3E_MIFI) += peripheral_mifi.o
COBJS-$(CONFIG_ZX297520V3E_MIFI_MINI) += peripheral_mifi_mini.o
COBJS-$(CONFIG_ZX297520V3E_MIFI_MINI_XR819) += peripheral_mifi_mini_xr819.o
COBJS-$(CONFIG_ZX297520V3E_PHONE) += peripheral_phone.o
COBJS-$(CONFIG_ZX297520V3E_WATCH) += peripheral_watch.o
COBJS-$(CONFIG_ZX297520V3E_WATCH_CAP) += peripheral_watch.o
COBJS-$(CONFIG_ZX297520V3E_UFI_MINI)	+= peripheral.o
COBJS-$(CONFIG_ZX297520V3E_MDL_AB)	+= peripheral.o

COBJS	:= $(sort $(COBJS-y))
SRCS	:= $(COBJS:.o=.c)
OBJS	:= $(addprefix $(obj),$(COBJS))

all:	$(LIB)

$(LIB):	$(obj).depend $(OBJS)
	$(call cmd_link_o_target, $(OBJS))

#########################################################################

# defines $(obj).depend target
include $(SRCTREE)/rules.mk

sinclude $(obj).depend

#########################################################################
