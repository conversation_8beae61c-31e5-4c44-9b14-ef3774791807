#
# (C) Copyright 2000-2007
# <PERSON>, DENX Software Engineering, <EMAIL>.
#
# See file CREDITS for list of people who contributed to this
# project.
#
# This program is free software; you can redistribute it and/or
# modify it under the terms of the GNU General Public License as
# published by the Free Software Foundation; either version 2 of
# the License, or (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.	See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program; if not, write to the Free Software
# Foundation, Inc., 59 Temple Place, Suite 330, Boston,
# MA 02111-1307 USA
#

include $(TOPDIR)/config.mk
include config.mk

LIB	:= $(obj)libdrivers.lib

all:	$(LIB)

$(LIB):	$(DRIVERLIBS)
	$(call cmd_link_o_target, $(DRIVERLIBS))

$(DRIVERLIBS):
	$(MAKE) -C $(dir $(subst $(obj),,$@))

#########################################################################

# defines $(obj).depend target
include $(SRCTREE)/rules.mk

sinclude $(obj).depend

#########################################################################

release:
	@rm -rf $(DRIVERSRC)
